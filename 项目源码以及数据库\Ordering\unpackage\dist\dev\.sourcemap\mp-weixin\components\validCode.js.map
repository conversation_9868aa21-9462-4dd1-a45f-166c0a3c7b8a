{"version": 3, "sources": ["webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/validCode.vue?18c5", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/validCode.vue?ce40", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/validCode.vue?674b", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/validCode.vue?ae6e", "uni-app:///components/validCode.vue", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/validCode.vue?edf4", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/validCode.vue?cfa8"], "names": ["name", "props", "maxlength", "type", "default", "isPwd", "data", "codeIndex", "codeArr", "input", "onLoad", "_self", "methods", "getVal"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AAC+K;AAC/K,gBAAgB,wLAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3BA;AAAA;AAAA;AAAA;AAAqpB,CAAgB,0qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuEzqB;AAAA,eACA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAE;IACA;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;MACA;IACA,EACA;IACA;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACpHA;AAAA;AAAA;AAAA;AAAwuC,CAAgB,qsCAAG,EAAC,C;;;;;;;;;;;ACA5vC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/validCode.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./validCode.vue?vue&type=template&id=07846644&\"\nvar renderjs\nimport script from \"./validCode.vue?vue&type=script&lang=js&\"\nexport * from \"./validCode.vue?vue&type=script&lang=js&\"\nimport style0 from \"./validCode.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/validCode.vue\"\nexport default component.exports", "export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./validCode.vue?vue&type=template&id=07846644&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isPwd && _vm.codeArr.length >= 1\n  var g1 = _vm.isPwd && _vm.codeArr.length >= 2\n  var g2 = _vm.isPwd && _vm.codeArr.length >= 3\n  var g3 = _vm.isPwd && _vm.codeArr.length >= 4\n  var g4 = _vm.maxlength === 6 ? _vm.isPwd && _vm.codeArr.length >= 5 : null\n  var g5 = _vm.maxlength === 6 ? _vm.isPwd && _vm.codeArr.length >= 6 : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./validCode.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./validCode.vue?vue&type=script&lang=js&\"", "<!-- \r\n自定义验证码输入、密码输入使用\r\n \r\n使用方法：\r\nmaxlength：输入最大长度\r\nisPwd：是否是密码模式\r\n@finish：回调函数\r\n <validcode :maxlength=\"4\" :isPwd=\"false\" @finish=\"finish\"></validcode>\r\n -->\r\n<template>\r\n\t<view class=\"code-area\">\r\n\t\t<view class=\"flex-box\">\r\n\t\t\t<input\r\n\t\t\t\ttype=\"number\"\r\n\t\t\t\tfocus=\"true\"\r\n\t\t\t\t:maxlength=\"maxlength\"\r\n\t\t\t\tclass=\"hide-input\"\r\n\t\t\t\t@input=\"getVal\"\r\n\t\t\t\t\r\n\t\t\t/>\r\n\t\t\t<view v-bind:class=\"['item', { active: codeIndex == 1 }]\">\r\n\t\t\t\t<view class=\"line\"></view>\r\n\t\t\t\t<block v-if=\"isPwd && codeArr.length >= 1\">\r\n\t\t\t\t\t<text class=\"dot\">.</text>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-else>\t{{ codeArr[0] ? codeArr[0] : ''}}</block>\r\n\t\t\t</view>\r\n\t\t\t<view v-bind:class=\"['item', { active: codeIndex == 2 }]\">\r\n\t\t\t\t<view class=\"line\"></view>\r\n\t\t\t\t<block v-if=\"isPwd && codeArr.length >= 2\">\r\n\t\t\t\t\t<text class=\"dot\">.</text>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-else>\t{{ codeArr[1] ? codeArr[1] : ''}}</block>\r\n\t\t\t</view>\r\n\t\t\t<view v-bind:class=\"['item', { active: codeIndex == 3 }]\">\r\n\t\t\t\t<view class=\"line\"></view>\r\n\t\t\t\t<block v-if=\"isPwd && codeArr.length >= 3\">\r\n\t\t\t\t\t<text class=\"dot\">.</text>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-else>\t{{ codeArr[2] ? codeArr[2] : ''}}</block>\r\n\t\t\t</view>\r\n\t\t\t<view v-bind:class=\"['item', { active: codeIndex == 4 }]\">\r\n\t\t\t\t<view class=\"line\"></view>\r\n\t\t\t\t<block v-if=\"isPwd && codeArr.length >= 4\">\r\n\t\t\t\t\t<text class=\"dot\">.</text>\r\n\t\t\t\t</block>\r\n\t\t\t\t<block v-else>\t{{ codeArr[3] ? codeArr[3] : ''}}</block>\r\n\t\t\t</view>\r\n\t\t\t<block v-if=\"maxlength === 6\">\t\t\t\t\r\n\t\t\t\t<view v-bind:class=\"['item', { active: codeIndex == 5 }]\">\r\n\t\t\t\t\t<view class=\"line\"></view>\r\n\t\t\t\t\t<block v-if=\"isPwd && codeArr.length >= 5\">\r\n\t\t\t\t\t\t<text class=\"dot\">.</text>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-else>\t{{ codeArr[4] ? codeArr[4] : ''}}</block>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-bind:class=\"['item', { active: codeIndex == 6 }]\">\r\n\t\t\t\t\t<view class=\"line\"></view>\r\n\t\t\t\t\t<block v-if=\"isPwd && codeArr.length >= 6\">\r\n\t\t\t\t\t\t<text class=\"dot\">.</text>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-else>\t{{ codeArr[5] ? codeArr[5] : ''}}</block>\r\n\t\t\t\t</view>\r\n\t\t\t</block>\r\n\t\t\t\r\n\t\t\t\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tlet _self;\r\nexport default {\r\n\tname:'validCode',\r\n\tprops: {\r\n\t\t//最大长度 值为4或者6\r\n\t\tmaxlength: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 4\r\n\t\t},\r\n\t\t//是否是密码\r\n\t\tisPwd: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tcodeIndex: 1, //下标\r\n\t\t\tcodeArr: [],\r\n\t\t\tinput:'',\r\n\t\t};\r\n\t},\r\n\tonLoad(){\r\n\t\t_self = this;\r\n\t},\r\n\tmethods: {\r\n\t\t//取值\r\n\t\tgetVal(e) {\r\n\t\t\t//_self.input = e;\r\n\t\t\tlet { value } = e.detail;\r\n\t\t\t// console.log('验证码:', value);\r\n\t\t\tlet arr = value.split('');\r\n\t\t\tthis.codeIndex = arr.length + 1;\r\n\t\t\tthis.codeArr = arr;\r\n\t\t\t// console.log(this.codeIndex, this.pwdArr);\r\n\t\t\tif (this.codeIndex > Number(this.maxlength)) {\r\n\t\t\t\t//输入完成\r\n\t\t\t\tthis.$emit('finish',this.codeArr.join(''));\r\n\t\t\t}\r\n\t\t},\r\n\t\t// //清空输入框\r\n\t\t// clear(){\r\n\t\t// \t_self.input.value='';\r\n\t\t// }\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.code-area {\r\n\ttext-align: center;\r\n\t.flex-box {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tposition: relative;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.item {\r\n\t\tposition: relative;\r\n\t\twidth: 60upx;\r\n\t\theight: 60upx;\r\n\t\tfont-size: 15upx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t\tline-height: 60upx;\r\n\t\tbox-sizing: border-box;\r\n\t\tborder: 2upx solid #cccccc;\r\n\t}\r\n\r\n\t.item:last-child {\r\n\t\tmargin-right: 0;\r\n\t}\r\n\r\n\t.active {\r\n\t\tborder-color: #ff4b4b;\r\n\t}\r\n\t.active .line {\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.line {\r\n\t\tdisplay: none;\r\n\t\tposition: absolute;\r\n\t\tleft: 50%;\r\n\t\ttop: 50%;\r\n\t\ttransform: translate(-50%, -50%);\r\n\t\twidth: 2upx;\r\n\t\theight: 40upx;\r\n\t\tbackground: #ff4b4b;\r\n\t\tanimation: twinkling 1s infinite ease;\r\n\t}\r\n\r\n\t.hide-input {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: -100%;\r\n\t\twidth: 200%;\r\n\t\theight: 100%;\r\n\t\ttext-align: left;\r\n\t\tz-index: 9;\r\n\t\topacity: 1;\r\n\t}\r\n\r\n\t@keyframes twinkling {\r\n\t\t0% {\r\n\t\t\topacity: 0.2;\r\n\t\t}\r\n\t\t50% {\r\n\t\t\topacity: 0.5;\r\n\t\t}\r\n\t\t100% {\r\n\t\t\topacity: 0.2;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.dot{\r\n\t\tfont-size: 80upx;\r\n\t\tline-height: 40upx;\r\n\t}\r\n}\r\n</style>\r\n", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./validCode.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./validCode.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749718152790\n      var cssReload = require(\"E:/software/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}