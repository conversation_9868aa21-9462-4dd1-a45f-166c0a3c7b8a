<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaa.mapper.FoodMassageMapper">
    <select id="selType" resultType="Type">
        select * from types where t_state = 0
    </select>
    <insert id="foodAdd" parameterType="Menus">
        insert into menus(m_name,m_img,m_price,m_state,t_id)
        value (#{m_name},#{m_img},#{m_price},#{m_state},#{t_id})
    </insert>
    <select id="foodSel" parameterType="Map" resultType="MenusDB">
        SELECT m.m_id,m.m_name,m.m_img,m.m_price,m.m_state,t.t_name from menus m
        LEFT JOIN types t ON t.t_id = m.t_id where m.m_state=#{m_state}
        <if test="m_name!='' and m_name!=null">
           and m.m_name like '%${m_name}%'
        </if>
        <if test="t_id!='' and t_id!=null">
            and m.t_id = #{t_id}
        </if>
        order by m.m_id desc limit #{start},#{limit}
    </select>
    <update id="foodUpd" parameterType="Map">
        update menus set m_name=#{m_name},m_price=#{m_price},m_img=#{m_img},t_id=#{t_id} where m_id=#{m_id}
    </update>
    <update id="stateUpd" parameterType="Map">
        update menus set m_state=#{m_state} where m_id=#{m_id}
    </update>
    <select id="countAll" parameterType="Map" resultType="int">
        select count(*) from menus where m_state=#{m_state} 
        <if test="m_name!='' and m_name!=null">
            and  m_name like '%${m_name}%'
        </if>
        <if test="t_id!='' and t_id!=null">
            and t_id = #{t_id}
        </if>
    </select>
    <select id="selName" parameterType="String" resultType="int">
        select count(m_id) from menus where m_name=#{m_name}
    </select>
</mapper>