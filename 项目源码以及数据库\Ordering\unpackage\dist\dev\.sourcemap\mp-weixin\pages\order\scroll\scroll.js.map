{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/order/scroll/scroll.vue?6c64", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/order/scroll/scroll.vue?8c22", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/order/scroll/scroll.vue?3de4", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/order/scroll/scroll.vue?4406", "uni-app:///pages/order/scroll/scroll.vue", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/order/scroll/scroll.vue?bf78", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/order/scroll/scroll.vue?4f4c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "scroll", "data", "upload", "requesting", "end", "emptyShow", "page", "listData", "o_id", "listCount", "http", "raise", "onLoad", "_self", "methods", "evaluate", "uni", "url", "getEvaluate", "success", "getList", "setTimeout", "refresh", "req", "testData"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AAC+K;AAC/K,gBAAgB,wLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAkpB,CAAgB,uqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4CtqB;AACA;AACA;AACA;AAEA;AACA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;IAAAC;EAAA;EACAC;IACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACAC;IACA;IACAA;IACA;IACAA;IACA;IACAA;IACA;IACA;IACA;IACA;EAEA;EACAC;IACA;IACAC;MACAC;QACAC;MACA;IACA;IACA;IACAC;MACAF;QACAC;QAAA;QACAhB;UACAO;QACA;QACAW;UACAN;QACA;MACA;IACA;IACA;IACAO;MAAA;MACA;MACA1B;MACA2B;QACA;QACA;QACA3B;QACA;UACA;UACA;QACA,OACA;UACA;UACA;QACA;QACA;QACA;QACA;QACA;QACA;UACA;QACA;;QACA;MACA;IACA;IACA;IACA4B;MACA;MACAT;MACA;MACA;IACA;IACA;IACAU;MACAP;QACAC;QACAhB;UACAO;QACA;QACAW;UACAK;QACA;MACA;IACA,EACA;IACA;IACA;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChKA;AAAA;AAAA;AAAA;AAAquC,CAAgB,ksCAAG,EAAC,C;;;;;;;;;;;ACAzvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/order/scroll/scroll.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/order/scroll/scroll.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./scroll.vue?vue&type=template&id=48b47565&\"\nvar renderjs\nimport script from \"./scroll.vue?vue&type=script&lang=js&\"\nexport * from \"./scroll.vue?vue&type=script&lang=js&\"\nimport style0 from \"./scroll.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/order/scroll/scroll.vue\"\nexport default component.exports", "export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./scroll.vue?vue&type=template&id=48b47565&\"", "var components\ntry {\n  components = {\n    scroll: function () {\n      return import(\n        /* webpackChunkName: \"components/scroll/scroll\" */ \"@/components/scroll/scroll.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./scroll.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./scroll.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<!-- requesting-加载中  emptyShow-控制空状态的显示   end-加载完毕  refreshSize-头部下拉是的高度\r\n\t\t @refresh -下拉刷新时间 raise-是否显示评论按钮\r\n\r\n\t\t-->\r\n\t\t<scroll id=\"scroll\"\r\n\r\n\t\t\t\t:requesting=\"requesting\"\r\n\t\t\t\t:empty-show=\"emptyShow\"\r\n\t\t\t\t:end=\"end\"\r\n\t\t\t\t:listCount=\"listCount\"\r\n\t\t\t\t@refresh=\"refresh\"\r\n\t\t\t\trefreshSize=\"200\"\r\n\t\t\t\t@evaluate=\"evaluate\"\r\n\t\t\t\t:raise=\"raise\"\r\n\t\t\t\t>\r\n\t\t\t<view class=\"cells\">\r\n\t\t\t\t<view class=\"cell\"\r\n\t\t\t\t\tv-for=\"(item, index) in listData\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"cell__hd\">\r\n\t\t\t\t\t\t<image mode=\"aspectFill\" :src=\"http+upload+item.m_img\" :alt=\"item.m_name\"/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"cell__bd\">\r\n\t\t\t\t\t\t<view class=\"name\">{{item.m_name}}</view>\r\n\t\t\t\t\t\t<view class=\"des\">数量:{{item.l_count}}</view>\r\n\t\t\t\t\t\t<view class=\"name\">单价:{{item.l_price}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view  class=\"cell__bd\">\r\n\t\t\t\t\t\t<view class=\"zong\">总价:{{item.l_sum}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view  class=\"cell__bd\">\r\n\t\t\t\t\t\t<view class=\"zong\" v-if=\"item.l_state == 0\">待上菜</view>\r\n\t\t\t\t\t\t<view class=\"zong\" v-else>交易完成</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</scroll>\r\n\t\t<!-- <view></view> -->\r\n\t\t<!-- <button id=\"button_evaluate\">对本次服务进行评价</button> -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tlet pageStart = 0;\r\n\tlet pageSize = 15;\r\n\t//当前页面\r\n\tlet _self;\r\n\r\n\tlet testData = [];//\r\n\t//插件模块的导入\r\n\timport scroll from \"../../../components/scroll/scroll.vue\";\r\n\texport default {\r\n\t\tcomponents: { scroll },\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tupload:'upload/',\r\n\t\t\t\t// 加载中\r\n\t\t\t\trequesting: false,\r\n\t\t\t\t// 加载完毕\r\n\t\t\t\tend: false,\r\n\t\t\t\t// 控制空状态的显示\r\n\t\t\t\temptyShow: false,\r\n\t\t\t\t//页面\r\n\t\t\t\tpage: pageStart,\r\n\t\t\t\tlistData: [],\r\n\t\t\t\t//订单的id\r\n\t\t\t\to_id:0,\r\n\t\t\t\t//当前列表长度\r\n\t\t\t\tlistCount: 0,\r\n\t\t\t\t//请求路径\r\n\t\t\t\thttp: '',\r\n\t\t\t\t//是否显示评价\r\n\t\t\t\traise:false\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad(option){\r\n\t\t\t_self = this;\r\n\t\t\t//加载时获取总配置的请求路径\r\n\t\t\t_self.http = getApp().globalData.http;\r\n\t\t\t//获取订单id\r\n\t\t\t_self.o_id = option.u_id;\r\n\t\t\t//判断是否显示按钮\r\n\t\t\t_self.getEvaluate();\r\n\t\t\t//下拉刷新\r\n\t\t\tthis.req();\r\n\t\t\t//请求后台数据\r\n\t\t\tthis.getList('refresh', pageStart);\r\n\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t//跳转服务评论\r\n\t\t\tevaluate(){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t    url: './publish/publish?o_id='+_self.o_id\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//判断是否显示按钮\r\n\t\t\tgetEvaluate(){\r\n\t\t\t\tuni.request({\r\n\t\t\t\t    url: this.http+'zyg/evaluate', //仅为示例，并非真实接口地址。\r\n\t\t\t\t    data: {\r\n\t\t\t\t        o_id: _self.o_id\r\n\t\t\t\t    },\r\n\t\t\t\t    success: (res) => {\r\n\t\t\t\t\t\t_self.raise = res.data;\r\n\t\t\t\t    }\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//这是显示调用的函数\r\n\t\t\tgetList(type, currentPage) {\r\n\t\t\t\tthis.requesting = true;\r\n\t\t\t\twx.showNavigationBarLoading();\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.requesting = false;\r\n\t\t\t\t\tthis.end = false;\r\n\t\t\t\t\twx.hideNavigationBarLoading();\r\n\t\t\t\t\tif (type === 'refresh') {\r\n\t\t\t\t\t\tthis.listData = testData;\r\n\t\t\t\t\t\tthis.page = currentPage + 1;\r\n\t\t\t\t\t}\r\n\t\t\t\t\telse {\r\n\t\t\t\t\t\tthis.listData = [...this.listData, ...testData];\r\n\t\t\t\t\t\tthis.page = currentPage + 1;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 没有数据的时候这样设置\r\n\t\t\t\t\t// this.listData = [];\r\n\t\t\t\t\t// this.emptyShow = true;\r\n\t\t\t\t\tthis.end = true;\r\n\t\t\t\t\tif(this.page > 6){\r\n\t\t\t\t\t\tthis.end = true; //没有更多数据\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.listCount = this.listData.length; //列表条数\r\n\t\t\t\t}, 1000);\r\n\t\t\t},\r\n\t\t\t//下啦刷新\r\n\t\t\trefresh(){\r\n\t\t\t\tthis.req();\r\n\t\t\t\t_self.getEvaluate();\r\n\t\t\t\tthis.getList('refresh', pageStart);\r\n\t\t\t\tthis.empty = false;\r\n\t\t\t},\r\n\t\t\t//下拉刷新\r\n\t\t\treq(){\r\n\t\t\t\tuni.request({\r\n\t\t\t\t\turl : this.http+\"/zyg/details\",\r\n\t\t\t\t\t data: {\r\n\t\t\t\t\t        o_id: _self.o_id\r\n\t\t\t\t\t},\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t       testData = res.data;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\t//上滑加载更多\r\n\t\t\t// more(){\r\n\t\t\t// \tthis.getList('more', this.page);\r\n\t\t\t// \tconsole.log(2);\r\n\t\t\t// }\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t// #button_evaluate{\r\n\t// \tposition: fixed;\r\n\t// \tz-index: 999;\r\n\t// \ttop: 500px;\r\n\t// \twidth: 98%;\r\n\t// \tmargin: 0px auto;\r\n\t// }\r\n\t.zong{\r\n\t\theight: 100%;;\r\n\t\tdisplay: flex;\r\n\t\talign-items:center;\r\n\t}\r\n.title {\r\n  position: fixed;\r\n  left: 0;\r\n  top: 0;\r\n  width: 100%;\r\n  background-color: #ffffff;\r\n  z-index: 99;\r\n  line-height: 90rpx;\r\n  text-align: center;\r\n  font-weight: bold;\r\n  box-shadow: 0 0 20rpx -5rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.cells {\r\n\tbackground-color: #fff;\r\n  margin-top: 20rpx;\r\n}\r\n\r\n.cell {\r\n  display: flex;\r\n  padding: 20rpx;\r\n}\r\n.cell:not(:last-child) {\r\n  border-bottom: 1rpx solid #ebedf0;\r\n}\r\n.cell__hd {\r\n  font-size: 0;\r\n}\r\n.cell__hd image {\r\n  width: 160rpx;\r\n  height: 160rpx;\r\n  margin-right: 20rpx;\r\n  border-radius: 12rpx;\r\n}\r\n.cell__bd {\r\n\t// display: flex;\r\n  flex: 1;\r\n}\r\n.cell__bd .name {\r\n  display: -webkit-box;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-line-clamp: 2;\r\n  overflow: hidden;\r\n  word-break: break-all;\r\n  font-size: 28rpx;\r\n  margin-bottom: 12rpx;\r\n}\r\n.cell__bd .des {\r\n  display: -webkit-box;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-line-clamp: 2;\r\n  overflow: hidden;\r\n  word-break: break-all;\r\n  color: #666666;\r\n  font-size: 24rpx;\r\n}\r\n\r\n.control-panel {\r\n  border-top: 1rpx solid #ebedf0;\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 12213;\r\n  background: #ffffff;\r\n  padding: 20rpx;\r\n  box-sizing: border-box;\r\n}\r\n.control-panel.isX {\r\n  padding-bottom: 60rpx;\r\n}\r\n\r\n.panel-item {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.panel-item__bd {\r\n  flex: 1;\r\n}\r\n\r\n\r\n</style>\r\n", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./scroll.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./scroll.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1748248725953\n      var cssReload = require(\"E:/software/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}