<view class="uni-numbox"><view data-event-opts="{{[['tap',[['_calcValue',['minus']]]]]}}" class="{{['uni-numbox__minus',(inputValue<=min||disabled)?'uni-numbox--disabled':'']}}" bindtap="__e">-</view><view class="uni-numbox__value" disabled="{{disabled}}" type="number" adjust-position="false" value="{{inputValue}}" data-event-opts="{{[['tap',[['ifShow',['$0'],['inputValue']]]],['input',[['__set_model',['','inputValue','$event',[]]]]]]}}" bindtap="__e" bindinput="__e">{{inputValue}}</view><view data-event-opts="{{[['tap',[['_calcValue',['plus']]]]]}}" class="{{['uni-numbox__plus',(inputValue>=max||disabled)?'uni-numbox--disabled':'']}}" bindtap="__e">+</view><block wx:if="{{showHide}}"><view class="modelBox"><view data-event-opts="{{[['tap',[['modelHide',['$event']]]]]}}" class="shade" bindtap="__e"></view><view class="model"><view class="modelTitle">请输入您的内容</view><view class="modelInput"><input placeholder-class="inputStyle" type="number" focus data-event-opts="{{[['input',[['__set_model',['','modelValue','$event',[]]]]]]}}" value="{{modelValue}}" bindinput="__e"/></view><view class="modeBtnBox"><view data-event-opts="{{[['tap',[['modelHide',['$event']]]]]}}" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['confirm',['$event']]]]]}}" bindtap="__e">确定</view></view></view></view></block></view>