import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.UUID;

public class CreateTestUser {
    public static void main(String[] args) {
        // 数据库连接信息
        String url = "******************************************************************************************************************************";
        String username = "root";
        String password = "3132161409";
        
        System.out.println("=== 创建测试用户 ===");
        
        Connection conn = null;
        try {
            // 连接数据库
            Class.forName("com.mysql.cj.jdbc.Driver");
            conn = DriverManager.getConnection(url, username, password);
            System.out.println("✅ 数据库连接成功");
            
            // 生成测试用户数据
            String userId = "test-" + UUID.randomUUID().toString().substring(0, 8);
            String usercode = "test";
            String userName = "测试用户";
            String userPassword = "123456";  // 明文密码
            String salt = "testsalt";  // 简单的盐值
            
            // 使用简单的MD5加密（密码+盐值）
            String hashedPassword = getMD5(userPassword + salt);
            
            System.out.println("生成的用户信息:");
            System.out.println("  用户ID: " + userId);
            System.out.println("  用户名: " + usercode);
            System.out.println("  显示名: " + userName);
            System.out.println("  明文密码: " + userPassword);
            System.out.println("  盐值: " + salt);
            System.out.println("  加密密码: " + hashedPassword);
            
            // 检查用户是否已存在
            String checkSql = "SELECT COUNT(*) FROM sys_user WHERE usercode = ?";
            PreparedStatement checkStmt = conn.prepareStatement(checkSql);
            checkStmt.setString(1, usercode);
            ResultSet rs = checkStmt.executeQuery();
            rs.next();
            int count = rs.getInt(1);
            
            if (count > 0) {
                // 更新现有用户
                String updateSql = "UPDATE sys_user SET username=?, password=?, salt=?, locked='0' WHERE usercode=?";
                PreparedStatement updateStmt = conn.prepareStatement(updateSql);
                updateStmt.setString(1, userName);
                updateStmt.setString(2, hashedPassword);
                updateStmt.setString(3, salt);
                updateStmt.setString(4, usercode);
                
                int updated = updateStmt.executeUpdate();
                if (updated > 0) {
                    System.out.println("✅ 测试用户已更新");
                }
                updateStmt.close();
            } else {
                // 插入新用户
                String insertSql = "INSERT INTO sys_user (id, usercode, username, password, salt, locked) VALUES (?, ?, ?, ?, ?, '0')";
                PreparedStatement insertStmt = conn.prepareStatement(insertSql);
                insertStmt.setString(1, userId);
                insertStmt.setString(2, usercode);
                insertStmt.setString(3, userName);
                insertStmt.setString(4, hashedPassword);
                insertStmt.setString(5, salt);
                
                int inserted = insertStmt.executeUpdate();
                if (inserted > 0) {
                    System.out.println("✅ 测试用户已创建");
                }
                insertStmt.close();
            }
            
            checkStmt.close();
            rs.close();
            
            System.out.println("\n=== 测试登录信息 ===");
            System.out.println("用户名: test");
            System.out.println("密码: 123456");
            System.out.println("请使用以上信息登录系统");
            
        } catch (Exception e) {
            System.out.println("❌ 错误: " + e.getMessage());
            e.printStackTrace();
        } finally {
            try {
                if (conn != null && !conn.isClosed()) {
                    conn.close();
                    System.out.println("✅ 数据库连接已关闭");
                }
            } catch (Exception e) {
                System.out.println("❌ 关闭连接时出错: " + e.getMessage());
            }
        }
    }
    
    // 简单的MD5加密方法
    public static String getMD5(String input) {
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(input.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b & 0xff));
            }
            return sb.toString();
        } catch (Exception e) {
            return "";
        }
    }
}
