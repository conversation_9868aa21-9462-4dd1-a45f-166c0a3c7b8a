<template>
    <view class="page">
        <view class="feedback-body" >
            <textarea placeholder="您想对我们说些什么..." style="border-bottom:1px solid   #F1F1F3;" v-model="content" class="feedback-textare" />
        </view>
        <!-- <choose :count="count"  :imgList="imgList"  @changes="fileChange"></choose>
        <compress  ref="compress" :maxwh="maxwh" :quality="quality" > </compress>


        <view class="swiper-list">
            <view class="uni-list-cell uni-list-cell-pd feedback-title">
                <view class="uni-list-cell-db ">图片是否压缩</view>
                <switch :checked="isYasuo" @change="changeIndicatorDots" />
            </view>
        </view>
        <view class='feedback-title'>
            <text>QQ/邮箱</text>
        </view>
        <view class="feedback-body">
            <input class="feedback-input" v-model="sendDate.contact" placeholder="(选填,方便我们联系你 )" />
        </view> -->
        <view class='feedback-title feedback-star-view'>
            <text>服务评分</text>
            <view class="feedback-star-view">
                <text class="feedback-star" v-for="(value,key) in stars" :key="key" :class="key < serveDate.score ? 'active' : ''" @tap="serve(value)"></text>
            </view>
        </view>
		<view class='feedback-title feedback-star-view'>
		    <text>菜品评分</text>
		    <view class="feedback-star-view">
		        <text class="feedback-star" v-for="(value,key) in stars" :key="key" :class="key < menuDate.score ? 'active' : ''" @tap="menu(value)"></text>
		    </view>
		</view>
		<view class='feedback-title feedback-star-view'>
		    <text>环境评分</text>
		    <view class="feedback-star-view">
		        <text class="feedback-star" v-for="(value,key) in stars" :key="key" :class="key < environmentDate.score ? 'active' : ''" @tap="environment(value)"></text>
		    </view>
		</view>
        <button type="default" class="feedback-submit" @tap="send">提交</button>

    </view>
</template>

<script>
    import choose from "@/components/template/image/choose.vue"
    import compress from "@/components/template/image/compress.vue"
	let _self;
    export default {
        name:'newsPublish',
        components:{
        	choose,
            compress
        },
        data() {
            return {
				o_id:0,
				http:'',
				
                isYasuo:true,
                count:6,
                maxwh:280,
                quality:1,
                msgContents: ["界面显示错乱", "启动缓慢，卡出翔了", "UI无法直视，丑哭了", "偶发性崩溃"],
                stars: [1, 2, 3, 4, 5],
                imgList: [],
				content:'',
                sendDate: {
                    score: 0,
                    //content: "",
                    contact: ""
                },
				//服务
				serveDate: {
					score: 0,
					//content: "",
					contact: ""
				},
				//菜品
				menuDate:{
					score: 0,
					//content: "",
					contact: ""
				},
				//环境
				environmentDate:{
					score: 0,
					//content: "",
					contact: ""
				}
				
            }
        },
        onLoad(option) {
			_self = this;
			_self.o_id = option.o_id;
			_self.http = getApp().globalData.http;
        },
        methods: {
            compressImg(e){
              console.log(e)  
            },
            changeIndicatorDots(e){
            this.isYasuo = !this.isYasuo
            },
            fileChange(e){
              this.imgList=e;
            },
			//服务
			serve(e){
				this.serveDate.score = e;
			},
			//菜品
			menu(e){
				this.menuDate.score = e;
			},
			//环境
			environment(e){
				this.environmentDate.score = e;
			},
            chooseStar(e) { //点击评星
                this.sendDate.score = e;
            },
            send() { //发送提交
				//·console.log(1);
				if(_self.content != '' &&  _self.content != null){
					if(_self.serveDate.score !=0 && _self.menuDate.score !=0 && _self.environmentDate.score !=0){
						uni.request({
						    url: _self.http+'zyg/addComment', //仅为示例，并非真实接口地址。
						    data: {
						        o_id: _self.o_id,
								content : _self.content,
								serve:  _self.serveDate.score,
								menu: _self.menuDate.score,
								environment: _self.environmentDate.score
						    },
						    success: (res) => {
						        // console.log(res.data);
								if(Number(res.data) === 1){
									uni.showToast({
									    title: '评论成功',
									    duration: 2000
									});
									setTimeout(()=>{
										uni.redirectTo({
										    url: '../../order'
										});
									},2000);
								}
						    }
						});
					}else{
						uni.showToast({
						    title: '请对我们！',
						    duration: 2000
						});
					}
				}else{
					uni.showToast({
					    title: '请对我们说些什么！',
					    duration: 2000
					});
				}
				
            }
        }
    }
</script>

<style>
    page {
        background-color: #EFEFF4;
    }

    .input-view {
        font-size: 28upx;
    }
    .close-view{
        text-align: center;line-height:14px;height: 16px;width: 16px;border-radius: 50%;background: #FF5053;color: #FFFFFF;position: absolute;top: -6px;right: -4px;font-size: 12px;
    }
</style>
