<view data-event-opts="{{[['tap',[['$_click',['$event']]]]]}}" class="{{['cmd-avatar',setShapeSizeClass]}}" style="{{(setIconTextStyle+setNumSizeStyle)}}" bindtap="__e"><block wx:if="{{src}}"><image class="cmd-avatar-img" mode="aspectFit" src="{{src}}" data-event-opts="{{[['load',[['$_imageLoad',['$event']]]],['error',[['$_imageError',['$event']]]]]}}" bindload="__e" binderror="__e"></image></block><block wx:if="{{icon&&!src&&!text}}"><cmd-icon vue-id="37565d42-1" type="{{icon}}" size="{{setIconSize}}" color="{{make.color}}" bind:__l="__l"></cmd-icon></block><block wx:if="{{text&&!src&&!icon}}"><text>{{text}}</text></block></view>