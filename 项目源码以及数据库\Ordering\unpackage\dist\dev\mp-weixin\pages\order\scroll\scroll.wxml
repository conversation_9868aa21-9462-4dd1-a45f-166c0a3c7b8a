<view><scroll vue-id="644a8f7e-1" id="scroll" requesting="{{requesting}}" empty-show="{{emptyShow}}" end="{{end}}" listCount="{{listCount}}" refreshSize="200" raise="{{raise}}" data-event-opts="{{[['^refresh',[['refresh']]],['^evaluate',[['evaluate']]]]}}" bind:refresh="__e" bind:evaluate="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="cells"><block wx:for="{{listData}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="cell"><view class="cell__hd"><image mode="aspectFill" src="{{http+upload+item.m_img}}" alt="{{item.m_name}}"></image></view><view class="cell__bd"><view class="name">{{item.m_name}}</view><view class="des">{{"数量:"+item.l_count}}</view><view class="name">{{"单价:"+item.l_price}}</view></view><view class="cell__bd"><view class="zong">{{"总价:"+item.l_sum}}</view></view><view class="cell__bd"><block wx:if="{{item.l_state==0}}"><view class="zong">待上菜</view></block><block wx:else><view class="zong">交易完成</view></block></view></view></block></view></scroll></view>