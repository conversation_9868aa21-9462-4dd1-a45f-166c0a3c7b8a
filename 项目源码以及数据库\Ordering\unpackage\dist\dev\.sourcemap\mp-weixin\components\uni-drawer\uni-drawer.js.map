{"version": 3, "sources": ["webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/uni-drawer/uni-drawer.vue?56f9", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/uni-drawer/uni-drawer.vue?75e1", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/uni-drawer/uni-drawer.vue?9841", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/uni-drawer/uni-drawer.vue?0b45", "uni-app:///components/uni-drawer/uni-drawer.vue", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/uni-drawer/uni-drawer.vue?87e2", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/uni-drawer/uni-drawer.vue?b23c"], "names": ["name", "props", "visible", "type", "default", "mode", "mask", "data", "visibleSync", "showDrawer", "rightMode", "closeTimer", "watchTimer", "watch", "clearTimeout", "setTimeout", "created", "methods", "close", "moveHandle"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AAC+K;AAC/K,gBAAgB,wLAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAspB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;eCU1qB;EACAA;EACAC;IACA;AACA;AACA;IACAC;MACAC;MACAC;IACA;IACA;AACA;AACA;IACAC;MACAF;MACAC;IACA;IACA;AACA;AACA;IACAE;MACAH;MACAC;IACA;EACA;EACAG;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAX;MAAA;MACAY;MACAC;QACA;MACA;MACA;QACAD;MACA;MACA;QACA;MACA;QACA;UACA;QACA;MACA;IACA;EACA;EACAE;IAAA;IACA;IACAD;MACA;IACA;IACA;EACA;EACAE;IACAC;MAAA;MACA;MACA;QACA;QACA;MACA;IACA;IACAC;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC/EA;AAAA;AAAA;AAAA;AAA47B,CAAgB,s7BAAG,EAAC,C;;;;;;;;;;;ACAh9B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/uni-drawer/uni-drawer.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-drawer.vue?vue&type=template&id=56836304&\"\nvar renderjs\nimport script from \"./uni-drawer.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-drawer.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-drawer.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uni-drawer/uni-drawer.vue\"\nexport default component.exports", "export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-drawer.vue?vue&type=template&id=56836304&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-drawer.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-drawer.vue?vue&type=script&lang=js&\"", "<template>\n\t<view v-if=\"visibleSync\" :class=\"{ 'uni-drawer--visible': showDrawer, 'uni-drawer--right': rightMode }\" class=\"uni-drawer\" @touchmove.stop.prevent=\"moveHandle\">\n\t\t<view class=\"uni-drawer__mask\" @tap=\"close\" />\n\t\t<view class=\"uni-drawer__content\">\n\t\t\t<slot />\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tname: 'UniDrawer',\n\t\tprops: {\n\t\t\t/**\n\t\t\t * 显示状态\n\t\t\t */\n\t\t\tvisible: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\t/**\n\t\t\t * 显示模式（左、右），只在初始化生效\n\t\t\t */\n\t\t\tmode: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\t/**\n\t\t\t * 蒙层显示状态\n\t\t\t */\n\t\t\tmask: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tvisibleSync: false,\n\t\t\t\tshowDrawer: false,\n\t\t\t\trightMode: false,\n\t\t\t\tcloseTimer: null,\n\t\t\t\twatchTimer: null\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tvisible(val) {\n\t\t\t\tclearTimeout(this.watchTimer)\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.showDrawer = val\n\t\t\t\t}, 100)\n\t\t\t\tif (this.visibleSync) {\n\t\t\t\t\tclearTimeout(this.closeTimer)\n\t\t\t\t}\n\t\t\t\tif (val) {\n\t\t\t\t\tthis.visibleSync = val\n\t\t\t\t} else {\n\t\t\t\t\tthis.watchTimer = setTimeout(() => {\n\t\t\t\t\t\tthis.visibleSync = val\n\t\t\t\t\t}, 300)\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\tthis.visibleSync = this.visible\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.showDrawer = this.visible\n\t\t\t}, 100)\n\t\t\tthis.rightMode = this.mode === 'right'\n\t\t},\n\t\tmethods: {\n\t\t\tclose() {\n\t\t\t\tthis.showDrawer = false\n\t\t\t\tthis.closeTimer = setTimeout(() => {\n\t\t\t\t\tthis.visibleSync = false\n\t\t\t\t\tthis.$emit('close')\n\t\t\t\t}, 200)\n\t\t\t},\n\t\t\tmoveHandle() {}\n\t\t}\n\t}\n</script>\n\n<style>\n\t@charset \"UTF-8\";\n\n\t.uni-drawer {\n\t\tdisplay: block;\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\toverflow: hidden;\n\t\tvisibility: hidden;\n\t\tz-index: 999;\n\t\theight: 100%\n\t}\n\n\t.uni-drawer.uni-drawer--right .uni-drawer__content {\n\t\tleft: auto;\n\t\tright: 0;\n\t\ttransform: translatex(100%)\n\t}\n\n\t.uni-drawer.uni-drawer--visible {\n\t\tvisibility: visible\n\t}\n\n\t.uni-drawer.uni-drawer--visible .uni-drawer__content {\n\t\ttransform: translatex(0)\n\t}\n\n\t.uni-drawer.uni-drawer--visible .uni-drawer__mask {\n\t\tdisplay: block;\n\t\topacity: 1\n\t}\n\n\t.uni-drawer__mask {\n\t\tdisplay: block;\n\t\topacity: 0;\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground: rgba(0, 0, 0, .4);\n\t\ttransition: opacity .3s\n\t}\n\n\t.uni-drawer__content {\n\t\tdisplay: block;\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 61.8%;\n\t\theight: 100%;\n\t\tbackground: #fff;\n\t\ttransition: all .3s ease-out;\n\t\ttransform: translatex(-100%)\n\t}\n</style>", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-drawer.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-drawer.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749718152591\n      var cssReload = require(\"E:/software/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}