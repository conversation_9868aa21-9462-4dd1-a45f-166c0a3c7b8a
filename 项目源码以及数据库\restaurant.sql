/*
 Navicat Premium Data Transfer

 Source Server         : zkq
 Source Server Type    : MySQL
 Source Server Version : 80012
 Source Host           : ************:3306
 Source Schema         : restaurant

 Target Server Type    : MySQL
 Target Server Version : 80012
 File Encoding         : 65001

 Date: 28/11/2019 08:35:48
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for comments
-- ----------------------------
DROP TABLE IF EXISTS `comments`;
CREATE TABLE `comments`  (
  `c_id` int(11) NOT NULL AUTO_INCREMENT,
  `o_id` int(11) NOT NULL,
  `c_content` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `c_serve` int(11) NULL DEFAULT NULL,
  `c_menu` int(11) NULL DEFAULT NULL,
  `c_environment` int(11) NULL DEFAULT NULL,
  PRIMAR<PERSON> KEY (`c_id`) USING BTREE,
  INDEX `o_id`(`o_id`) USING BTREE,
  CONSTRAINT `comments_ibfk_1` FOREIGN KEY (`o_id`) REFERENCES `orders` (`o_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of comments
-- ----------------------------
INSERT INTO `comments` VALUES (1, 6, '还可以！', 5, 4, 3);
INSERT INTO `comments` VALUES (2, 7, '', 4, 3, 4);
INSERT INTO `comments` VALUES (3, 8, '凑合着吧', 4, 4, 4);
INSERT INTO `comments` VALUES (4, 8, '凑合着吧', 4, 4, 4);
INSERT INTO `comments` VALUES (5, 11, '给你们点个赞！666', 5, 5, 5);
INSERT INTO `comments` VALUES (6, 13, '挺好吃，下次还来！', 4, 5, 3);
INSERT INTO `comments` VALUES (7, 16, '饭菜便宜点', 5, 5, 5);

-- ----------------------------
-- Table structure for menus
-- ----------------------------
DROP TABLE IF EXISTS `menus`;
CREATE TABLE `menus`  (
  `m_id` int(11) NOT NULL AUTO_INCREMENT,
  `m_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `m_img` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `m_price` decimal(5, 2) NULL DEFAULT NULL,
  `m_state` int(11) NULL DEFAULT NULL,
  `t_id` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`m_id`) USING BTREE,
  INDEX `t_id`(`t_id`) USING BTREE,
  CONSTRAINT `menus_ibfk_1` FOREIGN KEY (`t_id`) REFERENCES `types` (`t_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 32 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of menus
-- ----------------------------
INSERT INTO `menus` VALUES (1, '红烧三角豆腐', 'img/a13922176_157173184643842.jpg', 10.00, 0, 1);
INSERT INTO `menus` VALUES (2, '三宝烩牛', 'img/a5469756_148116584621597.jpg', 11.00, 1, 4);
INSERT INTO `menus` VALUES (3, '红烧猪大骨', 'img/a2921467_157170766971014.jpg', 12.00, 0, 1);
INSERT INTO `menus` VALUES (4, '醋溜娃娃菜', 'img/a3273609_157170540264658.jpg', 13.00, 0, 1);
INSERT INTO `menus` VALUES (5, '酱油炒饭', 'img/a69523_13997.jpg', 14.00, 0, 2);
INSERT INTO `menus` VALUES (6, '鲜虾面', 'img/2019102215717161599469724956.jpg', 15.00, 0, 2);
INSERT INTO `menus` VALUES (7, '鸡肉馄饨', 'img/2019102215717093663209724956.jpg', 16.00, 0, 2);
INSERT INTO `menus` VALUES (8, '老北京肉丁炸酱面', 'img/2019102115716633219379702111.jpg', 17.00, 0, 2);
INSERT INTO `menus` VALUES (9, '羊排意大利面', 'img/2019102115716353962629702111.jpg', 18.00, 0, 2);
INSERT INTO `menus` VALUES (10, '虾仁青椒蛋炒饭', 'img/2019102015715507521389724956.jpg', 19.00, 0, 2);
INSERT INTO `menus` VALUES (11, '桃胶银耳雪梨羹', 'img/2019102215717241773499702111.jpg', 20.00, 0, 3);
INSERT INTO `menus` VALUES (12, '胡椒生蚝汤', 'img/2019102115716661191059702111.jpg', 21.00, 0, 3);
INSERT INTO `menus` VALUES (13, '鱼丸萝卜汤', 'img/2019102115716608558039702111.jpg', 22.00, 0, 3);
INSERT INTO `menus` VALUES (14, '番茄鸡蛋汤', 'img/2019102115716287061879702111.jpg', 23.00, 0, 3);
INSERT INTO `menus` VALUES (15, '三珍鸡汤', 'img/2019101715713041393429702111.jpg', 24.00, 0, 3);
INSERT INTO `menus` VALUES (16, '乌龙糖糖美梨茶', 'img/2019102215717252996679702111.jpg', 25.00, 0, 4);
INSERT INTO `menus` VALUES (17, '鲜榨石榴汁', 'img/2019102115716472001839702111.jpg', 26.00, 0, 4);
INSERT INTO `menus` VALUES (18, '青橙一夏', 'img/2019102115716223958289702111.jpg', 27.00, 0, 4);
INSERT INTO `menus` VALUES (19, '自制酸奶', 'img/2019102115716211848049702111.jpg', 28.00, 0, 4);
INSERT INTO `menus` VALUES (20, '营养米糊', 'img/2019102115716218856219702111.jpg', 29.00, 0, 3);
INSERT INTO `menus` VALUES (21, '龙江家园', 'img/njjy0.jpg', 30.00, 0, 5);
INSERT INTO `menus` VALUES (22, '老村长', 'img/u=3609073620,2457136441&fm=26&gp=0.jpg', 31.00, 0, 5);
INSERT INTO `menus` VALUES (23, '天之蓝', 'img/u=2049314601,3665480986&fm=26&gp=0.jpg', 32.00, 0, 5);
INSERT INTO `menus` VALUES (24, '杜康', 'img/u=4081255430,3853167316&fm=26&gp=0.jpg', 33.00, 0, 5);
INSERT INTO `menus` VALUES (25, '茅台', 'img/u=2562445222,1444485285&fm=26&gp=0.jpg', 34.00, 0, 5);
INSERT INTO `menus` VALUES (26, '怡宝', 'img/u=4020073957,3590036790&fm=26&gp=0.jpg', 35.00, 0, 5);
INSERT INTO `menus` VALUES (27, '康师傅', 'img/u=714860911,60642720&fm=26&gp=0.jpg', 36.00, 0, 5);
INSERT INTO `menus` VALUES (28, '农夫山泉', 'img/u=2709142893,481752613&fm=26&gp=0.jpg', 37.00, 0, 5);
INSERT INTO `menus` VALUES (29, '北京烤鸭', 'img/1536036311-WZVCpdngHN.jpg', 10.00, 0, 1);
INSERT INTO `menus` VALUES (30, '培根乌冬面', 'img/a4420184_156973942084006.jpg', 30.00, 0, 2);
INSERT INTO `menus` VALUES (31, '红烧茄子', 'img/a4420184_156973942084006.jpg', 30.00, 0, 2);

-- ----------------------------
-- Table structure for orderdetail
-- ----------------------------
DROP TABLE IF EXISTS `orderdetail`;
CREATE TABLE `orderdetail`  (
  `l_id` int(11) NOT NULL AUTO_INCREMENT,
  `l_count` int(11) NULL DEFAULT NULL,
  `l_price` decimal(8, 2) NULL DEFAULT NULL,
  `l_sum` decimal(8, 2) NULL DEFAULT NULL,
  `l_remarks` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `l_state` int(11) NULL DEFAULT NULL,
  `o_id` int(11) NULL DEFAULT NULL,
  `m_id` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`l_id`) USING BTREE,
  INDEX `o_id`(`o_id`) USING BTREE,
  INDEX `m_id`(`m_id`) USING BTREE,
  CONSTRAINT `orderdetail_ibfk_1` FOREIGN KEY (`o_id`) REFERENCES `orders` (`o_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `orderdetail_ibfk_2` FOREIGN KEY (`m_id`) REFERENCES `menus` (`m_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 38 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of orderdetail
-- ----------------------------
INSERT INTO `orderdetail` VALUES (1, 1, 17.00, 17.00, '', 1, 1, 8);
INSERT INTO `orderdetail` VALUES (2, 1, 23.00, 23.00, '', 1, 1, 14);
INSERT INTO `orderdetail` VALUES (3, 1, 12.00, 12.00, '', 1, 2, 3);
INSERT INTO `orderdetail` VALUES (4, 1, 13.00, 13.00, '', 1, 2, 4);
INSERT INTO `orderdetail` VALUES (5, 1, 10.00, 10.00, '', 1, 3, 1);
INSERT INTO `orderdetail` VALUES (6, 1, 37.00, 37.00, '', 1, 3, 28);
INSERT INTO `orderdetail` VALUES (7, 1, 17.00, 17.00, '', 1, 4, 8);
INSERT INTO `orderdetail` VALUES (8, 1, 15.00, 15.00, '', 1, 4, 6);
INSERT INTO `orderdetail` VALUES (9, 1, 16.00, 16.00, '', 1, 4, 7);
INSERT INTO `orderdetail` VALUES (10, 2, 10.00, 20.00, '', 1, 5, 1);
INSERT INTO `orderdetail` VALUES (11, 1, 12.00, 12.00, '', 1, 5, 3);
INSERT INTO `orderdetail` VALUES (12, 1, 13.00, 13.00, '', 1, 5, 4);
INSERT INTO `orderdetail` VALUES (13, 1, 30.00, 30.00, '', 1, 6, 21);
INSERT INTO `orderdetail` VALUES (14, 1, 15.00, 15.00, '', 1, 7, 6);
INSERT INTO `orderdetail` VALUES (15, 1, 19.00, 19.00, '', 1, 7, 10);
INSERT INTO `orderdetail` VALUES (16, 1, 23.00, 23.00, '', 1, 7, 14);
INSERT INTO `orderdetail` VALUES (17, 1, 25.00, 25.00, '', 1, 7, 16);
INSERT INTO `orderdetail` VALUES (18, 1, 10.00, 10.00, '', 1, 8, 1);
INSERT INTO `orderdetail` VALUES (19, 1, 12.00, 12.00, '', 1, 8, 3);
INSERT INTO `orderdetail` VALUES (20, 1, 13.00, 13.00, '', 1, 8, 4);
INSERT INTO `orderdetail` VALUES (21, 1, 14.00, 14.00, '', 1, 8, 5);
INSERT INTO `orderdetail` VALUES (22, 3, 14.00, 42.00, '', 1, 9, 5);
INSERT INTO `orderdetail` VALUES (23, 2, 13.00, 26.00, '', 1, 9, 4);
INSERT INTO `orderdetail` VALUES (24, 3, 12.00, 36.00, '', 1, 9, 3);
INSERT INTO `orderdetail` VALUES (25, 3, 10.00, 30.00, '', 1, 9, 1);
INSERT INTO `orderdetail` VALUES (26, 6, 17.00, 102.00, '', 1, 10, 8);
INSERT INTO `orderdetail` VALUES (27, 1, 35.00, 35.00, '', 1, 11, 26);
INSERT INTO `orderdetail` VALUES (29, 1, 10.00, 10.00, '', 1, 13, 29);
INSERT INTO `orderdetail` VALUES (30, 1, 27.00, 27.00, '', 1, 14, 18);
INSERT INTO `orderdetail` VALUES (31, 2, 30.00, 60.00, '', 1, 15, 31);
INSERT INTO `orderdetail` VALUES (32, 1, 19.00, 19.00, '', 1, 15, 10);
INSERT INTO `orderdetail` VALUES (33, 1, 10.00, 10.00, '', 1, 16, 1);
INSERT INTO `orderdetail` VALUES (34, 1, 12.00, 12.00, '', 1, 16, 3);
INSERT INTO `orderdetail` VALUES (35, 1, 13.00, 13.00, '', 1, 16, 4);
INSERT INTO `orderdetail` VALUES (36, 1, 14.00, 14.00, '', 1, 16, 5);
INSERT INTO `orderdetail` VALUES (37, 1, 15.00, 15.00, '', 1, 16, 6);

-- ----------------------------
-- Table structure for orders
-- ----------------------------
DROP TABLE IF EXISTS `orders`;
CREATE TABLE `orders`  (
  `o_id` int(11) NOT NULL AUTO_INCREMENT,
  `o_totalprice` decimal(8, 2) NULL DEFAULT NULL,
  `o_time` datetime(0) NULL DEFAULT NULL,
  `o_deskNum` int(11) NOT NULL,
  `u_id` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`o_id`) USING BTREE,
  INDEX `u_id`(`u_id`) USING BTREE,
  CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`u_id`) REFERENCES `users` (`u_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of orders
-- ----------------------------
INSERT INTO `orders` VALUES (1, 40.00, '2019-10-08 20:46:31', 5, 1);
INSERT INTO `orders` VALUES (2, 25.00, '2019-09-12 14:32:04', 6, 10);
INSERT INTO `orders` VALUES (3, 47.00, '2019-11-12 14:36:39', 5, 1);
INSERT INTO `orders` VALUES (4, 48.00, '2019-11-12 15:38:38', 5, 10);
INSERT INTO `orders` VALUES (5, 45.00, '2019-11-12 16:24:04', 6, 10);
INSERT INTO `orders` VALUES (6, 30.00, '2019-11-19 16:53:19', 6, 1);
INSERT INTO `orders` VALUES (7, 82.00, '2019-11-19 17:00:02', 6, 2);
INSERT INTO `orders` VALUES (8, 49.00, '2019-11-19 17:24:59', 6, 2);
INSERT INTO `orders` VALUES (9, 134.00, '2019-11-19 17:25:57', 6, 13);
INSERT INTO `orders` VALUES (10, 102.00, '2019-11-19 17:27:08', 6, 13);
INSERT INTO `orders` VALUES (11, 35.00, '2019-11-20 22:23:34', 5, 1);
INSERT INTO `orders` VALUES (13, 10.00, '2019-11-21 02:38:32', 6, 1);
INSERT INTO `orders` VALUES (14, 27.00, '2019-11-21 15:08:12', 8, 1);
INSERT INTO `orders` VALUES (15, 79.00, '2019-11-21 16:43:49', 5, 1);
INSERT INTO `orders` VALUES (16, 64.00, '2019-11-21 16:47:26', 5, 1);

-- ----------------------------
-- Table structure for qyy1
-- ----------------------------
DROP TABLE IF EXISTS `qyy1`;
CREATE TABLE `qyy1`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `waitQ` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `waitJ` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `resultQ` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `resultB` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `resultJ` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `dateTime` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_permission
-- ----------------------------
DROP TABLE IF EXISTS `sys_permission`;
CREATE TABLE `sys_permission`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '资源名称',
  `type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '资源类型：menu,button,',
  `url` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '访问url地址',
  `percode` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '权限代码字符串',
  `parentid` bigint(20) NULL DEFAULT NULL COMMENT '父结点id',
  `parentids` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '父结点id列表串',
  `sortstring` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '排序号',
  `available` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否可用,1：可用，0不可用',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_permission
-- ----------------------------
INSERT INTO `sys_permission` VALUES (1, '权限', '', '', NULL, 0, '0/', '0', '1');
INSERT INTO `sys_permission` VALUES (11, '菜品管理', 'menu', '', NULL, 1, '0/1/', '1.', '1');
INSERT INTO `sys_permission` VALUES (12, '菜品新增', 'permission', 'foodMassage/selType', 'foodMassage:selType', 11, '0/1/11/', '', '1');
INSERT INTO `sys_permission` VALUES (13, '下架菜品', 'permission', 'foodMassage/foodSelUp.html', 'foodMassage:foodSelUp.html', 11, '0/1/11', ' ', '1');
INSERT INTO `sys_permission` VALUES (15, '菜品查询', 'permission', 'foodMassage/foodSel.html', 'foodMassage:foodSel.html', 11, '0/1/15/', NULL, '1');
INSERT INTO `sys_permission` VALUES (21, '用户管理', 'menu', '', '', 1, '0/1/', '2.', '1');
INSERT INTO `sys_permission` VALUES (25, '用户查询', 'permission', 'user/query', 'user:query', 21, '0/1/21/', '', '1');
INSERT INTO `sys_permission` VALUES (31, '角色管理', 'menu', '', '', 1, '0/1/', '3.', '1');
INSERT INTO `sys_permission` VALUES (32, '角色查询', 'permission', 'role/query', 'role:query', 31, '0/1/31/', '', '1');
INSERT INTO `sys_permission` VALUES (41, '订单管理', 'menu', '', '', 1, '0/1/', '4.', '1');
INSERT INTO `sys_permission` VALUES (42, '订单查询', 'permission', 'jrj/selOrder', 'jrj:selOrder', 41, '0/1/41/', '', '1');
INSERT INTO `sys_permission` VALUES (51, '类型管理', 'menu', '', '', 1, '0/1/', '5.', '1');
INSERT INTO `sys_permission` VALUES (52, '查看类型', 'permission', 'type/aaa', 'type:aaa', 51, '0/1/51/', '', '1');
INSERT INTO `sys_permission` VALUES (61, '财务管理', 'menu', '', '', 1, '0/1/', '6.', '1');
INSERT INTO `sys_permission` VALUES (62, '销售统计', 'permission', 'wbl/caiwu', 'wbl:caiwu', 61, '0/1/61/', '', '1');
INSERT INTO `sys_permission` VALUES (63, '统计显示', 'permission', 'wbl/tables', 'wbl:tables', 61, '0/1/61/', '', '1');
INSERT INTO `sys_permission` VALUES (71, '后厨显示', 'menu', '', '', 1, '0/1/', '7.', '1');
INSERT INTO `sys_permission` VALUES (72, '当前订单', 'permission', 'booth.html', 'booth.html', 71, '0/1/71/', '', '1');
INSERT INTO `sys_permission` VALUES (81, '本店评论', 'menu', '', NULL, 1, '0/1/', '8.', '1');
INSERT INTO `sys_permission` VALUES (82, '查看评论', 'permission', 'comment/comment.html', 'comment:comment', 81, '0/1/81', NULL, '1');

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `id` varchar(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `available` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否可用,1：可用，0不可用',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES ('sr-000001', '超级管理员', '0');
INSERT INTO `sys_role` VALUES ('sr-000002', '管理员', '0');
INSERT INTO `sys_role` VALUES ('sr-000003', '角色管理员', '0');
INSERT INTO `sys_role` VALUES ('sr-000004', '菜品管理员', '0');
INSERT INTO `sys_role` VALUES ('sr-000005', '后厨', '0');

-- ----------------------------
-- Table structure for sys_role_permission
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_permission`;
CREATE TABLE `sys_role_permission`  (
  `id` varchar(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `sys_role_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '角色id',
  `sys_permission_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '权限id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_permission
-- ----------------------------
INSERT INTO `sys_role_permission` VALUES ('srp-00000001', 'sr-000001', '11');
INSERT INTO `sys_role_permission` VALUES ('srp-00000005', 'sr-000001', '12');
INSERT INTO `sys_role_permission` VALUES ('srp-00000006', 'sr-000001', '15');
INSERT INTO `sys_role_permission` VALUES ('srp-00000007', 'sr-000001', '21');
INSERT INTO `sys_role_permission` VALUES ('srp-00000008', 'sr-000001', '25');
INSERT INTO `sys_role_permission` VALUES ('srp-00000009', 'sr-000001', '31');
INSERT INTO `sys_role_permission` VALUES ('srp-00000010', 'sr-000001', '32');
INSERT INTO `sys_role_permission` VALUES ('srp-00000011', 'sr-000001', '41');
INSERT INTO `sys_role_permission` VALUES ('srp-00000012', 'sr-000001', '42');
INSERT INTO `sys_role_permission` VALUES ('srp-00000013', 'sr-000001', '51');
INSERT INTO `sys_role_permission` VALUES ('srp-00000014', 'sr-000001', '52');
INSERT INTO `sys_role_permission` VALUES ('srp-00000015', 'sr-000001', '61');
INSERT INTO `sys_role_permission` VALUES ('srp-00000016', 'sr-000001', '62');
INSERT INTO `sys_role_permission` VALUES ('srp-00000017', 'sr-000001', '63');
INSERT INTO `sys_role_permission` VALUES ('srp-00000018', 'sr-000001', '71');
INSERT INTO `sys_role_permission` VALUES ('srp-00000019', 'sr-000001', '72');
INSERT INTO `sys_role_permission` VALUES ('srp-00000020', 'sr-000001', '13');
INSERT INTO `sys_role_permission` VALUES ('srp-00000023', 'sr-000001', '81');
INSERT INTO `sys_role_permission` VALUES ('srp-00000024', 'sr-000001', '82');
INSERT INTO `sys_role_permission` VALUES ('srp-00000025', 'sr-000002', '11');
INSERT INTO `sys_role_permission` VALUES ('srp-00000026', 'sr-000002', '12');
INSERT INTO `sys_role_permission` VALUES ('srp-00000027', 'sr-000002', '13');
INSERT INTO `sys_role_permission` VALUES ('srp-00000028', 'sr-000002', '15');
INSERT INTO `sys_role_permission` VALUES ('srp-00000029', 'sr-000002', '41');
INSERT INTO `sys_role_permission` VALUES ('srp-00000030', 'sr-000002', '42');
INSERT INTO `sys_role_permission` VALUES ('srp-00000031', 'sr-000002', '81');
INSERT INTO `sys_role_permission` VALUES ('srp-00000032', 'sr-000002', '82');
INSERT INTO `sys_role_permission` VALUES ('srp-00000033', 'sr-000003', '31');
INSERT INTO `sys_role_permission` VALUES ('srp-00000034', 'sr-000003', '32');
INSERT INTO `sys_role_permission` VALUES ('srp-00000035', 'sr-000004', '11');
INSERT INTO `sys_role_permission` VALUES ('srp-00000036', 'sr-000004', '12');
INSERT INTO `sys_role_permission` VALUES ('srp-00000037', 'sr-000004', '13');
INSERT INTO `sys_role_permission` VALUES ('srp-00000038', 'sr-000004', '15');
INSERT INTO `sys_role_permission` VALUES ('srp-00000039', 'sr-000005', '71');
INSERT INTO `sys_role_permission` VALUES ('srp-00000040', 'sr-000005', '72');

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `id` varchar(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
  `usercode` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '账号',
  `username` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '姓名',
  `password` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '密码',
  `salt` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '盐',
  `locked` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '账号是否锁定，1：锁定，0未锁定',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES ('su-000001', 'admin', 'admin', 'bf07fd8bbc73b6f70b8319f2ebb87483', 'uiwueylm', '0');
INSERT INTO `sys_user` VALUES ('su-000002', 'lisi', '李四', 'bf07fd8bbc73b6f70b8319f2ebb87483', 'uiwueylm', '1');
INSERT INTO `sys_user` VALUES ('su-000003', 'zhangsan', '张三', 'cb571f7bd7a6f73ab004a70322b963d5', 'eteokues', '0');
INSERT INTO `sys_user` VALUES ('su-000004', '聂新月', '聂新月', 'ac6281a3acc47b0df69db52e90d5f53e', 'dc894e', '0');
INSERT INTO `sys_user` VALUES ('su-000005', 'zhang', '张凯强', 'b7143bed19e85e4287a63d6efe9567d5', '4a64e8', '0');

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `id` varchar(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `sys_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `sys_role_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role` VALUES ('sur-00000001', 'su-000001', 'sr-000001');
INSERT INTO `sys_user_role` VALUES ('sur-00000002', 'su-000002', 'sr-000002');
INSERT INTO `sys_user_role` VALUES ('sur-00000003', 'su-000003', 'sr-000003');
INSERT INTO `sys_user_role` VALUES ('sur-00000004', 'su-000004', 'sr-000004');
INSERT INTO `sys_user_role` VALUES ('sur-00000005', 'su-000004', 'sr-000004');
INSERT INTO `sys_user_role` VALUES ('sur-00000006', 'su-000005', 'sr-000005');

-- ----------------------------
-- Table structure for types
-- ----------------------------
DROP TABLE IF EXISTS `types`;
CREATE TABLE `types`  (
  `t_id` int(11) NOT NULL AUTO_INCREMENT,
  `t_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `t_state` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`t_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of types
-- ----------------------------
INSERT INTO `types` VALUES (1, '菜品', 0);
INSERT INTO `types` VALUES (2, '主食', 0);
INSERT INTO `types` VALUES (3, '汤类', 0);
INSERT INTO `types` VALUES (4, '饮品', 0);
INSERT INTO `types` VALUES (5, '酒水', 0);
INSERT INTO `types` VALUES (6, '肉类', 1);
INSERT INTO `types` VALUES (7, '水果', 0);

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `u_id` int(11) NOT NULL AUTO_INCREMENT,
  `u_openid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `u_img` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `u_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`u_id`) USING BTREE,
  UNIQUE INDEX `u_openid`(`u_openid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO `users` VALUES (1, 'oCXz25TqdMAqwq0n5LK3My52qDYQ', 'https://wx.qlogo.cn/mmopen/vi_32/fc5bzeg84mofB1BgRwI6tQdniasbv0TqcAOZK9XxNb7f2lSWtlgJ4VhVx1O0KJOKrtYpiaYKzT28uyHM8olP14sA/132', '清风眉宇间');
INSERT INTO `users` VALUES (2, 'oCXz25V3Q8mc3dN4BFvwDW4ryu4k', 'https://wx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLeSzJNySNbTepPXBT4bJZhVgNsxmFn3cl1NwSyv6sVIStmTPeqXLicwPBL5HBcFSQzQgbiaanDnW4Q/132', '超温柔少女');
INSERT INTO `users` VALUES (8, 'oCXz25ZHnu9cVnImWwLQjXit6uNc', 'https://wx.qlogo.cn/mmopen/vi_32/ZsVALrzhApxMZB51hAC3a6OxpXFjrclpDlvG0VSkkGK1ZibWV2nz1j6GPqWfJIyHguK56t9akatAyU1KUA7S9ew/132', '浦夜');
INSERT INTO `users` VALUES (9, 'oCXz25d_M2sOH9U7PeIQFz8-57YY', 'https://wx.qlogo.cn/mmopen/vi_32/t8m4GEg1It9uav4HMicNyZAmfRCtCQVuwsDGWXdy8V1zozibhxYGeO3ClvibuLJEScJJ55607mKSmBbaImzZmL04A/132', '心悬');
INSERT INTO `users` VALUES (10, 'oCXz25W2fl6Dj00cBeqPAp-Egy0s', 'https://wx.qlogo.cn/mmopen/vi_32/bVR2NptKE8o1Y3Z4hA2woibj57L8cib2FbCaqnqyleyMHvviaSln8wOs5kO2eyjgxIrTL1ia0yICXNahxWjfptAmLQ/132', 'Yg.@');
INSERT INTO `users` VALUES (11, '\'aaa\'', '\'aaa\'', '\'aaa\'');
INSERT INTO `users` VALUES (12, 'oCXz25SCt3_WL1CNc11ZrxgvieVg', 'https://wx.qlogo.cn/mmopen/vi_32/DYAIOgq83erUrETXvcx5mUQiaJVxzrE0eemicBChclY8BY26dR7j9rar3YM3zxjy4JXopdRGM5enLUoGNM7x3jVg/132', 'wsl');
INSERT INTO `users` VALUES (13, 'oCXz25Rv2ApH25XhaoPmbxdDSJUg', 'https://wx.qlogo.cn/mmopen/vi_32/DYAIOgq83eoLSsEoe3RMhauIONTeWLrpRicmmU0ibpcgrQlz6lt28LGE6iaYzwQ7fvb0hgCCr3fhzic25r2mwWQrBA/132', '仰望星空');
INSERT INTO `users` VALUES (14, 'oCXz25UtUAV_ngMaIUBBW_WpEpAQ', 'https://wx.qlogo.cn/mmopen/vi_32/DYAIOgq83eq6mh1jrlGeppyZkPQRuR5ve0ic89ME4ibCBUqABlDa33AlqiaFlPx8G5D3G01hpkR3oWTzpX4KHk5aA/132', '星星');

-- ----------------------------
-- Triggers structure for table sys_role
-- ----------------------------
DROP TRIGGER IF EXISTS `T_sr`;
delimiter ;;
CREATE TRIGGER `T_sr` BEFORE INSERT ON `sys_role` FOR EACH ROW begin
set new.id=concat('sr-',lpad(((SELECT substring(id,4,6) from sys_role where id=(select id from sys_role order by id desc limit 1))+1),6,0));
end
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table sys_role_permission
-- ----------------------------
DROP TRIGGER IF EXISTS `T_srp`;
delimiter ;;
CREATE TRIGGER `T_srp` BEFORE INSERT ON `sys_role_permission` FOR EACH ROW begin
set new.id=concat('srp-',lpad(((SELECT substring(id,5,8) from sys_role_permission where id=(select id from sys_role_permission order by id desc limit 1))+1),8,0));
end
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table sys_user
-- ----------------------------
DROP TRIGGER IF EXISTS `T_su`;
delimiter ;;
CREATE TRIGGER `T_su` BEFORE INSERT ON `sys_user` FOR EACH ROW begin
set new.id=concat('su-',lpad(((SELECT substring(id,4,6) from sys_user where id=(select id from sys_user order by id desc limit 1))+1),6,0));
end
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table sys_user_role
-- ----------------------------
DROP TRIGGER IF EXISTS `T_sur`;
delimiter ;;
CREATE TRIGGER `T_sur` BEFORE INSERT ON `sys_user_role` FOR EACH ROW begin
set new.id=concat('sur-',lpad(((SELECT substring(id,5,8) from sys_user_role where id=(select id from sys_user_role order by id desc limit 1))+1),8,0));
end
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
