<!DOCTYPE html>
<html lang="en"  xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>用户添加</title>
    <link rel="stylesheet" th:href="@{/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{/layui/formSelects-v4.css}"/>
    <script th:src="@{/layui/layui.js}"></script>
    <script th:src="@{/layui/formSelects-v4.js}" charset="utf-8"></script>
    <script th:src="@{/js/jquery-3.1.1.js}"></script>

    <style>
      .layui-form-item .layui-input{
          width:195px;
      }
    </style>
</head>
<body>
<br>
<br>
<form class="layui-form" action="" lay-filter="example">
    <div class="layui-form-item">
        <label class="layui-form-label">用户名</label>
        <div class="layui-input-block">
            <input type="text" name="username" lay-verify="username" autocomplete="off" placeholder="请输入用户名" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">账号</label>
        <div class="layui-input-block">
            <input type="text" name="usercode" lay-verify="usercode" autocomplete="off" placeholder="请输入账号" class="layui-input" id="usercode">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">密码</label>
        <div class="layui-input-block">
            <input type="password" name="password" lay-verify="password" placeholder="请输入密码" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">角色</label>
        <div class="layui-input-block" >
            <!--<select name="roleid" lay-filter="role"  xm-select="roleDetial">-->
                <select name="roleid" lay-filter="role"  >
                <!--<option value="">请选择</option>-->
                <option  th:each="r:${sysRoleList}" th:value="${r.id}" th:text="${r.name}"></option>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-input-block">
            <button type="submit" class="layui-btn" lay-submit="" lay-filter="demo1">添加</button>
            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
        </div>
    </div>
</form>
<script>
    layui.use('form', function() {
        var form = layui.form
        // var code=(Math.floor(Math.random()*1000000));
        // $("#userid").val(code);
        //监听提交
        form.on('submit(demo1)', function (data) {
            // layer.alert(JSON.stringify(data.field), {
            //     title: '最终的提交信息'
            // })
            $.post("add",data.field,function (result) {
                if(result.data==true){
                    layer.msg(result.msg, {
                        icon: 1,
                        time: 1000
                    }, function(){
                        var index=parent.layer.getFrameIndex(window.name); //获取当前窗口的name
                        parent.layer.close(index);		//关闭窗口
                        parent.window.location.reload();
                    });

                }else {
                    layer.msg(result.msg, {
                        icon: 7,
                        time: 3000
                    })


                }
            });
            return false;
        });
        // 自定义验证规则
        form.verify({
            username: function(value){
                if(value.length < 2){
                    return '用户名至少得2个字符啊';
                }
            }
            ,usercode: function(value){
                if(value.length < 2){
                    return '账号至少得2个字符啊';
                }
            }
            ,password: [
                /^[\S]{6,12}$/
                ,'密码必须6到12位，且不能出现空格'
            ]
            ,content: function(value){
                layedit.sync(editIndex);
            }
        });
       // layui.formSelects.render('roleDetial', {

            //init: ["1", "请先选择机构"],              //默认值
          //  skin: "normal", //多选皮肤normal|primary|default|danger|warm
          //  height: "auto",                //是否固定高度, 38px | auto
          //  radio: false,                  //是否设置为单选模式
         //   direction: "down",        //显示方式向下显示
          //  on: function(id, vals, val, isAdd, isDisabled){
          //      var getName = layui.formSelects.value('roleDetial');//取值
               // layer.msg("你选择的值为：" + getName);
          //  }           //同formSelects.on
      //  });


    })
</script>
</body>
</html>
