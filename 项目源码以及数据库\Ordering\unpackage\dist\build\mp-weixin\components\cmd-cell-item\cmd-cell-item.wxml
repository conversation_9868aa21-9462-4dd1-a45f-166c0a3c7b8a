<view class="{{['cmd-cell-item',(disabled)?'is-disabled':'',(!border)?'no-border':'']}}" hover-class="{{hoverClass}}" data-event-opts="{{[['tap',[['$_click',['$event']]]]]}}" bindtap="__e"><view class="cmd-cell-item-body"><block wx:if="{{slotLeft}}"><view class="cmd-cell-item-left"><slot></slot></view></block><view class="cmd-cell-item-content"><block wx:if="{{title}}"><view class="cmd-cell-item-title">{{title}}</view></block><block wx:if="{{brief}}"><view class="cmd-cell-item-brief">{{brief}}</view></block></view><block wx:if="{{slotRight}}"><view class="cmd-cell-item-right"><slot></slot><view class="cmd-cell-icon-arrow-right"><block wx:if="{{arrow}}"><cmd-icon vue-id="1" type="chevron-right" size="24" color="#C5CAD5" bind:__l="__l"></cmd-icon></block></view></view></block><block wx:if="{{!slotRight}}"><view class="cmd-cell-item-right"><text class="{{[addon.length>18?'cmd-cell-addon-text':'']}}">{{addon}}</text><view class="cmd-cell-icon-arrow-right"><block wx:if="{{showSwitch}}"><switch color="{{switchColor}}" disabled="{{disabled}}" checked="{{switchState}}" data-event-opts="{{[['change',[['$_switch',['$event']]]]]}}" bindchange="__e"></switch></block><block wx:if="{{arrow}}"><cmd-icon vue-id="2" type="chevron-right" size="24" color="#C5CAD5" bind:__l="__l"></cmd-icon></block></view></view></block></view><block wx:if="{{addon2}}"><view class="cmd-cell-item-children" style="font-size:24rpx;color:#858b9c;">{{addon2}}</view></block></view>