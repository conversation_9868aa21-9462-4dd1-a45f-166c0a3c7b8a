import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

public class DatabaseConnectionTest {
    public static void main(String[] args) {
        // 数据库连接信息（从application.properties复制）
        String url = "******************************************************************************************************************************";
        String username = "root";
        String password = "3132161409";
        
        System.out.println("=== MySQL连接测试 ===");
        System.out.println("数据库URL: " + url);
        System.out.println("用户名: " + username);
        System.out.println("密码: " + password);
        
        Connection conn = null;
        try {
            // 加载MySQL驱动
            Class.forName("com.mysql.cj.jdbc.Driver");
            System.out.println("✅ MySQL驱动加载成功");
            
            // 尝试连接数据库
            System.out.println("🔄 正在连接数据库...");
            conn = DriverManager.getConnection(url, username, password);
            System.out.println("✅ 数据库连接成功！");
            
            // 测试查询sys_user表
            System.out.println("\n--- 测试查询sys_user表 ---");
            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery("SELECT usercode, username, locked FROM sys_user LIMIT 5");
            
            System.out.println("用户列表:");
            while (rs.next()) {
                String usercode = rs.getString("usercode");
                String userName = rs.getString("username");
                String locked = rs.getString("locked");
                String status = "1".equals(locked) ? "🔒锁定" : "✅正常";
                System.out.println("  - " + usercode + " (" + userName + ") " + status);
            }
            
            // 测试登录相关查询
            System.out.println("\n--- 测试admin用户查询 ---");
            ResultSet adminRs = stmt.executeQuery("SELECT * FROM sys_user WHERE usercode='admin'");
            if (adminRs.next()) {
                System.out.println("admin用户信息:");
                System.out.println("  ID: " + adminRs.getString("id"));
                System.out.println("  用户名: " + adminRs.getString("username"));
                System.out.println("  密码hash: " + adminRs.getString("password"));
                System.out.println("  盐值: " + adminRs.getString("salt"));
                System.out.println("  锁定状态: " + adminRs.getString("locked"));
            } else {
                System.out.println("❌ 未找到admin用户");
            }
            
            rs.close();
            stmt.close();
            
        } catch (ClassNotFoundException e) {
            System.out.println("❌ MySQL驱动未找到: " + e.getMessage());
        } catch (java.sql.SQLException e) {
            System.out.println("❌ 数据库连接失败: " + e.getMessage());
            System.out.println("错误代码: " + e.getErrorCode());
            System.out.println("SQL状态: " + e.getSQLState());
        } catch (Exception e) {
            System.out.println("❌ 其他错误: " + e.getMessage());
        } finally {
            try {
                if (conn != null && !conn.isClosed()) {
                    conn.close();
                    System.out.println("✅ 数据库连接已关闭");
                }
            } catch (Exception e) {
                System.out.println("❌ 关闭连接时出错: " + e.getMessage());
            }
        }
    }
}
