<template>
	<view style="flex-wrap: wrap;">
		
		<view class="orders-list" v-for="(item,index) in orders" :key="index" @click="toDetails(item.o_id)">
			<view class="uni-media-list-logo">
			    <image v-if="showImg" :src="http+upload+item.mimg" style="width: 60px;" mode="widthFix"></image>
		    </view> 
			<view class="uni-media-list-body">
			    <!-- <view class="uni-media-list-text-top">{{item.mname}}<span class="time">{{item.o_time}}</span></view> -->
			    <view class="uni-media-list-text-top">下单时间:{{item.o_time}}</view>
				<view class="uni-media-list-text-bottom uni-ellipsis">{{item.o_deskNum}}号桌<span class="sumprice">总价:{{item.o_totalprice}}￥</span></view>
			</view>
			 
		</view>
		
	</view>
</template>

<script>
	var _self;
	export default {
		data() {
			return {
				showImg:true,
				upload :'upload/',
				orders:[],
				u_id : 0,
				http:''
			}
		},
		onLoad:function(e){
			_self=this;
			_self.uid = getApp().globalData.u_id;
			_self.http = getApp().globalData.http;
			this.getOrders();
		},
		methods: {
			getOrders:function(){
				uni.request({
					url: _self.http+'item/ItemList',
					method:'GET',
					data: {
					        uid:_self.uid
					    },
					 success: (res) => {
							var date=res.data;
							for (let i in date) {
							 let d = new Date(date[i].o_time);
							 let year = d.getFullYear();        
							 let month = d.getMonth() + 1;      
							 let day = d.getDate();			 
							 let hh = d.getHours();            
							 let mm = d.getMinutes();           
							 let ss = d.getSeconds();
							 date[i].o_time = year+"-"+month+"-"+day+" "+hh+":"+mm+":"+ss;
							 }
							_self.orders=res.data;
					    }
				});
			},
			toDetails(id){
				uni.navigateTo({
				    url: 'scroll/scroll?u_id='+id,
				});
			}
		}
	}
</script>

<style>
view{
	width: 100%;
	}
.orders-list{
display: flex;width: 94%;
padding:20rpx 3% ;
flex-wrap: nowrap;
margin:30rpx 0;
background: linear-gradient(to right,rgba(0,0,0,.01),#fff);

box-shadow:0px 2px 2px -2px rgba(0,0,0,.3);
 }
/* .time{
        float: right;
		color: #939393;
        font-size: 12px;
} */
.sumprice{
	float: right;
	color: #DD524D;
	font-size:15px;
}
.uni-media-list-logo{
        width: 60px;
        height: 60px;
		padding:0px 15px;
}
.uni-media-list-logo uni_image{
	
        border-radius: 5px;
}
.uni-media-list-text-top{
	   font-size: 16px;
		
}
.uni-media-list-text-bottom{
	
        line-height: 14px;
        font-size: 14px;
        width: 85%;
		/* padding:30rpx 0;
		height: 20px; */
}
.uni-media-list-body{
	
        height: 45px;
}
.content{
	
}
</style>
