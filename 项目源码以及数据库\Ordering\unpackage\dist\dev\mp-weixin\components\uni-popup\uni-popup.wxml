<block wx:if="{{showPopup}}"><view class="uni-popup"><view data-event-opts="{{[['tap',[['close',[true]]]]]}}" class="{{['uni-popup__mask',ani,animation?'ani':'',!custom?'uni-custom':'']}}" bindtap="__e"></view><view data-event-opts="{{[['tap',[['close',[true]]]]]}}" class="{{['uni-popup__wrapper',type,ani,animation?'ani':'',!custom?'uni-custom':'']}}" bindtap="__e"><view data-event-opts="{{[['tap',[['clear',['$event']]]]]}}" class="uni-popup__wrapper-box" catchtap="__e"><slot></slot></view></view></view></block>