<!DOCTYPE html>
<html lang="en"  xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>订单详情</title>
    <link rel="stylesheet" th:href="@{/layui/css/layui.css}">
    <script th:src="@{/layui/layui.js}"></script>
    <script th:src="@{/js/jquery-3.1.1.js}"></script>
    <style>
        .layui-table-cell{
            text-align: center;
            height: auto;
            white-space: normal;
        }
        .layui-table img{
            max-width: 300px;
        }
    </style>
</head>
<body>
<div>
    <input type="hidden" th:value="${id}" id="o_id">
    <table class="layui-hide" id="orderDetialAll" lay-filter="demo"></table>
</div>
<script th:inline="none">
    var id=$("#o_id").val();
    layui.use(['table','layer'], function() {
            var table = layui.table;
            var layer = layui.layer;
            table.render({
                elem: '#orderDetialAll'
                , url: 'queryOrderDetialData?id='+id
                , cellMinWidth: 80
                , cols: [[
                    {field: 'l_id',  title: 'DetialID',align:'center', sort: true,hide:true}
                    , {field: 'm_id', title: '菜品ID',align:'center',templet: '<div>{{d.menus[0].m_id}}</div>', hide: true}
                    , {field: 'm_name', width: '10%', title: '菜品',align:'center',templet: '<div>{{d.menus[0].m_name}}</div>',style:'height:110px;'}
                    , {field: 'm_img', width: '20%', title: '图片',align:'center', templet:'<div><img src=\'/restaurant/upload/{{d.menus[0].m_img}}\' style=\'width:100px;height: 100px;\'/></div>',}
                    , {field: 'm_price', width: '10%', title: '价格',align:'center', templet: '<div>{{d.menus[0].m_price}}</div>'}
                    , {field: 'm_state', title: '菜品状态',align:'center',templet: '<div>{{d.menus[0].m_state}}</div>',hide:true }

                    , {field: 't_name', width: '10%', title: '类型',align:'center',templet: '<div>{{d.menus[0].types[0].t_name}}</div>' }

                    , {field: 'o_id', title: '订单编号',align:'center',templet: '<div>{{d.orders[0].o_id}}</div>',hide:true}
                    , {field: 'o_time', width: '10%', title: '下单时间',align:'center',templet: '<div>{{d.orders[0].o_time}}</div>'}
                    , {field: 'o_totalprice', width: '10%', title: '订单金额',align:'center',templet: '<div>{{d.orders[0].o_totalprice}}</div>'}

                    , {field: 'l_count', width: '10%', title: '数量',align:'center'}
                    // , {field: 'l_sum', width: '10%', title: '总金额',align:'center'}
                    // , {field: 'l_remarks', width: '10%', title: '备注',align:'center'}
                ]]

            });
        })
</script>
<!--<script type="text/html" id="imgtmp">-->
    <!--<img th:src="@{/images/{{d.menus[0].m_img}}}" style="width:100px;height:100px;">-->
<!--</script>-->
</body>
</html>