package com.aaa.config;

import com.aaa.entity.ActiveUser;
import com.aaa.entity.SysPermission;
import com.aaa.entity.SysUser;
import com.aaa.service.SysService;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.util.ByteSource;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashSet;
import java.util.List;
import java.util.Set;


public class UserRealm extends AuthorizingRealm {
    @Autowired
    private SysService sysService;
    //认证
    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken authenticationToken) throws AuthenticationException {
        String usercode=(String)authenticationToken.getPrincipal();
        SysUser sysUser=null;
        try {
            sysUser=sysService.findSysUserByUserCode(usercode);

        } catch (Exception e) {
            e.printStackTrace();
        }
        if (sysUser==null) {
            return null;
        }
      //SimpleAuthenticationInfo info=new SimpleAuthenticationInfo(sysUser,sysUser.getPassword(),ByteSource.Util.bytes(sysUser.getSalt()),getName());
      // return info;

// 根据用户id取出菜单
        List<SysPermission> menus = null;
        List<SysPermission> permissions=null;
        try {
            menus = sysService.findMenuListByUserId(sysUser.getId());
            permissions=sysService.findPermissionListByUserId(sysUser.getId());
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        // 用户密码
        String password = sysUser.getPassword();
        //盐
        String salt = sysUser.getSalt();

        // 构建用户身体份信息
        ActiveUser activeUser = new ActiveUser();
        activeUser.setUserid(sysUser.getId());
        activeUser.setUsername(sysUser.getUsername());
        activeUser.setUsercode(sysUser.getUsercode());
        activeUser.setMenus(menus);
        activeUser.setPermissions(permissions);

        // 获取用户输入的密码
        char[] passwordChars = (char[]) authenticationToken.getCredentials();
        String inputPassword = new String(passwordChars);

        System.out.println("=== UserRealm 自定义密码验证 ===");
        System.out.println("用户输入的密码: " + inputPassword);
        System.out.println("数据库存储的密码: " + password);
        System.out.println("盐值: " + salt);
        System.out.println("用户名: " + usercode);

        // 自定义密码验证逻辑
        String hashedInputPassword = getMD5(inputPassword + salt);
        System.out.println("输入密码加密后: " + hashedInputPassword);

        if (!hashedInputPassword.equals(password)) {
            System.out.println("密码验证失败！");
            throw new org.apache.shiro.authc.IncorrectCredentialsException("密码不正确");
        }

        System.out.println("密码验证成功！");

        // 返回认证信息，使用输入的密码作为凭证（绕过Shiro的密码验证）
        SimpleAuthenticationInfo simpleAuthenticationInfo = new SimpleAuthenticationInfo(
                activeUser, inputPassword, getName());

        return simpleAuthenticationInfo;


    }
    //授权
    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principalCollection) {

        SimpleAuthorizationInfo info=new SimpleAuthorizationInfo();
        //获取认证过的用户
        Subject subject=SecurityUtils.getSubject();
        ActiveUser user=(ActiveUser) subject.getPrincipal();
        //查询该用户所有的权限
        List<SysPermission> ps = null;
        try {
            ps=sysService.findPermissionListByUserId(user.getUserid());
            System.out.println(ps);
        } catch (Exception e) {
            e.printStackTrace();
        }
        Set<String> permissions = new HashSet<String>();
        for(SysPermission p:ps){
            permissions.add(p.getPercode());
        }
        info.setStringPermissions(permissions);
        return info;
    }

    // MD5加密方法
    private String getMD5(String input) {
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(input.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b & 0xff));
            }
            return sb.toString();
        } catch (Exception e) {
            return "";
        }
    }


}
