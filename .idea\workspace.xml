<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="1caefecf-16d0-44b3-8955-fdea1201c365" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/App.vue" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/App.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/components/uni-goods-nav/uni-goods-nav.vue" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/components/uni-goods-nav/uni-goods-nav.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/components/validCode.vue" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/components/validCode.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/manifest.json" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/manifest.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/pages.json" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/pages.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/pages/index/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/pages/index/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/pages/info/info.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/pages/login/login.vue" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/pages/login/login.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/pages/mine/mine.vue" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/pages/mine/mine.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/pages/pay/pay.vue" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/pages/pay/pay.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/pages/test/test.vue" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/pages/test/test.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/pages/user/info/info.vue" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/pages/user/info/info.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/static/fqfp.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/static/jdgb.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/static/pjtd.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/common/main.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/common/main.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/common/runtime.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/common/runtime.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/common/vendor.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/common/vendor.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/cmd-avatar/cmd-avatar.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/cmd-avatar/cmd-avatar.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/cmd-cell-item/cmd-cell-item.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/cmd-cell-item/cmd-cell-item.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/cmd-icon/cmd-icon.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/cmd-icon/cmd-icon.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/uni-badge/uni-badge.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/uni-badge/uni-badge.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/uni-drawer/uni-drawer.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/uni-drawer/uni-drawer.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/uni-goods-nav/uni-goods-nav.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/uni-goods-nav/uni-goods-nav.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/uni-icons/uni-icons.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/uni-icons/uni-icons.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/uni-list-item/uni-list-item.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/uni-list-item/uni-list-item.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/uni-list/uni-list.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/uni-list/uni-list.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/uni-popup/uni-popup.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/uni-popup/uni-popup.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/uni-swiper-dot/uni-swiper-dot.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/uni-swiper-dot/uni-swiper-dot.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/validCode.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/validCode.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/yp-number-box/yp-number-box.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/yp-number-box/yp-number-box.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/yp-number-box/yp-number-box1.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/components/yp-number-box/yp-number-box1.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/pages/index/index.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/pages/index/index.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/pages/login/login.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/pages/login/login.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/mine.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/mine.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/pages/pay/pay.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/pages/pay/pay.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/pages/test/test.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/.sourcemap/mp-weixin/pages/test/test.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/restaurant/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/restaurant/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/restaurant/src/main/java/com/aaa/RestaurantApplication.java" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/restaurant/src/main/java/com/aaa/RestaurantApplication.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/restaurant/src/main/java/com/aaa/controller/FoodMassageController.java" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/restaurant/src/main/java/com/aaa/controller/FoodMassageController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/restaurant/src/main/java/com/aaa/controller/ZYGItemController.java" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/restaurant/src/main/java/com/aaa/controller/ZYGItemController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/restaurant/src/main/java/com/aaa/controller/ZkqMenuShowController.java" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/restaurant/src/main/java/com/aaa/controller/ZkqMenuShowController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/restaurant/src/main/java/com/aaa/controller/ZygDetailsController.java" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/restaurant/src/main/java/com/aaa/controller/ZygDetailsController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/restaurant/src/main/resources/application.properties" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/restaurant/src/main/resources/application.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/restaurant/src/main/resources/templates/comment/comment.html" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/restaurant/src/main/resources/templates/comment/comment.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/项目源码以及数据库/restaurant/src/main/resources/templates/sysuser/updPwd.html" beforeDir="false" afterPath="$PROJECT_DIR$/项目源码以及数据库/restaurant/src/main/resources/templates/sysuser/updPwd.html" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/项目源码以及数据库/Ordering" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="E:\huanjing\apache-maven-3.9.9" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2wB8bXDbkGvWNP4xLdzCgyjzIzC" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.restaurant.executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.RestaurantApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/餐饮点餐系统&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;project.propVCSSupport.DirectoryMappings&quot;,
    &quot;ts.external.directory.path&quot;: &quot;E:\\software\\JetBrains\\IntelliJ IDEA 2025.1\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="MavenRunConfiguration" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.RestaurantApplication">
    <configuration name="restaurant" type="MavenRunConfiguration" factoryName="Maven" nameIsGenerated="true">
      <MavenSettings>
        <option name="myGeneralSettings" />
        <option name="myRunnerSettings" />
        <option name="myRunnerParameters">
          <MavenRunnerParameters>
            <option name="cmdOptions" />
            <option name="profiles">
              <set />
            </option>
            <option name="goals">
              <list />
            </option>
            <option name="multimoduleDir" />
            <option name="pomFileName" />
            <option name="profilesMap">
              <map>
                <entry key="jdk-1.8" value="true" />
              </map>
            </option>
            <option name="projectsCmdOptionValues">
              <list />
            </option>
            <option name="resolveToWorkspace" value="false" />
            <option name="workingDirPath" value="$PROJECT_DIR$/项目源码以及数据库/restaurant" />
          </MavenRunnerParameters>
        </option>
      </MavenSettings>
      <method v="2" />
    </configuration>
    <configuration name="RestaurantApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="restaurant" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.aaa.RestaurantApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="1caefecf-16d0-44b3-8955-fdea1201c365" name="更改" comment="" />
      <created>1745503679701</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1745503679701</updated>
      <workItem from="1745503682178" duration="353000" />
      <workItem from="1745564962962" duration="869000" />
      <workItem from="1745565927720" duration="167000" />
      <workItem from="1745568620298" duration="192000" />
      <workItem from="1745568889509" duration="219000" />
      <workItem from="1745574428250" duration="1818000" />
      <workItem from="1745578303249" duration="1340000" />
      <workItem from="1745579690669" duration="683000" />
      <workItem from="1745580602987" duration="1000000" />
      <workItem from="1748247148619" duration="1303000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <ignored-roots>
      <path value="$PROJECT_DIR$/项目源码以及数据库/Ordering/unpackage/dist/dev/mp-weixin" />
    </ignored-roots>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>