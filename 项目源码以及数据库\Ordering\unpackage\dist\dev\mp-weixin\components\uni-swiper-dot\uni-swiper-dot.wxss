
.uni-swiper__warp {
	position: relative;
	width: 100%;
	box-sizing: border-box;
	overflow: hidden;
}
.uni-swiper__dots-box {
	position: absolute;
	bottom: 20rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	box-sizing: box-sizing;
	width: 100%;
}
.uni-swiper__dots-item {
	flex-shrink: 0;
	width: 16rpx;
	border-radius: 50%;
	margin-left: 12rpx;
	background: rgba(0, 0, 0, .3);
	transition: all 0.2s linear;
}
.uni-swiper__dots-item:first-child {
	margin: 0;
}
.uni-swiper__dots-default {
	border-radius: 50%;
}
.uni-swiper__dots-long {
	border-radius: 100rpx;
}
.uni-swiper__dots-bar {
	border-radius: 100rpx;
}
.uni-swiper__dots-nav {
	bottom: 0;
	height: 80rpx;
	justify-content: flex-start;
	background: rgba(0, 0, 0, 0.2);
	box-sizing: box-sizing;
	overflow: hidden;
}
.uni-swiper__dots-nav-item {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	font-size: 28rpx;
	color: #fff;
	box-sizing: box-sizing;
	margin: 0 30rpx;
}
.uni-swiper__dots-indexes {
	display: flex;
	justify-content: center;
	align-items: center;
	color: #fff;
	font-size: 24rpx;
}

