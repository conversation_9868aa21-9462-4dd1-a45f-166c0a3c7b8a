(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/uni-goods-nav/uni-goods-nav"],{2335:function(t,n,o){"use strict";o.r(n);var i=o("2842"),e=o.n(i);for(var u in i)"default"!==u&&function(t){o.d(n,t,function(){return i[t]})}(u);n["default"]=e.a},2842:function(t,n,o){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o={name:"UniGoodsNav",props:{options:{type:Array,default:function(){return[{icon:"https://img-cdn-qiniu.dcloud.net.cn/uniapp/uni-ui/goodsnav/dianpu.png",text:"店铺"},{icon:"https://img-cdn-qiniu.dcloud.net.cn/uniapp/uni-ui/goodsnav/carts.png",text:"购物车"}]}},buttonGroup:{type:Array,default:function(){return[{text:"加入购物车",backgroundColor:"#ff0000",color:"#fff"},{text:"立即购买",backgroundColor:"#ffa200",color:"#fff"}]}},fill:{type:Boolean,default:!1},maskClick:{type:Boolean,default:!0},show:{type:Boolean,default:!0}},data:function(){return{showGoods:!1}},methods:{onClick:function(t,n){this.$emit("click",{index:t,content:n})},buttonClick:function(n,o){t.report&&t.report(o.text,o.text),this.$emit("butt",{index:n,content:o})},open:function(){var t=this;this.$emit("change",{show:!0}),this.showGoods=!0,this.$nextTick(function(){t.ani="uni-"+t.type})},close:function(t){var n=this;!this.maskClick&&t||(this.$emit("change",{show:!1}),this.ani="",this.$nextTick(function(){n.showGoods=!1}))}}};n.default=o}).call(this,o("543d")["default"])},3437:function(t,n,o){"use strict";o.r(n);var i=o("5794"),e=o("2335");for(var u in e)"default"!==u&&function(t){o.d(n,t,function(){return e[t]})}(u);o("adea");var a=o("2877"),c=Object(a["a"])(e["default"],i["a"],i["b"],!1,null,null,null);n["default"]=c.exports},5794:function(t,n,o){"use strict";var i=function(){var t=this,n=t.$createElement;t._self._c},e=[];o.d(n,"a",function(){return i}),o.d(n,"b",function(){return e})},a645:function(t,n,o){},adea:function(t,n,o){"use strict";var i=o("a645"),e=o.n(i);e.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/uni-goods-nav/uni-goods-nav-create-component',
    {
        'components/uni-goods-nav/uni-goods-nav-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('543d')['createComponent'](__webpack_require__("3437"))
        })
    },
    [['components/uni-goods-nav/uni-goods-nav-create-component']]
]);                
