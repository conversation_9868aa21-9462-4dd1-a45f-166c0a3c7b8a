{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/pay/pay.vue?8a30", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/pay/pay.vue?3710", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/pay/pay.vue?73ca", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/pay/pay.vue?ad0b", "uni-app:///pages/pay/pay.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "amount", "orderDate", "deskNumber", "selectMenus", "affirm", "pwd", "password", "result", "u_id", "http", "m_id", "count", "components", "uniPopup", "validCode", "methods", "pay", "close", "yesPay", "clear", "setTimeout", "fg", "uni", "requestAuthModes", "challenge", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "success", "_sef", "fail", "title", "pwds", "getPwd", "payment", "console", "method", "header", "url", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;;;AAGlD;AAC+K;AAC/K,gBAAgB,wLAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,8LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA+oB,CAAgB,oqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiEnqB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAGA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;IACAC;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MAAA;MACAC;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACAC;QACAC;QAAA;QACAC;QACAC;QACAC;UACA;UACAC;UACAA;QACA;QACAC;UACA;UACAR,WACA;YACAE;cACAO;YACA;YACAF;YACAA;UACA,GACA;QAEA;MACA;IACA;IAEAG;MACA;MACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;QACAT;UACAO;QACA;QACA;QACAF;MACA;IACA;IACAK;MACA;MACAC;MACAX;QACAY;QACAC;UACA;QACA;;QACAC;QAAA;QACArC;UACAE;UACAO;UACAR;UACAE;UACAQ;UACAC;QACA;QACAe;UACAO;UACA;UACA;UACAX;YACAc;UACA;QACA;MACA;IACA,EACA;IACA;IACA;EACA;EACAC;IACAV;IACAA;IACAA;IACAA;IACAA;IACAA;IACAA;IACAA;EACA;AACA;AAAA,2B", "file": "pages/pay/pay.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/pay/pay.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./pay.vue?vue&type=template&id=c10d0c50&\"\nvar renderjs\nimport script from \"./pay.vue?vue&type=script&lang=js&\"\nexport * from \"./pay.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/pay/pay.vue\"\nexport default component.exports", "export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pay.vue?vue&type=template&id=c10d0c50&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pay.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./pay.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view>\r\n\t\t\t<view style=\"width: 60%;height: 50px;margin: 120px auto 0px auto;padding-left: 50px;\">\r\n\t\t\t\t<text>金额</text><br/>\r\n\t\t\t\t<text style=\"font-size: 36px;\">&yen;{{amount}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view><button style=\"background-color: #01C760;width: 60%;margin: 60px auto;\" @click=\"pay\">支付</button></view>\r\n\t\t</view>\r\n\t\t<uni-popup  ref=\"popup\" type=\"center\" @change=\"clear\">\r\n\t\t\t<!-- 确认付款 -->\r\n\t\t\t<view v-if=\"affirm\" style=\"width: 240px; height: 200px;\" >\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<view style=\"float: left;color: #808080;\" @click=\"close\">x</view>\r\n\t\t\t\t\t<view style=\"float: right;text-align: right;font-size: 16px;\"><text style=\"color: #576B95;\" @click=\"pwds\">使用密码</text></view>\r\n\t\t\t\t\t<view style=\"clear: both;\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"text-align: center;width: 100%;border-bottom: #888888;font-size: 16px;\">\r\n\t\t\t\t\t付给商家<br/><text style=\"font-size: 28px;\">&yen;{{amount}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<view style=\"float: left;font-size: 14px;color: #808080;\">支付方式</view>\r\n\t\t\t\t\t<view style=\"float: right;text-align: right;font-size: 14px;color: #808080;\">零钱></view>\r\n\t\t\t\t\t<view style=\"clear: both;\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"margin-top: 30px;\">\r\n\t\t\t\t\t<button @click=\"yesPay\" style=\"width: 80%;background-color:#01C760;\">确认支付</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- 指纹付款 -->\r\n\t\t\t<!-- <view v-if=\"fingerprint\">\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<view style=\"float: left;color: #808080;\" @click=\"close\">x</view>\r\n\t\t\t\t\t<view style=\"float: right;text-align: right;font-size: 16px;\"><text style=\"color: #576B95;\" @click=\"pwds\">使用密码</text></view>\r\n\t\t\t\t\t<view style=\"clear: both;\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view> -->\r\n\t\t\t<!-- 密码付款 -->\r\n\t\t\t<view v-if=\"pwd\">\r\n\t\t\t\t<view style=\"width: 240px;\">\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<view style=\"float: left;color: #808080;\" @click=\"close\">x</view>\r\n\t\t\t\t\t\t<view style=\"float: right;text-align: right;font-size: 16px;\"><text style=\"color: #576B95;\" @click=\"yesPay\">使用指纹</text></view>\r\n\t\t\t\t\t\t<view style=\"clear: both;\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view style=\"text-align: center; margin-top: 20px; width: 100%;border-bottom: #888888;font-size: 16px;\">\r\n\t\t\t\t\t\t付给商家<br/><text style=\"font-size: 28px;\">&yen;{{amount}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<view style=\"float: left;font-size: 14px;color: #808080;\">支付方式</view>\r\n\t\t\t\t\t\t<view style=\"float: right;text-align: right;font-size: 14px;color: #808080;\">零钱></view>\r\n\t\t\t\t\t\t<view style=\"clear: both;\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view style=\"margin-top: 30px;\">\r\n\t\t\t\t\t\t<!-- ref=\"pwdclear\" -->\r\n\t\t\t\t\t\t<validCode :maxlength=\"6\" :isPwd=\"true\" @finish=\"getPwd\" style=\"width: 100%;\" ></validCode>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t</view>\r\n\t\r\n</template>\r\n\r\n<script>\r\n\tlet _sef;\r\n\timport uniPopup from \"@/components/uni-popup/uni-popup.vue\"\r\n\timport validCode from '@/components/validCode.vue'\r\n\texport default{\r\n\t\tdata(){\r\n\t\t\treturn{\r\n\t\t\t\tamount:0,\r\n\t\t\t\torderDate:'',\r\n\t\t\t\tdeskNumber:0,\r\n\t\t\t\tselectMenus:[],\r\n\t\t\t\taffirm:true,\r\n\t\t\t\t//fingerprint:false,\r\n\t\t\t\tpwd:false,\r\n\t\t\t\tpassword:'666666',\r\n\t\t\t\tresult:false,\r\n\t\t\t\tu_id:0,\r\n\t\t\t\thttp:'',\r\n\t\t\t\tm_id:[],\r\n\t\t\t\tcount:[]\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomponents:{\r\n\t\t\tuniPopup,\r\n\t\t\tvalidCode\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\tpay(){\r\n\t\t\t\tthis.$refs.popup.open();\r\n\t\t\t},\r\n\t\t\tclose(){\r\n\t\t\t\tthis.$refs.popup.close();\r\n\t\t\t},\r\n\t\t\tyesPay(){\r\n\t\t\t\tthis.affirm = false;\r\n\t\t\t\tthis.pwd = false;\r\n\t\t\t\tthis.close();\r\n\t\t\t\tthis.fg();\r\n\t\t\t},\r\n\t\t\tclear(){\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthis.affirm = true;\r\n\t\t\t\t\t//this.fingerprint = false;\r\n\t\t\t\t\tthis.pwd = false;\r\n\t\t\t\t},300);\r\n\t\t\t},\r\n\t\t\t//指纹支付\r\n\t\t\tfg(){\r\n\t\t\t\tuni.startSoterAuthentication({\r\n\t\t\t\t    requestAuthModes: ['fingerPrint'],//facial 人脸  fingerPrint指纹\r\n\t\t\t\t    challenge: '123456',\r\n\t\t\t\t    authContent: '请验证指纹',\r\n\t\t\t\t    success(res) {\r\n\t\t\t\t       //result console.log(res);\r\n\t\t\t\t\t   _sef.result = true;\r\n\t\t\t\t\t   _sef.payment();\r\n\t\t\t\t    },\r\n\t\t\t\t    fail(err) {\r\n\t\t\t\t        //console.log(err);\r\n\t\t\t\t\t\tsetTimeout(\r\n\t\t\t\t\t\t()=>{\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '指纹错误！'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t_sef.pay();\r\n\t\t\t\t\t\t\t_sef.pwds();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t, 2000);\r\n\t\t\t\t\t\t\r\n\t\t\t\t    }\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tpwds(){\r\n\t\t\t\tthis.affirm = false;\r\n\t\t\t\t//this.fingerprint = false;\r\n\t\t\t\tthis.pwd = true;\r\n\t\t\t},\r\n\t\t\tgetPwd(e){\r\n\t\t\t\tif(this.password == e){\r\n\t\t\t\t\tthis.result = true;\r\n\t\t\t\t\tthis.payment();\r\n\t\t\t\t}else{\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '密码错误！'\r\n\t\t\t\t\t});\r\n\t\t\t\t\t//_sef.pwdclears();\r\n\t\t\t\t\t_sef.pwds();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tpayment(){\r\n\t\t\t\tthis.close();\r\n\t\t\t\tconsole.log(this.selectMenus);\r\n\t\t\t\tuni.request({\r\n\t\t\t\t\tmethod: 'POST',\r\n\t\t\t\t\theader: {\r\n\t\t\t\t\t    'content-type': 'application/x-www-form-urlencoded',   //自定义请求头信息\r\n\t\t\t\t\t},\r\n\t\t\t\t\turl: this.http+\"zkq/addOrder\", //仅为示例，并非真实接口地址。\r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t\torderDate : this.orderDate,\r\n\t\t\t\t\t\tu_id : this.u_id,\r\n\t\t\t\t\t\tamount : this.amount,\r\n\t\t\t\t\t\tdeskNumber : this.deskNumber,\r\n\t\t\t\t\t\tm_id : this.m_id,\r\n\t\t\t\t\t\tcount : this.count\r\n\t\t\t\t\t},\r\n\t\t\t\t    success: (res) => {\r\n\t\t\t\t        console.log(res.data);\r\n\t\t\t\t        //this.text = 'request success';\r\n\t\t\t\t\t\t//跳转页面\r\n\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t    url: '/pages/index/index'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t    }\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// pwdclears(){\r\n\t\t\t// \tthis.$refs.pwdclear.clear();\r\n\t\t\t// }\r\n\t\t},\r\n\t\tonLoad(option){\r\n\t\t\t_sef = this;\r\n\t\t\t_sef.orderDate = option.orderDate\r\n\t\t\t_sef.deskNumber = option.deskNumber\r\n\t\t\t_sef.m_id = option.id\r\n\t\t\t_sef.count = option.count\r\n\t\t\t_sef.amount = option.price\r\n\t\t\t_sef.u_id = getApp().globalData.u_id\r\n\t\t\t_sef.http = getApp().globalData.http\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style>\r\n</style>\r\n"], "sourceRoot": ""}