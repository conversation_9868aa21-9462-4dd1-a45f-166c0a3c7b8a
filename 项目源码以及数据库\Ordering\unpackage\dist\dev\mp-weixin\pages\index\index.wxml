<view><view><uni-popup class="vue-ref" vue-id="8dd740cc-1" type="top" data-ref="popup1" bind:__l="__l" vue-slots="{{['default']}}"><view>请确认订单：</view><block wx:for="{{selectMenus}}" wx:for-item="itme" wx:for-index="index" wx:key="index"><view class="anorder"><view><image style="width:80px;" src="{{http+upload+itme.m_img}}" mode="widthFix"></image></view><view><view style="height:40px;overflow:hidden;">{{itme.m_name}}</view>单价：<text>{{itme.m_price}}</text><view class="_br"></view>数量：<text>{{itme.count}}</text></view><view><view style="height:80px;line-height:80px;color:red;">{{"小计:"+itme.m_price*itme.count}}</view></view></view></block><view><view>总价：<text>{{"¥"+price}}</text><view class="_br"></view>{{"时间："+orderDate}}<view class="_br"></view>{{"桌号："+deskNumber+'桌'}}</view><view><button data-event-opts="{{[['tap',[['submitOrder',['$event']]]]]}}" bindtap="__e">提交订单</button></view></view></uni-popup><uni-popup class="vue-ref" vue-id="8dd740cc-2" type="bottom" data-ref="popup" bind:__l="__l" vue-slots="{{['default']}}"><view id="cartPopup"><block wx:for="{{selectMenus}}" wx:for-item="itme" wx:for-index="index" wx:key="index"><view><view><image style="width:100px;" src="{{http+upload+itme.m_img}}" mode="widthFix"></image></view><view><view style="height:40px;overflow:hidden;padding-top:10px;">{{itme.m_name}}</view>单价：<text>{{itme.m_price}}</text><view class="_br"></view></view><view><view style="width:100px;margin-top:10px;"><view style="padding-left:25px;margin-bottom:15px;">{{"总价:"+itme.m_price*itme.count}}</view><view data-event-opts="{{[['tap',[['Modified',['$0','-'],[[['selectMenus','',index]]]]]]]}}" style="float:left;width:30px;height:20px;background-color:#F8F8F8;text-align:center;" bindtap="__e">-</view><view style="float:left;width:30px;height:20px;text-align:center;">{{itme.count}}</view><view data-event-opts="{{[['tap',[['Modified',['$0','+'],[[['selectMenus','',index]]]]]]]}}" style="float:left;width:30px;height:20px;background-color:#F8F8F8;text-align:center;" bindtap="__e">+</view><view style="clear:both;"></view></view></view></view></block></view></uni-popup><view class="{{[(false||desk)?'desknone':'']}}" id="desk" data-event-opts="{{[['tap',[['ondesk',['$event']]]]]}}" bindtap="__e">[  ]</view><view id="v_type"><button type="default" id="but_type" data-event-opts="{{[['tap',[['show',['left']]]]]}}" bindtap="__e">></button><uni-drawer vue-id="8dd740cc-3" visible="{{showLeft}}" mode="left" data-event-opts="{{[['^close',[['closeDrawer',['left']]]]]}}" bind:close="__e" bind:__l="__l" vue-slots="{{['default']}}"><uni-list vue-id="{{('8dd740cc-4')+','+('8dd740cc-3')}}" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{types}}" wx:for-item="time" wx:for-index="index" wx:key="index"><uni-list-item vue-id="{{('8dd740cc-5-'+index)+','+('8dd740cc-4')}}" title="{{time.t_name}}" id="{{time.id}}" data-event-opts="{{[['^click',[['chaxun',['$0'],[[['types','',index]]]]]]]}}" bind:click="__e" bind:__l="__l"></uni-list-item></block></uni-list><view class="close"><button type="default" data-event-opts="{{[['tap',[['hide',['$event']]]]]}}" bindtap="__e">关闭</button></view></uni-drawer></view><view class="content"><uni-swiper-dot vue-id="8dd740cc-6" info="{{info}}" current="{{current}}" mode="{{mode}}" field="content" cash="cash" bind:__l="__l" vue-slots="{{['default']}}"><swiper class="swiper-box" autoplay="true" interval="3000" duration="1000" circular="true" data-event-opts="{{[['change',[['change',['$event']]]]]}}" bindchange="__e"><block wx:for="{{info}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item><view class="{{['swiper-item',item.colorClass]}}"><image src="{{item.url}}" mode="aspectFill"></image></view></swiper-item></block></swiper></uni-swiper-dot></view><uni-goods-nav class="vue-ref" style="{{(goods_nav_style)}}" vue-id="8dd740cc-7" id="goods_nav_style" fill="{{true}}" options="{{options}}" button-group="{{buttonGroup}}" data-ref="openGroup" data-event-opts="{{[['^click',[['cart']]],['^butt',[['buy']]]]}}" bind:click="__e" bind:butt="__e" bind:__l="__l"></uni-goods-nav><view style="width:100%;margin:10px 0px;"><block wx:for="{{menu}}" wx:for-item="time" wx:for-index="index" wx:key="index"><view class="menus"><view style="width:115px;"><image style="width:100px;margin:20px 5px 0px 10px;" src="{{http+upload+time.m_img}}" mode="widthFix"></image></view><view style="margin:10px 0px 0px 20px;width:110px;font-size:16px;color:#808080;"><view style="height:40px;margin-top:10px;overflow:hidden;">{{time.m_name}}</view><text style="color:red;">{{"¥"+time.m_price}}</text></view><view style="width:100px;margin-top:30px;"><yp-number-box vue-id="{{'8dd740cc-8-'+index}}" index="{{index}}" value="{{time.count}}" data-event-opts="{{[['^change',[['bindChange']]]]}}" bind:change="__e" bind:__l="__l"></yp-number-box></view></view></block></view></view></view>