<template>
    <view class="content">
        <button type="primary" @click="startSoterAuthenticationFingerPrint">开始指纹认证</button>
		<!--  @confirm="search"  -->
		<uni-search-bar radius="50" clearButton="auto"  @confirm="search" placeholder="菜名" />
		<button @click="aaa">清空</button>
		<!-- <button @click="search">提交</button> -->
    </view>
</template>

<script>
	let _self;
	import uniSearchBar from '@/components/uni-search-bar/uni-search-bar.vue'
    export default {
        data() {
            return {
            }
        },
		components:{
			uniSearchBar
		},
        onLoad() {
			_self=this;
        },
        methods: {
            startSoterAuthenticationFingerPrint() {
                uni.startSoterAuthentication({
                    requestAuthModes: ['fingerPrint'],//facial 人脸  fingerPrint指纹
                    challenge: '123456',
					
                    //authContent: '请用指纹',
                    success(res) {
                        console.log(res);
                    },
                    fail(err) {
                        console.log(err);
                    },
                    complete(res) {
                        console.log(res);
                    }
                })
            },
			search(){
				console.log(1);
			},
			aaa(){
				
			},
			bbb(e){
			}
        }
    }
</script>

<style>
    button {
        width: 200px;
        margin: 20px auto;
    }
</style>