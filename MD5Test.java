public class MD5Test {
    public static void main(String[] args) {
        // 测试常见密码的MD5值
        String[] passwords = {"123", "admin", "123456", "password", "111111", "000000"};
        
        for (String pwd : passwords) {
            String md5 = getMD5ofStr(pwd);
            System.out.println("MD5(\"" + pwd + "\") = " + md5);
            
            // 检查是否匹配数据库中的值
            if (md5.equals("bf07fd8bbc73b6f70b8319f2ebb87483")) {
                System.out.println("*** 找到匹配！admin/lisi的密码是: " + pwd + " ***");
            }
            if (md5.equals("cb571f7bd7a6f73ab004a70322b963d5")) {
                System.out.println("*** 找到匹配！zhangsan的密码是: " + pwd + " ***");
            }
        }
    }
    
    // 简化的MD5方法（基于项目中的实现）
    public static String getMD5ofStr(String inbuf) {
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(inbuf.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b & 0xff));
            }
            return sb.toString();
        } catch (Exception e) {
            return "";
        }
    }
}
