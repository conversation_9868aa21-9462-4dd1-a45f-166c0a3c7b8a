import org.apache.shiro.crypto.hash.SimpleHash;

public class MD5Test {
    public static void main(String[] args) {
        // 数据库中的实际数据
        System.out.println("=== 使用Shiro加盐MD5验证 ===");

        // admin用户：密码hash=bf07fd8bbc73b6f70b83, 盐值=uiwueylm
        String adminHash = "bf07fd8bbc73b6f70b83";
        String adminSalt = "uiwueylm";

        // zhangsan用户：密码hash=cb571f7bd7a6f73ab004, 盐值=eteokues
        String zhangsanHash = "cb571f7bd7a6f73ab004";
        String zhangsanSalt = "eteokues";

        // 常见密码列表
        String[] passwords = {"123", "admin", "123456", "password", "111111", "000000",
                             "888888", "666666", "12345", "qwerty", "abc123", "test"};

        System.out.println("\n--- 测试admin用户密码（使用Shiro方式）---");
        for (String pwd : passwords) {
            // 使用Shiro的SimpleHash进行加盐MD5加密
            SimpleHash hash = new SimpleHash("MD5", pwd, adminSalt, 1);
            String md5 = hash.toString();
            System.out.println("Shiro MD5(\"" + pwd + "\", \"" + adminSalt + "\") = " + md5);

            if (md5.startsWith(adminHash)) {
                System.out.println("*** 找到匹配！admin的密码是: " + pwd + " ***");
            }
        }

        System.out.println("\n--- 测试zhangsan用户密码（使用Shiro方式）---");
        for (String pwd : passwords) {
            // 使用Shiro的SimpleHash进行加盐MD5加密
            SimpleHash hash = new SimpleHash("MD5", pwd, zhangsanSalt, 1);
            String md5 = hash.toString();
            System.out.println("Shiro MD5(\"" + pwd + "\", \"" + zhangsanSalt + "\") = " + md5);

            if (md5.startsWith(zhangsanHash)) {
                System.out.println("*** 找到匹配！zhangsan的密码是: " + pwd + " ***");
            }
        }
    }

    // MD5加密方法
    public static String getMD5ofStr(String inbuf) {
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(inbuf.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b & 0xff));
            }
            return sb.toString();
        } catch (Exception e) {
            return "";
        }
    }
}
