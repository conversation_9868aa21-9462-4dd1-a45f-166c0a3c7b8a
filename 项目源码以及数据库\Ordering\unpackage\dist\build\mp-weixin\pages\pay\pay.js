(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/pay/pay"],{"0673":function(t,e,n){"use strict";n.r(e);var u=n("87ee"),o=n("b918");for(var i in o)"default"!==i&&function(t){n.d(e,t,function(){return o[t]})}(i);var a=n("2877"),s=Object(a["a"])(o["default"],u["a"],u["b"],!1,null,null,null);e["default"]=s.exports},"87ee":function(t,e,n){"use strict";var u=function(){var t=this,e=t.$createElement;t._self._c},o=[];n.d(e,"a",function(){return u}),n.d(e,"b",function(){return o})},"93f6":function(t,e,n){"use strict";(function(t){n("07a9"),n("921b");u(n("66fd"));var e=u(n("0673"));function u(t){return t&&t.__esModule?t:{default:t}}t(e.default)}).call(this,n("543d")["createPage"])},aa44:function(t,e,n){"use strict";(function(t){var u;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=function(){return n.e("components/uni-popup/uni-popup").then(n.bind(null,"b201"))},i=function(){return n.e("components/validCode").then(n.bind(null,"0e68"))},a={data:function(){return{amount:0,orderDate:"",deskNumber:0,selectMenus:[],affirm:!0,pwd:!1,password:"666666",result:!1,u_id:0,http:"",m_id:[],count:[]}},components:{uniPopup:o,validCode:i},methods:{pay:function(){this.$refs.popup.open()},close:function(){this.$refs.popup.close()},yesPay:function(){this.affirm=!1,this.pwd=!1,this.close(),this.fg()},clear:function(){var t=this;setTimeout(function(){t.affirm=!0,t.pwd=!1},300)},fg:function(){t.startSoterAuthentication({requestAuthModes:["fingerPrint"],challenge:"123456",authContent:"请验证指纹",success:function(t){u.result=!0,u.payment()},fail:function(e){t.showToast({title:"指纹错误！"}),u.yesPay()}})},pwds:function(){this.affirm=!1,this.pwd=!0},getPwd:function(e){this.password==e?(this.result=!0,this.payment()):(t.showToast({title:"密码错误！"}),u.pwds())},payment:function(){this.close(),console.log(this.selectMenus),t.request({method:"POST",header:{"content-type":"application/x-www-form-urlencoded"},url:this.http+"zkq/addOrder",data:{orderDate:this.orderDate,u_id:this.u_id,amount:this.amount,deskNumber:this.deskNumber,m_id:this.m_id,count:this.count},success:function(e){console.log(e.data),t.switchTab({url:"/pages/index/index"})}})}},onLoad:function(t){u=this,u.orderDate=t.orderDate,u.deskNumber=t.deskNumber,u.m_id=t.id,u.count=t.count,u.amount=t.price,u.u_id=getApp().globalData.u_id,u.http=getApp().globalData.http}};e.default=a}).call(this,n("543d")["default"])},b918:function(t,e,n){"use strict";n.r(e);var u=n("aa44"),o=n.n(u);for(var i in u)"default"!==i&&function(t){n.d(e,t,function(){return u[t]})}(i);e["default"]=o.a}},[["93f6","common/runtime","common/vendor"]]]);