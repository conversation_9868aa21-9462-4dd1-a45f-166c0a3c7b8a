{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/index/index.vue?2afd", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/index/index.vue?886a", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/index/index.vue?4869", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/index/index.vue?7b26", "uni-app:///pages/index/index.vue", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/index/index.vue?cf2c", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/index/index.vue?8018"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "upload", "orderDate", "desk", "deskNumber", "goods_nav_style", "price", "http", "menu", "selectMenus", "showLeft", "cartStatus", "types", "t_id", "info", "colorClass", "url", "content", "cash", "dotStyle", "backgroundColor", "border", "color", "selectedBackgroundColor", "selectedB<PERSON>er", "current", "mode", "options", "icon", "text", "buttonGroup", "components", "uniDrawer", "uniListItem", "uniSwiperDot", "ypNumberBox", "ypNumberBox1", "uniPopup", "uniGoodsNav", "onLoad", "_sef", "uni", "animation", "success", "ms", "methods", "submitOrder", "id", "count", "show", "hide", "closeDrawer", "confirm", "change", "chaxun", "Modified", "num", "m", "bindChange", "cou", "seMenu", "sms", "zongjia", "sum", "ondesk", "cart", "buy", "title", "duration", "onNavigationBarButtonTap", "onBackPress", "watch"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AAC+K;AAC/K,gBAAgB,wLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,8LAEN;AACP,KAAK;AACL;AACA,aAAa,oMAEN;AACP,KAAK;AACL;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,aAAa,4NAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/DA;AAAA;AAAA;AAAA;AAAipB,CAAgB,sqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4GrqB;AACA;AAAA,eACA;EACAC;IACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACAC;MACAC,QACA;QAAA;QAAAC;MAAA,EACA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,EACA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,EACA;MACAC;MACAC;MACAC,UACA;QAAAC;QACAC;MACA,GACA;QACAD;QACAC;QACAf;MACA,GACA;QACAc;QACAC;MACA,EACA;MACAC,cACA;QACAD;QACAT;QACAE;MACA;IAEA;EACA;EACAS;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IAAA;IACAC;IACAC;MACAC;IACA;IACAF;IACA;IACAC;MACAzB;MACA2B;QACA;MACA;IACA;IACA;IACAF;MACAzB;MACA2B;QACA;QACA;UACAC;QACA;QAAA;QACA;MACA;IACA;IACAH;MACAE;QACA;QACAH;MACA;IACA;EACA;EACAK;IACA;IACAC;MAEA;MACA;MACA;MACA;QACA;QACAC;QACAC;MACA;MACAP;QACAzB;MACA;IACA;IACA;IACAiC;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACA;MACA;IACA;IACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MACAb;QACAzB;QACAhB;UACAa;QACA;QACA8B;UACA;UACA;UACA;YACA;YACA;cACA;gBACAC;gBACA;cACA;gBACAA;cACA;YACA;UACA;UAAA;UACA;QACA;MACA;MACA;IACA;IACA;IACAW;MACA;MACA;QACAC;MACA;QACAA;MACA;MACA;MACA;QACA;UACAC;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;QACA;UACA;UACA;YACAC;YACA;cACA;gBACA;gBACA;cACA;gBACA;gBACA;cACA;YACA;cACAC;cACA;cACA;YACA;cACA;YACA;UACA;QACA;UACAA;UACA;UACA;QACA;MACA;QACAA;QACA;QACA;UACA;YACAC;UACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACAC;QACAP;QACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACAf;UACAC;QACA;MACA;QACA;QACAD;UACAC;QACA;QACA;QACA;MACA;IACA;IACA;IACAsB;MACA;MACAvB;QACAE;UACA;UACAH;UACAA;QACA;MACA;IACA;IACA;IACAyB;MACAzB;MACA;MACA;QACA;QACA;MACA;QACAA;QACA;QACA;UACAiB;QACA;MACA;QACA;QACA;MACA;IACA;IACA;IACAS;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAzB;UACA0B;UACAC;UACAxC;QACA;QACA;MACA;MACA;MACA;IACA;EACA;EACAyC,gEACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC,QACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxbA;AAAA;AAAA;AAAA;AAAu7B,CAAgB,i7BAAG,EAAC,C;;;;;;;;;;;ACA38B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=57280228&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniDrawer: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-drawer/uni-drawer\" */ \"@/components/uni-drawer/uni-drawer.vue\"\n      )\n    },\n    uniList: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-list/uni-list\" */ \"@/components/uni-list/uni-list.vue\"\n      )\n    },\n    uniListItem: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-list-item/uni-list-item\" */ \"@/components/uni-list-item/uni-list-item.vue\"\n      )\n    },\n    uniSwiperDot: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-swiper-dot/uni-swiper-dot\" */ \"@/components/uni-swiper-dot/uni-swiper-dot.vue\"\n      )\n    },\n    uniGoodsNav: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-goods-nav/uni-goods-nav\" */ \"@/components/uni-goods-nav/uni-goods-nav.vue\"\n      )\n    },\n    ypNumberBox: function () {\n      return import(\n        /* webpackChunkName: \"components/yp-number-box/yp-number-box\" */ \"@/components/yp-number-box/yp-number-box.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view>\r\n\t\t<uni-popup ref=\"popup1\" type=\"top\">\r\n\t\t\t<view>请确认订单：</view>\r\n\t\t\t<view v-for=\"(itme,index) in selectMenus\" :key=\"index\" class=\"anorder\">\r\n\t\t\t\t\t<view><image :src=\"http+upload+itme.m_img\" mode=\"widthFix\" style=\"width: 80px;\"></image></view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<view style=\"height: 40px;overflow: hidden;\">{{itme.m_name}}</view>单价：<text>{{itme.m_price}}</text><br/>\r\n\t\t\t\t\t\t数量：<text>{{itme.count}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<view style=\"height: 80px; line-height: 80px;color: red;\">小计:{{itme.m_price*itme.count}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view>\r\n\t\t\t\t<view>\r\n\t\t\t\t\t总价：<text>&yen;{{price}}</text><br/>时间：{{orderDate}}<br/>桌号：{{deskNumber}}桌\r\n\t\t\t\t</view>\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<button @click=\"submitOrder\">提交订单</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t\t<!-- <view :style=\"shade\" id=\"shade\"></view> -->\r\n\t\t<uni-popup ref=\"popup\" type=\"bottom\">\r\n\t\t\t<view  id=\"cartPopup\">\r\n\t\t\t\t<view v-for=\"(itme,index) in selectMenus\" :key=\"index\">\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<image :src=\"http+upload+itme.m_img\" mode=\"widthFix\" style=\"width: 100px;\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<view style=\"height: 40px;overflow: hidden;padding-top: 10px;\">{{itme.m_name}}</view>单价：<text>{{itme.m_price}}</text><br/>\r\n\t\t\t\t\t\t<!-- 数量：<text>{{itme.count}}</text> -->\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<view style=\"width: 100px;margin-top: 10px;\">\r\n\t\t\t\t\t\t\t<view style=\"padding-left: 25px;margin-bottom: 15px;\">总价:{{itme.m_price*itme.count}}</view>\r\n\t\t\t\t\t\t\t<view @click=\"Modified(itme,'-')\" style=\"float: left;width: 30px;height: 20px;background-color:  #F8F8F8;text-align: center;\">-</view>\r\n\t\t\t\t\t\t\t<view style=\"float: left;width: 30px;height: 20px;text-align: center;\">{{itme.count}}</view>\r\n\t\t\t\t\t\t\t<view @click=\"Modified(itme,'+')\"  style=\"float: left;width: 30px;height: 20px;background-color: #F8F8F8;text-align: center;\">+</view>\r\n\t\t\t\t\t\t\t<view style=\"clear: both;\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t\t<!-- {'uni-numbox--disabled': inputValue <= min || disabled} -->\r\n\t\t<!-- 'display:' \"desk ? 'block':'none' -->\r\n\t\t<view id=\"desk\" :class=\"{'desknone' : false || desk }\" @click=\"ondesk\">[&nbsp;&nbsp;]</view>\r\n\t\t<!-- 这是Type类型展示 -->\r\n\t\t<view id=\"v_type\">\r\n\t\t\t<button type=\"default\" @click=\"show('left')\" id=\"but_type\">></button>\r\n\t\t\t<uni-drawer :visible=\"showLeft\" mode=\"left\" @close=\"closeDrawer('left')\">\r\n\t\t\t\t<uni-list>\r\n\t\t\t\t<uni-list-item v-for='(time,index) in types' :key=\"index\" :title=\"time.t_name\" :id=\"time.id\" @click=\"chaxun(time)\" />\r\n\t\t\t\t</uni-list>\r\n\t\t\t\t<view class=\"close\"><button type=\"default\" @click=\"hide\">关闭</button></view>\r\n\t\t\t</uni-drawer>\r\n\t\t</view>\r\n\t\t<!-- 这个是轮播模块 -->\r\n\t\t<view class=\"content\">\r\n\t\t\t<uni-swiper-dot :info=\"info\" :current=\"current\" :mode=\"mode\" field=\"content\" cash=\"cash\" >\r\n\t\t\t\t<swiper class=\"swiper-box\" @change=\"change\"\r\n\t\t\t\tautoplay=\"true\"\r\n\t\t\t\tinterval=\"3000\"\r\n\t\t\t\tduration=\"1000\"\r\n\t\t\t\tcircular=\"true\">\r\n\t\t\t\t\t<swiper-item v-for=\"(item, index) in info\" :key=\"index\">\r\n\t\t\t\t\t\t<view :class=\"item.colorClass\" class=\"swiper-item\">\r\n\t\t\t\t\t\t\t<image :src=\"item.url\" mode=\"aspectFill\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</swiper-item>\r\n\t\t\t\t</swiper>\r\n\t\t\t</uni-swiper-dot>\r\n\t\t</view>\r\n\t\t<uni-goods-nav :style=\"goods_nav_style\" id=\"goods_nav_style\" :fill=\"true\" ref=\"openGroup\" :options=\"options\" :button-group=\"buttonGroup\"   @click=\"cart\" @butt=\"buy\" />\r\n\t\t<!-- 这个是菜品展示模块time.m_img -->\r\n\t\t<view style=\"width: 100%;margin: 10px 0px;\">\r\n\t\t\t<view v-for=\"(time,index) in menu\" :key=\"index\" class=\"menus\">\r\n\t\t\t\t<view style=\"width: 115px;\">\r\n\t\t\t\t\t<image :src=\"http+upload+time.m_img\" style=\"width: 100px;margin: 20px 5px 0px 10px;\" mode=\"widthFix\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"margin:10px 0px 0px 20px; width: 110px;font-size: 16px;color: #808080;\">\r\n\t\t\t\t\t<view style=\"height: 40px;margin-top: 10px;overflow: hidden;\">{{time.m_name}}</view>\r\n\t\t\t\t\t<text style=\"color: red;\">&yen;{{time.m_price}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"width: 100px;margin-top: 30px;\">\r\n\t\t\t\t\t<yp-number-box @change=\"bindChange\" :index=\"index\" :value=\"time.count\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport uniDrawer from '../../components/uni-drawer/uni-drawer.vue'\r\n\timport uniListItem from '../../components/uni-list-item/uni-list-item.vue'//drawer列表数据\r\n\t//轮播\r\n\timport uniSwiperDot from '@/components/uni-swiper-dot/uni-swiper-dot.vue'\r\n\timport ypNumberBox  from \"@/components/yp-number-box/yp-number-box.vue\"\r\n\timport ypNumberBox1  from \"@/components/yp-number-box/yp-number-box1.vue\"\r\n\t//Popup弹出框\r\n\timport uniPopup from \"@/components/uni-popup/uni-popup.vue\"\r\n\t//购物车\r\n\timport uniGoodsNav from \"@/components/uni-goods-nav/uni-goods-nav.vue\"\r\n\tlet _sef;\r\n\tlet num = 0;\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tupload :'upload/',\r\n\t\t\t\t//订单时间\r\n\t\t\t\torderDate:\"\",\r\n\t\t\t\t//是否显示扫一扫\r\n\t\t\t\tdesk:false,\r\n\t\t\t\t//桌号\r\n\t\t\t\tdeskNumber:0,\r\n\t\t\t\t//判断手机高度\r\n\t\t\t\tgoods_nav_style:\"margin-top:405px;\",\r\n\t\t\t\t//总价\r\n\t\t\t\tprice:0,\r\n\t\t\t\t//请求地址\r\n\t\t\t\thttp:\"\",\r\n\t\t\t\t//菜单\r\n\t\t\t\tmenu:[],\r\n\t\t\t\t//已选菜单\r\n\t\t\t\tselectMenus:[],\r\n\t\t\t\t//是否显示类型\r\n\t\t\t\tshowLeft: false,\r\n\t\t\t\tcartStatus:true,\r\n\t\t\t\ttypes:[\r\n\t\t\t\t\t{\"t_name\":'全部',t_id:0}\r\n\t\t\t\t],\r\n\t\t\t\t//轮播内容\r\n\t\t\t\tinfo: [{\r\n\t\t\t\t\t\tcolorClass: 'uni-bg-red',\r\n\t\t\t\t\t\turl: 'http://pic.qjimage.com/chineseview116/high/482-10992.jpg',\r\n\t\t\t\t\t\tcontent: '猪脚',\r\n\t\t\t\t\t\tcash:'89.00'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tcolorClass: 'uni-bg-green',\r\n\t\t\t\t\t\turl: 'http://images.quanjing.com/chineseview116/high/482-10610.jpg',\r\n\t\t\t\t\t\tcontent: '口水鸡',\r\n\t\t\t\t\t\tcash:'88.80'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tcolorClass: 'uni-bg-blue',\r\n\t\t\t\t\t\turl: 'http://img1.juimg.com/161225/335318-161225154Z323.jpg',\r\n\t\t\t\t\t\tcontent: '山药银杏果份',\r\n\t\t\t\t\t\tcash:'35.80'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\t//轮播样式\r\n\t\t\t\tdotStyle: [{\r\n\t\t\t\t\t\tbackgroundColor: 'rgba(0, 0, 0, .3)',\r\n\t\t\t\t\t\tborder: '1px rgba(0, 0, 0, .3) solid',\r\n\t\t\t\t\t\tcolor: '#fff',\r\n\t\t\t\t\t\tselectedBackgroundColor: 'rgba(0, 0, 0, .9)',\r\n\t\t\t\t\t\tselectedBorder: '1px rgba(0, 0, 0, .9) solid'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tcurrent: 0,\r\n\t\t\t\tmode: 'nav',\r\n\t\t\t\toptions: [\r\n\t\t\t\t\t{icon: '/static/qingkong.png',\r\n\t\t\t\t    text: '清空购物车'\r\n\t\t\t\t   },\r\n\t\t\t\t\t{\r\n\t\t\t\t     icon: 'https://img-cdn-qiniu.dcloud.net.cn/uniapp/uni-ui/goodsnav/carts.png',\r\n\t\t\t\t     text: '购物车',\r\n\t\t\t\t     info: 0\r\n\t\t\t\t   },\r\n\t\t\t\t\t{\r\n\t\t\t\t\ticon: '/static/Money.png',\r\n\t\t\t\t\ttext: '0',\r\n\t\t\t\t\t}\r\n\t\t\t\t\t],\r\n\t\t\t\tbuttonGroup: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\ttext: '立即购买',\r\n\t\t\t\t\tbackgroundColor: '#ffa200',\r\n\t\t\t\t\tcolor: '#fff'\r\n\t\t\t\t\t}\r\n\t\t\t\t]\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomponents: {\r\n\t\t\tuniDrawer,\r\n\t\t\tuniListItem,\r\n\t\t\tuniSwiperDot,\r\n\t\t\typNumberBox,\r\n\t\t\typNumberBox1,\r\n\t\t\tuniPopup,\r\n\t\t\tuniGoodsNav\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t_sef = this;\r\n\t\t\tuni.showTabBar({\r\n\t\t\t\tanimation:true,\r\n\t\t\t});\r\n\t\t\t_sef.http = getApp().globalData.http;\r\n\t\t\t//这个是菜品类型的展示\r\n\t\t\tuni.request({\r\n\t\t\t\turl:this.http+\"/zkq/types\",\r\n\t\t\t\tsuccess:(vel)=>{\r\n\t\t\t\t\tthis.types = this.types.concat(vel.data);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t//这个是所有菜品的展示\r\n\t\t\tuni.request({\r\n\t\t\t\turl:this.http+\"/zkq/menus\",\r\n\t\t\t\tsuccess:(vel)=>{\r\n\t\t\t\t\tlet ms = vel.data;\r\n\t\t\t\t\tfor(let m in ms){\r\n\t\t\t\t\t\tms[m].count = 0;\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthis.menu = ms\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\tuni.getSystemInfo({\r\n\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\tlet px = Number(res.windowHeight)-Number(148);\r\n\t\t\t\t\t_sef.goods_nav_style = \"margin-top:\"+px+\"px;\";\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t//submitOrder 提交订单\r\n\t\t\tsubmitOrder(){\r\n\r\n\t\t\t\tlet count = new Array();\r\n\t\t\t\tlet id = new Array();\r\n\t\t\t\tlet sm = this.selectMenus;\r\n\t\t\t\tfor(let i in sm){\r\n\t\t\t\t\t//console.log(sm[i].m_id);\r\n\t\t\t\t\tid.push(sm[i].m_id);\r\n\t\t\t\t\tcount.push(sm[i].count);\r\n\t\t\t\t}\r\n\t\t\t\tuni.redirectTo({\r\n\t\t\t\t    url: '../pay/pay?orderDate='+this.orderDate+\"&deskNumber=\"+this.deskNumber+\"&id=\"+id+\"&count=\"+count+\"&price=\"+this.price\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//显示分类\r\n\t\t\tshow(e) {\r\n\t\t\t\tif (e === 'left') {\r\n\t\t\t\t\tthis.showLeft = true\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//隐藏分类\r\n\t\t\thide() {\r\n\t\t\t\tthis.showLeft = false\r\n\t\t\t},\r\n\t\t\tcloseDrawer(e) {\r\n\t\t\t\tif (e === 'left') {\r\n\t\t\t\t\tthis.showLeft = false\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tconfirm() {},\r\n\t\t\t//轮播图片编号\r\n\t\t\tchange(e) {\r\n\t\t\t\tthis.current = e.detail.current\r\n\t\t\t},\r\n\t\t\t//分类查询\r\n\t\t\tchaxun(type){\r\n\t\t\t\tuni.request({\r\n\t\t\t\t\turl:this.http+\"/zkq/menus\",\r\n\t\t\t\t\tdata:{\r\n\t\t\t\t\t\tt_id:type.t_id\r\n\t\t\t\t\t},\r\n\t\t\t\t\tsuccess:(vel)=>{\r\n\t\t\t\t\t\tlet ms = vel.data;\r\n\t\t\t\t\t\tlet sms = _sef.selectMenus;\r\n\t\t\t\t\t\tfor(let m in ms){\r\n\t\t\t\t\t\t\t//ms[m].count = 0;\r\n\t\t\t\t\t\t\tfor(let s in sms){\r\n\t\t\t\t\t\t\t\tif(ms[m].m_id === sms[s].m_id){\r\n\t\t\t\t\t\t\t\t\tms[m].count = sms[s].count;\r\n\t\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\t\t}else if(s === sms.length-1){\r\n\t\t\t\t\t\t\t\t\tms[m].count = 0;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tthis.menu = ms\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\tthis.hide();\r\n\t\t\t},\r\n\t\t\t//购物车加减时触发\r\n\t\t\tModified(val,fh){\r\n\t\t\t\t let num = val.count;\r\n\t\t\t\t if(fh===\"-\"){\r\n\t\t\t\t\t num--;\r\n\t\t\t\t }else{\r\n\t\t\t\t\t num++;\r\n\t\t\t\t }\r\n\t\t\t\tlet m = this.menu;\r\n\t\t\t\tfor(let i in m){\r\n\t\t\t\t\tif(m[i].m_id === val.m_id){\r\n\t\t\t\t\t\tm[i].count = num;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//输入框改变时出发 加入购物车和购物车的加减\r\n\t\t\tbindChange(val){\r\n\t\t\t\tlet m_index = val[0];\r\n\t\t\t\t//改变时的数量\r\n\t\t\t\tlet m_count = val[1];\r\n\t\t\t\tlet seMenu = this.menu[m_index];\r\n\t\t\t\tif(m_count>0){\r\n\t\t\t\t\tif(this.selectMenus.length>0){\r\n\t\t\t\t\t\tlet cou = 0;\r\n\t\t\t\t\t\tfor(let i in this.selectMenus){\r\n\t\t\t\t\t\t\tcou++;\r\n\t\t\t\t\t\t\tif(this.selectMenus[i].m_id == seMenu.m_id){\r\n\t\t\t\t\t\t\t\tif(m_count == 0){\r\n\t\t\t\t\t\t\t\t\tthis.selectMenus.splice(i,1);\r\n\t\t\t\t\t\t\t\t\treturn this.zongjia();\r\n\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\tthis.selectMenus[i].count = m_count;\r\n\t\t\t\t\t\t\t\t\treturn this.zongjia();\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}else if(this.selectMenus.length == cou){\r\n\t\t\t\t\t\t\t\tseMenu.count = m_count;\r\n\t\t\t\t\t\t\t\tthis.selectMenus.push(seMenu);\r\n\t\t\t\t\t\t\t\treturn this.zongjia();\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tseMenu.count = m_count;\r\n\t\t\t\t\t\tthis.selectMenus.push(seMenu);\r\n\t\t\t\t\t\treturn this.zongjia();\r\n\t\t\t\t\t}\r\n\t\t\t\t}else{\r\n\t\t\t\t\tseMenu.count = 0;\r\n\t\t\t\t\tlet sms = this.selectMenus;\r\n\t\t\t\t\tfor(let i in sms){\r\n\t\t\t\t\t\tif(sms[i].m_id === seMenu.m_id){\r\n\t\t\t\t\t\t\tsms.splice(i,1);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn this.zongjia();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//总价格\r\n\t\t\tzongjia(){\r\n\t\t\t\tthis.price = 0;\r\n\t\t\t\tlet num = 0;\r\n\t\t\t\tlet sum = 0;\r\n\t\t\t\tfor(let i in this.selectMenus){\r\n\t\t\t\t\tsum += Number(this.selectMenus[i].count);\r\n\t\t\t\t\tnum = this.selectMenus[i].m_price * this.selectMenus[i].count;\r\n\t\t\t\t\tthis.price += num;\r\n\t\t\t\t}\r\n\t\t\t\tthis.options[1].info = sum;\r\n\t\t\t\tthis.options[2].text=this.price;\r\n\t\t\t\tif(this.price==0){\r\n\t\t\t\t\t//关闭购物车弹出框\r\n\t\t\t\t\tthis.$refs.popup.close();\r\n\t\t\t\t\tthis.$refs.openGroup.close()\r\n\t\t\t\t\t//显示TabBar\r\n\t\t\t\t\tuni.showTabBar({\r\n\t\t\t\t\t\tanimation:true,\r\n\t\t\t\t\t});\r\n\t\t\t\t}else{\r\n\t\t\t\t\t//关闭TabBar\r\n\t\t\t\t\tuni.hideTabBar({\r\n\t\t\t\t\t\tanimation:true,\r\n\t\t\t\t\t});\r\n\t\t\t\t\t//打购物车开弹出框 goods_nav_style\r\n\t\t\t\t\tthis.$refs.openGroup.open();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//点击扫码\r\n\t\t\tondesk(val){\r\n\t\t\t\t// 允许从相机和相册扫码\r\n\t\t\t\tuni.scanCode({\r\n\t\t\t\t    success: function (res) {\r\n\t\t\t\t\t\t//this.deskNumber = Number(res.result);\r\n\t\t\t\t\t\t_sef.deskNumber = Number(res.result);\r\n\t\t\t\t\t\t_sef.desk = true;\r\n\t\t\t\t    }\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//购物车\r\n\t\t\tcart(e) {\r\n\t\t\t\t_sef.hide();\r\n\t\t\t\tthis.$refs.popup1.close();\r\n\t\t\t\tif(e.index==1 && this.cartStatus){\r\n\t\t\t\t\tthis.$refs.popup.open();\r\n\t\t\t\t\tthis.cartStatus = false;\r\n\t\t\t\t}else if(e.index==0){\r\n\t\t\t\t\t_sef.selectMenus=[];\r\n\t\t\t\t\tvar m = _sef.menu;\r\n\t\t\t\t\tfor (let i in m) {\r\n\t\t\t\t\t\tm[i].count = 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.cartStatus = true;\r\n\t\t\t\t\tthis.$refs.popup.close();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//立即购买\r\n\t\t\tbuy(e) {\r\n\t\t\t\tlet date = new Date();\r\n\t\t\t\tlet year = date.getFullYear();       //年\r\n\t\t\t\tlet month = date.getMonth() + 1;     //月\r\n\t\t\t\tlet day = date.getDate();\t\t\t//日\r\n\t\t\t\tlet hh = date.getHours();            //时\r\n\t\t\t\tlet mm = date.getMinutes();          //分\r\n\t\t\t\tlet ss = date.getSeconds();\r\n\t\t\t\tthis.orderDate = year+\"-\"+month+\"-\"+day+\" \"+hh+\":\"+mm+\":\"+ss;\r\n\t\t\t\tif(_sef.deskNumber == '' || _sef.deskNumber <= 0){\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t    title: '你还没有扫描桌号！',\r\n\t\t\t\t\t    duration: 2000,\r\n\t\t\t\t\t\ticon:'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.$refs.popup.close();\r\n\t\t\t\tthis.$refs.popup1.open();\r\n\t\t\t}\r\n\t\t},\r\n\t\tonNavigationBarButtonTap(e) {\r\n\t\t},\r\n\t\tonBackPress() {\r\n\t\t\tif (this.showLeft) {\r\n\t\t\t\tthis.hide()\r\n\t\t\t\treturn true\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch:{\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.anorder{\r\n\t\twidth: 100%;\r\n\t\theight: 100px;\r\n\t\tmargin: 10px;\r\n\t\tfont-size: 16px;\r\n\r\n\t}\r\n\t.anorder>view{\r\n\t\twidth: 30%;\r\n\t\tfloat: left;\r\n\t}\r\n\t.desknone{\r\n\t\tdisplay: none;\r\n\t}\r\n\t#cartPopup>view{\r\n\t\tmargin: 10px auto;\r\n\t\tclear: both;\r\n\t\tcolor: #808080;\r\n\t\tfont-size: 16px;\r\n\t\theight: 100px;\r\n\t}\r\n\t#cartPopup>view>view{\r\n\t\tfloat: left;\r\n\t\twidth: 30%;\r\n\t\tmargin-left: 5px;\r\n\t}\r\n\t#apps{\r\n\t\tdisplay: flex;\r\n\t}\r\n\t#goods_nav_style{\r\n\t\tborder: #00BFFF;z-index: 999;position: fixed;width: 100%;\r\n\t}\r\n\t#desk{\r\n\t\tposition: fixed;\r\n\t\tright: 0px;\r\n\t\tz-index: 2;\r\n\t\twidth: 40px;\r\n\t\theight: 40px;\r\n\t\tbackground-color: #DEDEDE;\r\n\t\tline-height: 40px;\r\n\t\ttext-align: center;\r\n\t\toverflow: hidden;\r\n\t\tborder-radius: 5px;\r\n\t\topacity:0.5;\r\n\t}\r\n\t#but_type{\r\n\t\tposition: fixed;\r\n\t\twidth: 40px;\r\n\t\tz-index: 2;\r\n\t\tcolor: gray;\r\n\t\topacity:0.5;\r\n\t}\r\n\t#v_type{\r\n\r\n\t}\r\n\t.swiper-item {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\theight: 100%;\r\n\t\tbackground: #eee;\r\n\t\tcolor: #fff;\r\n\t}\r\n\t.swiper-item image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\t/* 菜单 */\r\n\t.menus{\r\n\t\twidth: 100%;\r\n\t\theight: 100px;\r\n\t\toverflow: hidden;\r\n\r\n\t\t/* border: #000000 1px solid; */\r\n\t}\r\n\t.menus>view{\r\n\t\tfloat: left;\r\n\t}\r\n\t.butCount{\r\n\t\twidth: 10xp;\r\n\t\theight: 10xp;\r\n\t}\r\n\t#shade{\r\n\t\twidth: 100%;height: 100%;background-color: #00BFFF;opacity: 0.3;position: fixed;\r\n\t}\r\n\t.uni-tab__cart-box{\r\n\t}\r\n</style>", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1748248725299\n      var cssReload = require(\"E:/software/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}