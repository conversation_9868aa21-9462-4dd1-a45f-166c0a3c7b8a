{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/order/order.vue?6240", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/order/order.vue?7ab1", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/order/order.vue?cde2", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/order/order.vue?808f", "uni-app:///pages/order/order.vue", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/order/order.vue?6c58", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/order/order.vue?2321"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "showImg", "upload", "orders", "u_id", "http", "onLoad", "_self", "methods", "getOrders", "uni", "url", "method", "uid", "success", "date", "toDetails"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AAC+K;AAC/K,gBAAgB,wLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAipB,CAAgB,sqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmBrqB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;IACAA;IACAA;IACA;EACA;EACAC;IACAC;MACAC;QACAC;QACAC;QACAZ;UACAa;QACA;QACAC;UACA;UACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACAC;UACA;UACAR;QACA;MACA;IACA;IACAS;MACAN;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClEA;AAAA;AAAA;AAAA;AAAu7B,CAAgB,i7BAAG,EAAC,C;;;;;;;;;;;ACA38B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/order/order.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/order/order.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./order.vue?vue&type=template&id=127632e4&\"\nvar renderjs\nimport script from \"./order.vue?vue&type=script&lang=js&\"\nexport * from \"./order.vue?vue&type=script&lang=js&\"\nimport style0 from \"./order.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/order/order.vue\"\nexport default component.exports", "export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order.vue?vue&type=template&id=127632e4&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view style=\"flex-wrap: wrap;\">\r\n\t\t\r\n\t\t<view class=\"orders-list\" v-for=\"(item,index) in orders\" :key=\"index\" @click=\"toDetails(item.o_id)\">\r\n\t\t\t<view class=\"uni-media-list-logo\">\r\n\t\t\t    <image v-if=\"showImg\" :src=\"http+upload+item.mimg\" style=\"width: 60px;\" mode=\"widthFix\"></image>\r\n\t\t    </view> \r\n\t\t\t<view class=\"uni-media-list-body\">\r\n\t\t\t    <!-- <view class=\"uni-media-list-text-top\">{{item.mname}}<span class=\"time\">{{item.o_time}}</span></view> -->\r\n\t\t\t    <view class=\"uni-media-list-text-top\">下单时间:{{item.o_time}}</view>\r\n\t\t\t\t<view class=\"uni-media-list-text-bottom uni-ellipsis\">{{item.o_deskNum}}号桌<span class=\"sumprice\">总价:{{item.o_totalprice}}￥</span></view>\r\n\t\t\t</view>\r\n\t\t\t \r\n\t\t</view>\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar _self;\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tshowImg:true,\r\n\t\t\t\tupload :'upload/',\r\n\t\t\t\torders:[],\r\n\t\t\t\tu_id : 0,\r\n\t\t\t\thttp:''\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad:function(e){\r\n\t\t\t_self=this;\r\n\t\t\t_self.uid = getApp().globalData.u_id;\r\n\t\t\t_self.http = getApp().globalData.http;\r\n\t\t\tthis.getOrders();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetOrders:function(){\r\n\t\t\t\tuni.request({\r\n\t\t\t\t\turl: _self.http+'item/ItemList',\r\n\t\t\t\t\tmethod:'GET',\r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t        uid:_self.uid\r\n\t\t\t\t\t    },\r\n\t\t\t\t\t success: (res) => {\r\n\t\t\t\t\t\t\tvar date=res.data;\r\n\t\t\t\t\t\t\tfor (let i in date) {\r\n\t\t\t\t\t\t\t let d = new Date(date[i].o_time);\r\n\t\t\t\t\t\t\t let year = d.getFullYear();        \r\n\t\t\t\t\t\t\t let month = d.getMonth() + 1;      \r\n\t\t\t\t\t\t\t let day = d.getDate();\t\t\t \r\n\t\t\t\t\t\t\t let hh = d.getHours();            \r\n\t\t\t\t\t\t\t let mm = d.getMinutes();           \r\n\t\t\t\t\t\t\t let ss = d.getSeconds();\r\n\t\t\t\t\t\t\t date[i].o_time = year+\"-\"+month+\"-\"+day+\" \"+hh+\":\"+mm+\":\"+ss;\r\n\t\t\t\t\t\t\t }\r\n\t\t\t\t\t\t\t_self.orders=res.data;\r\n\t\t\t\t\t    }\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\ttoDetails(id){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t    url: 'scroll/scroll?u_id='+id,\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\nview{\r\n\twidth: 100%;\r\n\t}\r\n.orders-list{\r\ndisplay: flex;width: 94%;\r\npadding:20rpx 3% ;\r\nflex-wrap: nowrap;\r\nmargin:30rpx 0;\r\nbackground: linear-gradient(to right,rgba(0,0,0,.01),#fff);\r\n\r\nbox-shadow:0px 2px 2px -2px rgba(0,0,0,.3);\r\n }\r\n/* .time{\r\n        float: right;\r\n\t\tcolor: #939393;\r\n        font-size: 12px;\r\n} */\r\n.sumprice{\r\n\tfloat: right;\r\n\tcolor: #DD524D;\r\n\tfont-size:15px;\r\n}\r\n.uni-media-list-logo{\r\n        width: 60px;\r\n        height: 60px;\r\n\t\tpadding:0px 15px;\r\n}\r\n.uni-media-list-logo uni_image{\r\n\t\r\n        border-radius: 5px;\r\n}\r\n.uni-media-list-text-top{\r\n\t   font-size: 16px;\r\n\t\t\r\n}\r\n.uni-media-list-text-bottom{\r\n\t\r\n        line-height: 14px;\r\n        font-size: 14px;\r\n        width: 85%;\r\n\t\t/* padding:30rpx 0;\r\n\t\theight: 20px; */\r\n}\r\n.uni-media-list-body{\r\n\t\r\n        height: 45px;\r\n}\r\n.content{\r\n\t\r\n}\r\n</style>\r\n", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749718152315\n      var cssReload = require(\"E:/software/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}