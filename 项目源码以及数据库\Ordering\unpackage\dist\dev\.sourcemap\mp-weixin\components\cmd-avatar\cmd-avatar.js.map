{"version": 3, "sources": ["webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/cmd-avatar/cmd-avatar.vue?6345", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/cmd-avatar/cmd-avatar.vue?c3f3", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/cmd-avatar/cmd-avatar.vue?12bc", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/cmd-avatar/cmd-avatar.vue?cc9d", "uni-app:///components/cmd-avatar/cmd-avatar.vue", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/cmd-avatar/cmd-avatar.vue?69d0", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/cmd-avatar/cmd-avatar.vue?c0cb"], "names": ["name", "components", "cmdIcon", "props", "size", "validator", "default", "shape", "make", "type", "src", "icon", "text", "computed", "setShapeSizeClass", "classList", "setNumSizeStyle", "setIconTextStyle", "styleString", "setIconSize", "methods", "$_imageLoad", "$_imageError", "$_click"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AAC+K;AAC/K,gBAAgB,wLAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAspB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACW1qB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA,gBAeA;EACAA;EAEAC;IACAC;EACA;EAEAC;IACA;AACA;AACA;IACAC;MACAC;QACA;MACA;MACAC;IACA;IACA;AACA;AACA;IACAC;MACAF;QAAA;MAAA;MACAC;IACA;IACA;AACA;AACA;IACAE;MACAC;MACAH;QACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAI;MACAD;MACAH;IACA;IACA;AACA;AACA;IACAK;MACAF;MACAH;IACA;IACA;AACA;AACA;IACAM;MACAH;MACAH;IACA;EACA;EAEAO;IACA;IACAC;MACA;MACA;QACAC;MACA;MACA;QACAA;MACA;MACA;IACA;IACA;IACAC;MACA,uCACA,qDACA,wCACA,8CACA;IACA;IACA;IACAC;MACA;MACA;QACAC;MACA;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC7IA;AAAA;AAAA;AAAA;AAA47B,CAAgB,s7BAAG,EAAC,C;;;;;;;;;;;ACAh9B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/cmd-avatar/cmd-avatar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./cmd-avatar.vue?vue&type=template&id=5c9fac44&\"\nvar renderjs\nimport script from \"./cmd-avatar.vue?vue&type=script&lang=js&\"\nexport * from \"./cmd-avatar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./cmd-avatar.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/cmd-avatar/cmd-avatar.vue\"\nexport default component.exports", "export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cmd-avatar.vue?vue&type=template&id=5c9fac44&\"", "var components\ntry {\n  components = {\n    cmdIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/cmd-icon/cmd-icon\" */ \"@/components/cmd-icon/cmd-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cmd-avatar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cmd-avatar.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"cmd-avatar\" :class=\"setShapeSizeClass\" :style=\"setIconTextStyle+setNumSizeStyle\" @tap=\"$_click\">\r\n\t\t<image v-if=\"src\" class=\"cmd-avatar-img\" mode=\"aspectFit\" :src=\"src\" @load=\"$_imageLoad\" @error=\"$_imageError\"></image>\r\n\t\t<cmd-icon v-if=\"icon && !src && !text\" :type=\"icon\" :size=\"setIconSize\" :color=\"make.color\"></cmd-icon>\r\n\t\t<text v-if=\"text && !src && !icon\" v-text=\"text\"></text>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport cmdIcon from '../cmd-icon/cmd-icon.vue'\r\n\r\n\t/**  \r\n\t * 头像组件  \r\n\t * @description 用于展示用户头像、数字号、单字符。  \r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=176  \r\n\t * @property {String,Number} text 头像显示文字 - 建议取首位text[0]，长度小于3位  \r\n\t * @property {String} icon 头像显示图标icon - 列如：home、add  \r\n\t * @property {String} src 头像图片地址 - 仅支持相对路径、绝对路径，支持 base64 码  \r\n\t * @property {String,Number} size 头像显示大小 - sm，lg，md，任意Number值  \r\n\t * @property {String} shape 头像形状 - 圆形：circle，矩形：square  \r\n\t * @property {Object} make 自定义头像样式 - 颜色、背景、边距，列如：{'margin-right': '8px'}  \r\n\t * @event {Function} click 头像组件点击事件  \r\n\t * @event {Function} load 图片载入成功事件  \r\n\t * @event {Function} error 图片载入错误事件  \r\n\t * @example <cmd-avatar text='CMD'></cmd-avatar>  \r\n\t */\r\n\texport default {\r\n\t\tname: 'cmd-avatar',\r\n\r\n\t\tcomponents: {\r\n\t\t\tcmdIcon\r\n\t\t},\r\n\r\n\t\tprops: {\r\n\t\t\t/**\r\n\t\t\t * 大小 sm md lg Number\r\n\t\t\t */\r\n\t\t\tsize: {\r\n\t\t\t\tvalidator: val => {\r\n\t\t\t\t\treturn typeof val === 'number' || ['sm', 'lg', 'md'].includes(val);\r\n\t\t\t\t},\r\n\t\t\t\tdefault: 'md'\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 形状 circle圆形 square矩形\r\n\t\t\t */\r\n\t\t\tshape: {\r\n\t\t\t\tvalidator: val => ['circle', 'square'].includes(val),\r\n\t\t\t\tdefault: 'circle'\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 样式属性style\r\n\t\t\t */\r\n\t\t\tmake: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: () => {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\t'color': ''\r\n\t\t\t\t\t};\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 图片网络地址\r\n\t\t\t */\r\n\t\t\tsrc: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 图标type名称\r\n\t\t\t */\r\n\t\t\ticon: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 文本名字建议取首位text[0],长度小于3位\r\n\t\t\t */\r\n\t\t\ttext: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: ''\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tcomputed: {\r\n\t\t\t// 形状大小\r\n\t\t\tsetShapeSizeClass() {\r\n\t\t\t\tlet classList = []\r\n\t\t\t\tif (['circle', 'square'].includes(this.shape)) {\r\n\t\t\t\t\tclassList.push(`cmd-avatar-${this.shape}`)\r\n\t\t\t\t}\r\n\t\t\t\tif (['sm', 'lg', 'md'].includes(this.size)) {\r\n\t\t\t\t\tclassList.push(`cmd-avatar-${this.size}`)\r\n\t\t\t\t}\r\n\t\t\t\treturn classList\r\n\t\t\t},\r\n\t\t\t// 自定义大小\r\n\t\t\tsetNumSizeStyle() { \r\n\t\t\t\treturn typeof this.size === 'number' ?\r\n\t\t\t\t\t`width:${this.size}px;` +\r\n\t\t\t\t\t`height:${this.size}px;` +\r\n\t\t\t\t\t`font-size:${(this.size / 2)}px;` +\r\n\t\t\t\t\t`line-height:${this.size}px;` :'';\r\n\t\t\t},\r\n\t\t\t// 图标文本样式\r\n\t\t\tsetIconTextStyle() {\r\n\t\t\t\tlet styleString = '';\r\n\t\t\t\tfor (let it in this.make) {\r\n\t\t\t\t\tstyleString += `${it}:${this.make[it]};`;\r\n\t\t\t\t}\r\n\t\t\t\treturn styleString;\r\n\t\t\t},\r\n\t\t\t// 图标大小\r\n\t\t\tsetIconSize() {\r\n\t\t\t\tif (typeof this.size === 'number') {\r\n\t\t\t\t\treturn this.size / 2;\r\n\t\t\t\t}\r\n\t\t\t\tif (this.size === 'sm') {\r\n\t\t\t\t\treturn uni.upx2px(64) / 2;\r\n\t\t\t\t}\r\n\t\t\t\tif (this.size === 'lg') {\r\n\t\t\t\t\treturn uni.upx2px(128) / 2;\r\n\t\t\t\t}\r\n\t\t\t\treturn uni.upx2px(96) / 2;\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\t// 当图片载入完毕时\r\n\t\t\t$_imageLoad(e) {\r\n\t\t\t\tthis.$emit('load', e);\r\n\t\t\t},\r\n\t\t\t// 当错误发生时\r\n\t\t\t$_imageError(e) {\r\n\t\t\t\tthis.$emit('error', e);\r\n\t\t\t},\r\n\t\t\t// 点击事件\r\n\t\t\t$_click(e) {\r\n\t\t\t\tthis.$emit('click', e)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.cmd-avatar {\r\n\t\tcolor: #fff;\r\n\t\ttext-align: center;\r\n\t\tborder-radius: 6upx;\r\n\t\tbackground: #eee;\r\n\t\tbox-shadow: 0 2px 1px -1px rgba(0, 0, 0, .2), 0 1px 1px 0 rgba(0, 0, 0, .14), 0 1px 3px 0 rgba(0, 0, 0, .12);\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.cmd-avatar-img {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.cmd-avatar-lg {\r\n\t\twidth: 128upx;\r\n\t\theight: 128upx;\r\n\t\tfont-size: 64upx;\r\n\t\tline-height: 128upx;\r\n\t}\r\n\r\n\t.cmd-avatar-sm {\r\n\t\twidth: 64upx;\r\n\t\theight: 64upx;\r\n\t\tfont-size: 32upx;\r\n\t\tline-height: 64upx;\r\n\t}\r\n\r\n\t.cmd-avatar-md {\r\n\t\twidth: 96upx;\r\n\t\theight: 96upx;\r\n\t\tfont-size: 48upx;\r\n\t\tline-height: 96upx;\r\n\t}\r\n\r\n\t.cmd-avatar-square {\r\n\t\tborder-radius: 6upx;\r\n\t\tbackground-clip: border-box;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.cmd-avatar-circle {\r\n\t\tborder-radius: 50%;\r\n\t\tbackground-clip: border-box;\r\n\t\toverflow: hidden;\r\n\t}\r\n</style>\n", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cmd-avatar.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cmd-avatar.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1748248725420\n      var cssReload = require(\"E:/software/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}