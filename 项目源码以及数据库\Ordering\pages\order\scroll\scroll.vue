<template>
	<view>
		<!-- requesting-加载中  emptyShow-控制空状态的显示   end-加载完毕  refreshSize-头部下拉是的高度
		 @refresh -下拉刷新时间 raise-是否显示评论按钮

		-->
		<scroll id="scroll"

				:requesting="requesting"
				:empty-show="emptyShow"
				:end="end"
				:listCount="listCount"
				@refresh="refresh"
				refreshSize="200"
				@evaluate="evaluate"
				:raise="raise"
				>
			<view class="cells">
				<view class="cell"
					v-for="(item, index) in listData" :key="index">
					<view class="cell__hd">
						<image mode="aspectFill" :src="http+upload+item.m_img" :alt="item.m_name"/>
					</view>
					<view class="cell__bd">
						<view class="name">{{item.m_name}}</view>
						<view class="des">数量:{{item.l_count}}</view>
						<view class="name">单价:{{item.l_price}}</view>
					</view>
					<view  class="cell__bd">
						<view class="zong">总价:{{item.l_sum}}</view>
					</view>
					<view  class="cell__bd">
						<view class="zong" v-if="item.l_state == 0">待上菜</view>
						<view class="zong" v-else>交易完成</view>
					</view>
				</view>
			</view>
		</scroll>
		<!-- <view></view> -->
		<!-- <button id="button_evaluate">对本次服务进行评价</button> -->
	</view>
</template>

<script>
	let pageStart = 0;
	let pageSize = 15;
	//当前页面
	let _self;

	let testData = [];//
	//插件模块的导入
	import scroll from "../../../components/scroll/scroll.vue";
	export default {
		components: { scroll },
		data() {
			return {
				upload:'upload/',
				// 加载中
				requesting: false,
				// 加载完毕
				end: false,
				// 控制空状态的显示
				emptyShow: false,
				//页面
				page: pageStart,
				listData: [],
				//订单的id
				o_id:0,
				//当前列表长度
				listCount: 0,
				//请求路径
				http: '',
				//是否显示评价
				raise:false
			};
		},
		onLoad(option){
			_self = this;
			//加载时获取总配置的请求路径
			_self.http = getApp().globalData.http;
			//获取订单id
			_self.o_id = option.u_id;
			//判断是否显示按钮
			_self.getEvaluate();
			//下拉刷新
			this.req();
			//请求后台数据
			this.getList('refresh', pageStart);

		},
		methods: {
			//跳转服务评论
			evaluate(){
				uni.navigateTo({
				    url: './publish/publish?o_id='+_self.o_id
				});
			},
			//判断是否显示按钮
			getEvaluate(){
				uni.request({
				    url: this.http+'zyg/evaluate', //仅为示例，并非真实接口地址。
				    data: {
				        o_id: _self.o_id
				    },
				    success: (res) => {
						_self.raise = res.data;
				    }
				});
			},
			//这是显示调用的函数
			getList(type, currentPage) {
				this.requesting = true;
				wx.showNavigationBarLoading();
				setTimeout(() => {
					this.requesting = false;
					this.end = false;
					wx.hideNavigationBarLoading();
					if (type === 'refresh') {
						this.listData = testData;
						this.page = currentPage + 1;
					}
					else {
						this.listData = [...this.listData, ...testData];
						this.page = currentPage + 1;
					}
					// 没有数据的时候这样设置
					// this.listData = [];
					// this.emptyShow = true;
					this.end = true;
					if(this.page > 6){
						this.end = true; //没有更多数据
					}
					this.listCount = this.listData.length; //列表条数
				}, 1000);
			},
			//下啦刷新
			refresh(){
				this.req();
				_self.getEvaluate();
				this.getList('refresh', pageStart);
				this.empty = false;
			},
			//下拉刷新
			req(){
				uni.request({
					url : this.http+"/zyg/details",
					 data: {
					        o_id: _self.o_id
					},
					success: (res) => {
					       testData = res.data;
					}
				});
			}
			//上滑加载更多
			// more(){
			// 	this.getList('more', this.page);
			// 	console.log(2);
			// }
		}
	}
</script>

<style lang="scss">
	// #button_evaluate{
	// 	position: fixed;
	// 	z-index: 999;
	// 	top: 500px;
	// 	width: 98%;
	// 	margin: 0px auto;
	// }
	.zong{
		height: 100%;;
		display: flex;
		align-items:center;
	}
.title {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  background-color: #ffffff;
  z-index: 99;
  line-height: 90rpx;
  text-align: center;
  font-weight: bold;
  box-shadow: 0 0 20rpx -5rpx rgba(0, 0, 0, 0.1);
}

.cells {
	background-color: #fff;
  margin-top: 20rpx;
}

.cell {
  display: flex;
  padding: 20rpx;
}
.cell:not(:last-child) {
  border-bottom: 1rpx solid #ebedf0;
}
.cell__hd {
  font-size: 0;
}
.cell__hd image {
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
  border-radius: 12rpx;
}
.cell__bd {
	// display: flex;
  flex: 1;
}
.cell__bd .name {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  word-break: break-all;
  font-size: 28rpx;
  margin-bottom: 12rpx;
}
.cell__bd .des {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  word-break: break-all;
  color: #666666;
  font-size: 24rpx;
}

.control-panel {
  border-top: 1rpx solid #ebedf0;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 12213;
  background: #ffffff;
  padding: 20rpx;
  box-sizing: border-box;
}
.control-panel.isX {
  padding-bottom: 60rpx;
}

.panel-item {
  display: flex;
  align-items: center;
}
.panel-item__bd {
  flex: 1;
}


</style>
