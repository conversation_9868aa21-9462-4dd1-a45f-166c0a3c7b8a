@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
movable-area {
  width: 100%;
  height: 100vh;
}
.scroll {
  width: 100%;
  height: calc(100vh + 90rpx);
}
.scroll__view {
  height: 100%;
  position: relative;
  box-sizing: border-box;
}
.scroll__success {
  position: absolute;
  z-index: 9;
  top: 30rpx;
  left: 0;
  width: 100%;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 24rpx;
  text-align: center;
  opacity: 0;
  color: #3F82FD;
}
.scroll__success:after {
  content: " ";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: currentColor;
  opacity: 0.7;
  -webkit-transform: scaleX(0);
          transform: scaleX(0);
  transition: -webkit-transform 0.15s ease-in-out;
  transition: transform 0.15s ease-in-out;
  transition: transform 0.15s ease-in-out, -webkit-transform 0.15s ease-in-out;
  z-index: 0;
}
.scroll__success > view {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  color: #ffffff;
}
.scroll__success--show {
  opacity: 1;
}
.scroll__success--show:after {
  -webkit-transform: scaleX(1);
          transform: scaleX(1);
}
.scroll__success--tran {
  opacity: 0;
  transition: opacity 0.3s linear;
}
.scroll__refresh {
  height: 90rpx;
  padding: 20rpx 0;
  box-sizing: border-box;
}
.scroll__refresh--hidden {
  visibility: hidden;
}
.scroll__empty {
  padding: 30rpx;
  text-align: center;
}
.scroll__empty image {
  width: 200rpx;
  height: 200rpx;
  margin: 160rpx auto 60rpx;
}
.scroll__empty view {
  color: #999999;
}
.scroll__bottom {
  height: 40rpx;
  padding: 30rpx 0;
}
.scroll__bottom > .scroll__loading {
  text-align: center;
  color: #999999;
  font-size: 30rpx;
}
.scroll__bottom > .scroll__loading > .scroll__text {
  display: inline-block;
  vertical-align: middle;
}
/* start: refresh */
.la-square-jelly-box, .la-square-jelly-box > view {
  position: relative;
  box-sizing: border-box;
}
.la-square-jelly-box {
  width: 50rpx;
  height: 50rpx;
  margin: 0 auto;
  margin-top: -6rpx;
  display: block;
  font-size: 0;
  color: #3F82FD;
}
.la-square-jelly-box > view {
  display: inline-block;
  float: none;
  background-color: currentColor;
  opacity: 0.5;
}
.la-square-jelly-box > view:nth-child(1), .la-square-jelly-box > view:nth-child(2) {
  position: absolute;
  left: 0;
  width: 100%;
}
.la-square-jelly-box > view:nth-child(1) {
  top: -25%;
  z-index: 1;
  height: 100%;
  border-radius: 10%;
  -webkit-animation: square-jelly-box-animate 0.6s -0.1s linear infinite;
          animation: square-jelly-box-animate 0.6s -0.1s linear infinite;
}
.la-square-jelly-box > view:nth-child(2) {
  bottom: -9%;
  height: 10%;
  background: #000;
  border-radius: 50%;
  opacity: 0.2;
  -webkit-animation: square-jelly-box-shadow 0.6s -0.1s linear infinite;
          animation: square-jelly-box-shadow 0.6s -0.1s linear infinite;
}
@-webkit-keyframes square-jelly-box-shadow {
50% {
    -webkit-transform: scale(1.25, 1);
            transform: scale(1.25, 1);
}
}
@keyframes square-jelly-box-shadow {
50% {
    -webkit-transform: scale(1.25, 1);
            transform: scale(1.25, 1);
}
}
@-webkit-keyframes square-jelly-box-animate {
17% {
    border-bottom-right-radius: 10%;
}
25% {
    -webkit-transform: translateY(25%) rotate(22.5deg);
            transform: translateY(25%) rotate(22.5deg);
}
50% {
    border-bottom-right-radius: 100%;
    -webkit-transform: translateY(50%) scale(1, 0.9) rotate(45deg);
            transform: translateY(50%) scale(1, 0.9) rotate(45deg);
}
75% {
    -webkit-transform: translateY(25%) rotate(67.5deg);
            transform: translateY(25%) rotate(67.5deg);
}
100% {
    -webkit-transform: translateY(0) rotate(90deg);
            transform: translateY(0) rotate(90deg);
}
}
@keyframes square-jelly-box-animate {
17% {
    border-bottom-right-radius: 10%;
}
25% {
    -webkit-transform: translateY(25%) rotate(22.5deg);
            transform: translateY(25%) rotate(22.5deg);
}
50% {
    border-bottom-right-radius: 100%;
    -webkit-transform: translateY(50%) scale(1, 0.9) rotate(45deg);
            transform: translateY(50%) scale(1, 0.9) rotate(45deg);
}
75% {
    -webkit-transform: translateY(25%) rotate(67.5deg);
            transform: translateY(25%) rotate(67.5deg);
}
100% {
    -webkit-transform: translateY(0) rotate(90deg);
            transform: translateY(0) rotate(90deg);
}
}
/* end: refresh */
/* start: more */
.la-line-spin-fade-rotating, .la-line-spin-fade-rotating > view {
  position: relative;
  box-sizing: border-box;
}
.la-line-spin-fade-rotating {
  vertical-align: middle;
  display: inline-block;
  font-size: 0;
  color: currentColor;
  margin-right: 10rpx;
}
.la-line-spin-fade-rotating > view {
  display: inline-block;
  float: none;
  background-color: currentColor;
  border: 0 solid currentColor;
}
.la-line-spin-fade-rotating {
  width: 32rpx;
  height: 32rpx;
  -webkit-animation: ball-spin-fade-rotating-rotate 6s infinite linear;
          animation: ball-spin-fade-rotating-rotate 6s infinite linear;
}
.la-line-spin-fade-rotating > view {
  position: absolute;
  width: 2rpx;
  height: 8rpx;
  margin: 4rpx;
  margin-top: -4rpx;
  margin-left: 0;
  border-radius: 0;
  -webkit-animation: line-spin-fade-rotating 1s infinite ease-in-out;
          animation: line-spin-fade-rotating 1s infinite ease-in-out;
}
.la-line-spin-fade-rotating > view:nth-child(1) {
  top: 15%;
  left: 50%;
  -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
  -webkit-animation-delay: -1.125s;
          animation-delay: -1.125s;
}
.la-line-spin-fade-rotating > view:nth-child(2) {
  top: 25.2512626585%;
  left: 74.7487373415%;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  -webkit-animation-delay: -1.25s;
          animation-delay: -1.25s;
}
.la-line-spin-fade-rotating > view:nth-child(3) {
  top: 50%;
  left: 85%;
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
  -webkit-animation-delay: -1.375s;
          animation-delay: -1.375s;
}
.la-line-spin-fade-rotating > view:nth-child(4) {
  top: 74.7487373415%;
  left: 74.7487373415%;
  -webkit-transform: rotate(135deg);
          transform: rotate(135deg);
  -webkit-animation-delay: -1.5s;
          animation-delay: -1.5s;
}
.la-line-spin-fade-rotating > view:nth-child(5) {
  top: 84.9999999974%;
  left: 50.0000000004%;
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
  -webkit-animation-delay: -1.625s;
          animation-delay: -1.625s;
}
.la-line-spin-fade-rotating > view:nth-child(6) {
  top: 74.7487369862%;
  left: 25.2512627193%;
  -webkit-transform: rotate(225deg);
          transform: rotate(225deg);
  -webkit-animation-delay: -1.75s;
          animation-delay: -1.75s;
}
.la-line-spin-fade-rotating > view:nth-child(7) {
  top: 49.9999806189%;
  left: 15.0000039834%;
  -webkit-transform: rotate(270deg);
          transform: rotate(270deg);
  -webkit-animation-delay: -1.875s;
          animation-delay: -1.875s;
}
.la-line-spin-fade-rotating > view:nth-child(8) {
  top: 25.2506949798%;
  left: 25.2513989292%;
  -webkit-transform: rotate(315deg);
          transform: rotate(315deg);
  -webkit-animation-delay: -2s;
          animation-delay: -2s;
}
@-webkit-keyframes ball-spin-fade-rotating-rotate {
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes ball-spin-fade-rotating-rotate {
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@-webkit-keyframes line-spin-fade-rotating {
50% {
    opacity: 0.2;
}
100% {
    opacity: 1;
}
}
@keyframes line-spin-fade-rotating {
50% {
    opacity: 0.2;
}
100% {
    opacity: 1;
}
}
