<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <link rel="stylesheet" th:href="@{/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{/css/backlogin.css}">
    <script th:src="@{/layui/layui.js}"></script>
    <script th:src="@{/js/jquery-3.1.1.js}"></script>

</head>
<body>
<div id="container">
    <div></div>
    <div class="admin-login-background">
        <!--<div class="admin-header">-->
        <!--<img th:src="@{/images/dg.jpg}" class="admin-logo">-->
        <!--</div>-->
        <form class="layui-form" action="" id="formLogin">
            <div>
                <i class="layui-icon layui-icon-username admin-icon admin-icon-username"></i>
                <input type="text" name="usercode" placeholder="请输入用户名"
                       autocomplete="off"
                       class="layui-input admin-input admin-input-username">
            </div>
            <br>
            <br>
            <div>
                <i class="layui-icon layui-icon-password admin-icon admin-icon-password"></i>
                <input type="password" name="password"
                       placeholder="请输入密码"
                       autocomplete="off"
                       class="layui-input admin-input admin-input-password">
            </div>
            <!--<div>-->
            <!--<input type="text" name="verify"-->
            <!--placeholder="请输入验证码"-->
            <!--autocomplete="off"-->
            <!--class="layui-input admin-input admin-input-verify">-->
            <!--<img class="admin-captcha" src=""-->
            <!--onclick="updateVerify()">-->
            <!--</div>-->
            <button class="layui-btn admin-button" lay-submit lay-filter="formDemo">登陆</button>


        </form>

    </div>
</div>

<script>

    function updateVerify() {

    }


</script>
<script>

    layui.use('form', function(){
        var form = layui.form;
        //监听提交
        form.on('submit(formDemo)', function(data){
            // layer.msg(JSON.stringify(data.field));
            var flag;
            var msg;
            $.ajax({
                url:"login",
                type:'post',//method请求方式，get或者post
                data:$("#formLogin").serialize(),
                dataType:'json',//预期服务器返回的数据类型
                success:function (result) {
                   flag=result.flag;
                   msg=result.msg;
                   if (flag) {
                      // alert(msg);
                       location.href="index";
                   }else{
                       alert(msg);
                       location.href="toLogin";
                   }


                }

            });
            return false;
        });
    });
</script>
</body>
</html>