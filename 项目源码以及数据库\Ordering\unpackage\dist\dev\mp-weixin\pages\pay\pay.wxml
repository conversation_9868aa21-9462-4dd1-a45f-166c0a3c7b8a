<view><view><view style="width:60%;height:50px;margin:120px auto 0px auto;padding-left:50px;"><text>金额</text><view class="_br"></view><text style="font-size:36px;">{{"¥"+amount}}</text></view><view><button data-event-opts="{{[['tap',[['pay',['$event']]]]]}}" style="background-color:#01C760;width:60%;margin:60px auto;" bindtap="__e">支付</button></view></view><uni-popup class="vue-ref" vue-id="4fb6422e-1" type="center" data-ref="popup" data-event-opts="{{[['^change',[['clear']]]]}}" bind:change="__e" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{affirm}}"><view style="width:240px;height:200px;"><view><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" style="float:left;color:#808080;" bindtap="__e">x</view><view style="float:right;text-align:right;font-size:16px;"><text data-event-opts="{{[['tap',[['pwds',['$event']]]]]}}" style="color:#576B95;" bindtap="__e">使用密码</text></view><view style="clear:both;"></view></view><view style="text-align:center;width:100%;border-bottom:#888888;font-size:16px;">付给商家<view class="_br"></view><text style="font-size:28px;">{{"¥"+amount}}</text></view><view><view style="float:left;font-size:14px;color:#808080;">支付方式</view><view style="float:right;text-align:right;font-size:14px;color:#808080;">零钱></view><view style="clear:both;"></view></view><view style="margin-top:30px;"><button data-event-opts="{{[['tap',[['yesPay',['$event']]]]]}}" style="width:80%;background-color:#01C760;" bindtap="__e">确认支付</button></view></view></block><block wx:if="{{pwd}}"><view><view style="width:240px;"><view><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" style="float:left;color:#808080;" bindtap="__e">x</view><view style="float:right;text-align:right;font-size:16px;"><text data-event-opts="{{[['tap',[['yesPay',['$event']]]]]}}" style="color:#576B95;" bindtap="__e">使用指纹</text></view><view style="clear:both;"></view></view><view style="text-align:center;margin-top:20px;width:100%;border-bottom:#888888;font-size:16px;">付给商家<view class="_br"></view><text style="font-size:28px;">{{"¥"+amount}}</text></view><view><view style="float:left;font-size:14px;color:#808080;">支付方式</view><view style="float:right;text-align:right;font-size:14px;color:#808080;">零钱></view><view style="clear:both;"></view></view><view style="margin-top:30px;"><valid-code style="width:100%;" vue-id="{{('4fb6422e-2')+','+('4fb6422e-1')}}" maxlength="{{6}}" isPwd="{{true}}" data-event-opts="{{[['^finish',[['getPwd']]]]}}" bind:finish="__e" bind:__l="__l"></valid-code></view></view></view></block></uni-popup></view>