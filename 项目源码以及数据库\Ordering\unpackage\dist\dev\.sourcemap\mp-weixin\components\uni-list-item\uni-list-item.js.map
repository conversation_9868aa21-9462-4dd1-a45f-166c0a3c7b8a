{"version": 3, "sources": ["webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/uni-list-item/uni-list-item.vue?9870", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/uni-list-item/uni-list-item.vue?29f8", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/uni-list-item/uni-list-item.vue?9a84", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/uni-list-item/uni-list-item.vue?cfea", "uni-app:///components/uni-list-item/uni-list-item.vue", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/uni-list-item/uni-list-item.vue?786a", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/uni-list-item/uni-list-item.vue?86d2"], "names": ["name", "components", "uniIcons", "uniBadge", "props", "id", "type", "default", "title", "note", "disabled", "showArrow", "showBadge", "showSwitch", "switchChecked", "badgeText", "badgeType", "thumb", "showExtraIcon", "extraIcon", "color", "size", "data", "methods", "onClick", "onSwitchChange"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACa;;;AAGzE;AAC+K;AAC/K,gBAAgB,wLAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,8LAEN;AACP,KAAK;AACL;AACA,aAAa,8LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAypB,CAAgB,8qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC0B7qB;EACAA;EACAC;IACAC;IACAC;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IAAA;IACAE;MACAH;MACAC;IACA;IAAA;IACAG;MACA;MACAJ;MACAC;IACA;IACAI;MACA;MACAL;MACAC;IACA;IACAK;MACA;MACAN;MACAC;IACA;IACAM;MACA;MACAP;MACAC;IACA;IACAO;MACA;MACAR;MACAC;IACA;IACAQ;MACA;MACAT;MACAC;IACA;IACAS;MACA;MACAV;MACAC;IACA;IACAU;MACA;MACAX;MACAC;IACA;IACAW;MACA;MACAZ;MACAC;IACA;IACAY;MACAb;MACAC;QACA;UACAD;UACAc;UACAC;QACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AChHA;AAAA;AAAA;AAAA;AAA+7B,CAAgB,y7BAAG,EAAC,C;;;;;;;;;;;ACAn9B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/uni-list-item/uni-list-item.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-list-item.vue?vue&type=template&id=1093b690&\"\nvar renderjs\nimport script from \"./uni-list-item.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-list-item.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-list-item.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uni-list-item/uni-list-item.vue\"\nexport default component.exports", "export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-list-item.vue?vue&type=template&id=1093b690&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-icons/uni-icons\" */ \"@/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uniBadge: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-badge/uni-badge\" */ \"@/components/uni-badge/uni-badge.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-list-item.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-list-item.vue?vue&type=script&lang=js&\"", "<template>\n\t<view :class=\"disabled ? 'uni-list-item--disabled' : ''\" :hover-class=\"disabled || showSwitch ? '' : 'uni-list-item--hover'\" class=\"uni-list-item\" @click=\"onClick\">\n\t\t<view class=\"uni-list-item__container\">\n\t\t\t<view v-if=\"thumb\" class=\"uni-list-item__icon\">\n\t\t\t\t<image :src=\"thumb\" class=\"uni-list-item__icon-img\" />\n\t\t\t</view>\n\t\t\t<view v-else-if=\"showExtraIcon\" class=\"uni-list-item__icon\">\n\t\t\t\t<uni-icons :color=\"extraIcon.color\" :size=\"extraIcon.size\" :type=\"extraIcon.type\" class=\"uni-icon-wrapper\" />\n\t\t\t</view>\n\t\t\t<view class=\"uni-list-item__content\">\n\t\t\t\t<view class=\"uni-list-item__content-title\">{{ title }}</view>\r\n\t\t\t\t<view class=\"uni-list-item__content-id\">{{ id }}</view>\n\t\t\t\t<view v-if=\"note\" class=\"uni-list-item__content-note\">{{ note }}</view>\n\t\t\t</view>\n\t\t\t<view v-if=\"showBadge || showArrow || showSwitch\" class=\"uni-list-item__extra\">\n\t\t\t\t<uni-badge v-if=\"showBadge\" :type=\"badgeType\" :text=\"badgeText\" />\n\t\t\t\t<switch v-if=\"showSwitch\" :disabled=\"disabled\" :checked=\"switchChecked\" @change=\"onSwitchChange\" />\n\t\t\t\t<uni-icons v-if=\"showArrow\" :size=\"20\" class=\"uni-icon-wrapper\" color=\"#bbb\" type=\"arrowright\" />\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport uniIcons from '../uni-icons/uni-icons.vue'\n\timport uniBadge from '../uni-badge/uni-badge.vue'\n\texport default {\n\t\tname: 'UniListItem',\n\t\tcomponents: {\n\t\t\tuniIcons,\n\t\t\tuniBadge\n\t\t},\n\t\tprops: {\r\n\t\t\tid:{\r\n\t\t\t\ttype:Number,\r\n\t\t\t\tdefault:0\r\n\t\t\t},\n\t\t\ttitle: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t}, // 列表标题\n\t\t\tnote: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t}, // 列表描述\n\t\t\tdisabled: {\n\t\t\t\t// 是否禁用\n\t\t\t\ttype: [Boolean, String],\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\tshowArrow: {\n\t\t\t\t// 是否显示箭头\n\t\t\t\ttype: [Boolean, String],\n\t\t\t\tdefault: true\n\t\t\t},\n\t\t\tshowBadge: {\n\t\t\t\t// 是否显示数字角标\n\t\t\t\ttype: [Boolean, String],\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\tshowSwitch: {\n\t\t\t\t// 是否显示Switch\n\t\t\t\ttype: [Boolean, String],\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\tswitchChecked: {\n\t\t\t\t// Switch是否被选中\n\t\t\t\ttype: [Boolean, String],\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\tbadgeText: {\n\t\t\t\t// badge内容\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\tbadgeType: {\n\t\t\t\t// badge类型\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'success'\n\t\t\t},\n\t\t\tthumb: {\n\t\t\t\t// 缩略图\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\tshowExtraIcon: {\n\t\t\t\t// 是否显示扩展图标\n\t\t\t\ttype: [Boolean, String],\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\textraIcon: {\n\t\t\t\ttype: Object,\n\t\t\t\tdefault () {\n\t\t\t\t\treturn {\n\t\t\t\t\t\ttype: 'contact',\n\t\t\t\t\t\tcolor: '#000000',\n\t\t\t\t\t\tsize: 20\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {}\n\t\t},\n\t\tmethods: {\n\t\t\tonClick() {\n\t\t\t\tthis.$emit('click')\n\t\t\t},\n\t\t\tonSwitchChange(e) {\n\t\t\t\tthis.$emit('switchChange', e.detail)\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t@charset \"UTF-8\";\n\n\t.uni-list-item {\n\t\tfont-size: 32upx;\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: space-between;\n\t\talign-items: center\n\t}\n\n\t.uni-list-item--disabled {\n\t\topacity: .3\n\t}\n\n\t.uni-list-item--hover {\n\t\tbackground-color: #f1f1f1\n\t}\n\n\t.uni-list-item__container {\n\t\tpadding: 24upx 30upx;\n\t\twidth: 100%;\n\t\tbox-sizing: border-box;\n\t\tflex: 1;\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tjustify-content: space-between;\n\t\talign-items: center\n\t}\n\n\t.uni-list-item__container:after {\n\t\tposition: absolute;\n\t\tz-index: 3;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tleft: 30upx;\n\t\theight: 1px;\n\t\tcontent: '';\n\t\t-webkit-transform: scaleY(.5);\n\t\ttransform: scaleY(.5);\n\t\tbackground-color: #e5e5e5\n\t}\n\n\t.uni-list-item__content {\n\t\tflex: 1;\n\t\toverflow: hidden;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tcolor: #3b4144\n\t}\n\n\t.uni-list-item__content-title {\n\t\tfont-size: 32upx;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t\tcolor: inherit;\n\t\tline-height: 1.5;\n\t\toverflow: hidden\n\t}\n\n\t.uni-list-item__content-note {\n\t\tmargin-top: 6upx;\n\t\tcolor: #999;\n\t\tfont-size: 28upx;\n\t\twhite-space: normal;\n\t\tdisplay: -webkit-box;\n\t\t-webkit-box-orient: vertical;\n\t\t-webkit-line-clamp: 2;\n\t\toverflow: hidden\n\t}\n\n\t.uni-list-item__extra {\n\t\twidth: 25%;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tjustify-content: flex-end;\n\t\talign-items: center\n\t}\n\n\t.uni-list-item__icon {\n\t\tmargin-right: 18upx;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tjustify-content: center;\n\t\talign-items: center\n\t}\n\n\t.uni-list-item__icon-img {\n\t\theight: 52upx;\n\t\twidth: 52upx\n\t}\n\n\t.uni-list>.uni-list-item:last-child .uni-list-item-container:after {\n\t\theight: 0\n\t}\r\n\t.uni-list-item__content-id{\r\n\t\tdisplay: none;\r\n\t}\n</style>", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-list-item.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-list-item.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1748246853168\n      var cssReload = require(\"E:/software/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}