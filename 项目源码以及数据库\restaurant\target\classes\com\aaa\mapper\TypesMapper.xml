<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.aaa.mapper.TypesMapper" >
    <select id="selTypes" resultType="Types" parameterType="Types">
        SELECT * from types
        <if test="startRow!=null and pageSize!=null and pageSize!=0">
            limit #{startRow},#{pageSize}
        </if>
    </select>
    <select id="selOne" resultType="int">
        select count(*) from types
    </select>
    <insert id="addTypes" parameterType="Types">
        INSERT INTO types(t_name,t_state) VALUES (#{t_name},#{t_state})
    </insert>
    <select id="addTypesOne" parameterType="java.lang.String" resultType="Types">
        SELECT * FROM types WHERE t_name=#{t_name}
    </select>
    <update id="updateTypes" parameterType="Map">
        UPDATE types SET t_state=#{t_state} WHERE t_id=#{t_id}
    </update>
    <select id="selMenus" parameterType="int" resultType="int">
        SELECT count(*) FROM menus where t_id = #{t_id} and m_state =0;
    </select>
</mapper>