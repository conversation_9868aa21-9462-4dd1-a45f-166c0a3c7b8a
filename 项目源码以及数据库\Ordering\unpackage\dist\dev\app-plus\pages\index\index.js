setCssToHead(["#but_type{ position: fixed; width: 40px; z-index: 999; color: gray; opacity:0.5; }\n#v_type{ }\n.",[1],"swiper-item { display: -webkit-box; display: -webkit-flex; display: -ms-flexbox; display: flex; -webkit-box-pack: center; -webkit-justify-content: center; -ms-flex-pack: center; justify-content: center; -webkit-box-align: center; -webkit-align-items: center; -ms-flex-align: center; align-items: center; height: 100%; background: #eee; color: #fff; }\n.",[1],"swiper-item wx-image { width: 100%; height: 100%; }\n.",[1],"menus{ width: 100%; height: 100px; overflow: hidden; }\n.",[1],"menus\x3ewx-view{ float: left; }\n.",[1],"butCount{ width: 10xp; height: 10xp; }\n",],"Some selectors are not allowed in component wxss, including tag name selectors, ID selectors, and attribute selectors.(./pages/index/index.wxss:9:1)",{path:"./pages/index/index.wxss"})();
document.dispatchEvent(new CustomEvent("generateFuncReady", { detail: { generateFunc: $gwx('./pages/index/index.wxml') } }));