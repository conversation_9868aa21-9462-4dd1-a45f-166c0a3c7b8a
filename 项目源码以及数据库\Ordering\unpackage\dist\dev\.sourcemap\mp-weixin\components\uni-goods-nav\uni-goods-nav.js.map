{"version": 3, "sources": ["webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/uni-goods-nav/uni-goods-nav.vue?4b39", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/uni-goods-nav/uni-goods-nav.vue?caf7", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/uni-goods-nav/uni-goods-nav.vue?42d6", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/uni-goods-nav/uni-goods-nav.vue?ad05", "uni-app:///components/uni-goods-nav/uni-goods-nav.vue", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/uni-goods-nav/uni-goods-nav.vue?83ec", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/uni-goods-nav/uni-goods-nav.vue?35b6"], "names": ["name", "props", "options", "type", "default", "icon", "text", "buttonGroup", "backgroundColor", "color", "fill", "maskClick", "show", "data", "showGoods", "methods", "onClick", "index", "content", "buttonClick", "uni", "open", "close"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACa;;;AAGzE;AAC+K;AAC/K,gBAAgB,wLAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAypB,CAAgB,8qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC4B7qB;EACAA;EACAC;IACAC;MACAC;MACAC;QACA,QACA;UACAC;UACAC;QACA,GACA;UACAD;UACAC;QACA;MACA;IACA;IACAC;MACAJ;MACAC;QACA;UACAE;UACAE;UACAC;QACA,GACA;UACAH;UACAE;UACAC;QACA,EACA;MACA;IACA;IACAC;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IAAA;IACAQ;MACAT;MACAC;IACA;EACA;EACAS;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;QACAC;QACAC;MACA;IACA;IACAC;MACA;QACAC;MACA;MACA;QACAH;QACAC;MACA;IACA;IAGA;IACAG;MAAA;MACA;QACAT;MACA;MACA;MACA;QACA;MAEA;IACA;IACAU;MAAA;MACA;MACA;QACAV;MACA;MACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACxHA;AAAA;AAAA;AAAA;AAA+7B,CAAgB,y7BAAG,EAAC,C;;;;;;;;;;;ACAn9B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/uni-goods-nav/uni-goods-nav.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-goods-nav.vue?vue&type=template&id=1afb79a4&\"\nvar renderjs\nimport script from \"./uni-goods-nav.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-goods-nav.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-goods-nav.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uni-goods-nav/uni-goods-nav.vue\"\nexport default component.exports", "export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-goods-nav.vue?vue&type=template&id=1afb79a4&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-goods-nav.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-goods-nav.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view v-if=\"showGoods\" class=\"showGoods\" style=\"\">\r\n\t\t\t<!-- 底部占位 -->\r\n\t\t\t<view   class=\"uni-tab__seat\" />\r\n\t\t\t<view class=\"uni-tab__cart-box flex\">\r\n\t\t\t\t<view class=\"flex uni-tab__cart-sub-box\">\r\n\t\t\t\t\t<view v-for=\"(item,index) in options\" :key=\"index\" class=\"flex uni-tab__cart-button-left uni-tab__shop-cart\" @click=\"onClick(index,item)\">\r\n\t\t\t\t\t\t<view class=\"uni-tab__icon\">\r\n\t\t\t\t\t\t\t<image :src=\"item.icon\" mode=\"widthFix\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"uni-tab__text\">{{ item.text }}</text>\r\n\t\t\t\t\t\t<view class=\"flex uni-tab__dot-box\">\r\n\t\t\t\t\t\t\t<view v-if=\"item.info\" :class=\"{ 'uni-tab__dots': item.info > 9 }\" class=\"uni-tab__dot \">\r\n\t\t\t\t\t\t\t\t{{ item.info }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view :class=\"{'uni-tab__right':fill}\" class=\"flex uni-tab__cart-sub-box \">\r\n\t\t\t\t\t<view v-for=\"(item,index) in buttonGroup\" :key=\"index\" :style=\"{backgroundColor:item.backgroundColor,color:item.color}\" class=\"flex uni-tab__cart-button-right\" @click=\"buttonClick(index,item)\">{{ item.text }}</view>\r\n\t\t\t\t\t<!-- <view class=\"flex uni-tab__cart-button-right uni-tab__color-y \">立即购买</view> -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tname: 'UniGoodsNav',\r\n\t\tprops: {\r\n\t\t\toptions: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn [\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\ticon: 'https://img-cdn-qiniu.dcloud.net.cn/uniapp/uni-ui/goodsnav/dianpu.png',\r\n\t\t\t\t\t\ttext: '店铺'\r\n\t\t\t\t\t}, \r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ticon: 'https://img-cdn-qiniu.dcloud.net.cn/uniapp/uni-ui/goodsnav/carts.png',\r\n\t\t\t\t\t\ttext: '购物车'\r\n\t\t\t\t\t}]\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tbuttonGroup: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn [{\r\n\t\t\t\t\t\t\ttext: '加入购物车',\r\n\t\t\t\t\t\t\tbackgroundColor: '#ff0000',\r\n\t\t\t\t\t\t\tcolor: '#fff'\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\ttext: '立即购买',\r\n\t\t\t\t\t\t\tbackgroundColor: '#ffa200',\r\n\t\t\t\t\t\t\tcolor: '#fff'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t]\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tfill: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// maskClick\r\n\t\t\tmaskClick: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},// 开启动画\r\n\t\t\tshow: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata(){\r\n\t\t\treturn{\r\n\t\t\t\tshowGoods: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tonClick(index, item) {\r\n\t\t\t\tthis.$emit('click', {\r\n\t\t\t\t\tindex,\r\n\t\t\t\t\tcontent: item\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tbuttonClick(index, item) {\r\n\t\t\t\tif (uni.report) {\r\n\t\t\t\t\tuni.report(item.text, item.text)\r\n\t\t\t\t}\r\n\t\t\t\tthis.$emit('butt', {\r\n\t\t\t\t\tindex,\r\n\t\t\t\t\tcontent: item\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t//隐藏显示\r\n\t\t\topen() {\r\n\t\t\t\tthis.$emit('change', {\r\n\t\t\t\t\tshow: true\r\n\t\t\t\t})\r\n\t\t\t\tthis.showGoods = true\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.ani = 'uni-' + this.type\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tclose(type) {\r\n\t\t\t\tif (!this.maskClick && type) return\r\n\t\t\t\tthis.$emit('change', {\r\n\t\t\t\t\tshow: false\r\n\t\t\t\t})\r\n\t\t\t\tthis.ani = ''\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.showGoods = false\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.flex {\r\n\t\tdisplay: flex;\r\n\t}\r\n\r\n\t.uni-tab__cart-box {\r\n\t\twidth: 100%;\r\n\t\theight: 100rpx;\r\n\t\tbackground: #fff;\r\n\t\tz-index: 900;\r\n\t\t/* position: absolute fixed ; */\r\n\t}\r\n\r\n\t.uni-tab__cart-sub-box {\r\n\t\twidth: 100%;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.uni-tab__right {\r\n\t\tmargin: 5px 0;\r\n\t\tmargin-right: 10px;\r\n\t\tborder-radius: 100px;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.uni-tab__cart-button-left {\r\n\t\tposition: relative;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tflex-direction: column;\r\n\t\twidth: 100%;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.uni-tab__icon {\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t}\r\n\r\n\t.uni-tab__icon image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.uni-tab__cart-button-left .uni-tab__text {\r\n\t\tmargin-top: 5rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.uni-tab__cart-button-right {\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\twidth: 100%;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t.uni-tab__cart-button-right:active {\r\n\t\topacity: 0.7;\r\n\t}\r\n\r\n\t.uni-tab__cart-button-left .uni-tab__dot-box {\r\n\t\tposition: absolute;\r\n\t\tright: 40rpx;\r\n\t\ttop: 20rpx;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\twidth: 0;\r\n\t\theight: 0;\r\n\t}\r\n\r\n\t.uni-tab__dot-box .uni-tab__dot {\r\n\t\tflex-shrink: 0;\r\n\t\twidth: 30rpx;\r\n\t\theight: 30rpx;\r\n\t\tline-height: 30rpx;\r\n\t\tcolor: #ffffff;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 12px;\r\n\t\tbackground: #ff0000;\r\n\t\tborder-radius: 50%;\r\n\t}\r\n\r\n\t.uni-tab__dot-box .uni-tab__dot.uni-tab__dots {\r\n\t\tpadding: 0 8rpx;\r\n\t\twidth: auto;\r\n\t\tborder-radius: 30rpx;\r\n\t}\r\n\r\n\t.uni-tab__color-y {\r\n\t\tbackground: #ffa200;\r\n\t}\r\n\r\n\t.uni-tab__color-r {\r\n\t\tbackground: #ff0000;\r\n\t}\r\n</style>", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-goods-nav.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-goods-nav.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1748248725633\n      var cssReload = require(\"E:/software/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}