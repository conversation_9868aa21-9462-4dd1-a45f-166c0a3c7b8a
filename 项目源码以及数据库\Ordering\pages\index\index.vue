<template>
	<view>
		<view>
		<uni-popup ref="popup1" type="top">
			<view>请确认订单：</view>
			<view v-for="(itme,index) in selectMenus" :key="index" class="anorder">
					<view><image :src="http+upload+itme.m_img" mode="widthFix" style="width: 80px;"></image></view>
					<view>
						<view style="height: 40px;overflow: hidden;">{{itme.m_name}}</view>单价：<text>{{itme.m_price}}</text><br/>
						数量：<text>{{itme.count}}</text>
					</view>
					<view>
						<view style="height: 80px; line-height: 80px;color: red;">小计:{{itme.m_price*itme.count}}</view>
					</view>
			</view>
			<view>
				<view>
					总价：<text>&yen;{{price}}</text><br/>时间：{{orderDate}}<br/>桌号：{{deskNumber}}桌
				</view>
				<view>
					<button @click="submitOrder">提交订单</button>
				</view>
			</view>
		</uni-popup>
		<!-- <view :style="shade" id="shade"></view> -->
		<uni-popup ref="popup" type="bottom">
			<view  id="cartPopup">
				<view v-for="(itme,index) in selectMenus" :key="index">
					<view>
						<image :src="http+upload+itme.m_img" mode="widthFix" style="width: 100px;"></image>
					</view>
					<view>
						<view style="height: 40px;overflow: hidden;padding-top: 10px;">{{itme.m_name}}</view>单价：<text>{{itme.m_price}}</text><br/>
						<!-- 数量：<text>{{itme.count}}</text> -->
						
					</view>
					<view>
						<view style="width: 100px;margin-top: 10px;">
							<view style="padding-left: 25px;margin-bottom: 15px;">总价:{{itme.m_price*itme.count}}</view>
							<view @click="Modified(itme,'-')" style="float: left;width: 30px;height: 20px;background-color:  #F8F8F8;text-align: center;">-</view>
							<view style="float: left;width: 30px;height: 20px;text-align: center;">{{itme.count}}</view>
							<view @click="Modified(itme,'+')"  style="float: left;width: 30px;height: 20px;background-color: #F8F8F8;text-align: center;">+</view>
							<view style="clear: both;"></view>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
		<!-- {'uni-numbox--disabled': inputValue <= min || disabled} -->
		<!-- 'display:' "desk ? 'block':'none' -->
		<view id="desk" :class="{'desknone' : false || desk }" @click="ondesk">[&nbsp;&nbsp;]</view>
		<!-- 这是Type类型展示 -->
		<view id="v_type">
			<button type="default" @click="show('left')" id="but_type">></button>
			<uni-drawer :visible="showLeft" mode="left" @close="closeDrawer('left')">
				<uni-list>
				<uni-list-item v-for='(time,index) in types' :key="index" :title="time.t_name" :id="time.id" @click="chaxun(time)" />
				</uni-list>
				<view class="close"><button type="default" @click="hide">关闭</button></view>
			</uni-drawer>
		</view>
		<!-- 这个是轮播模块 -->
		<view class="content">
			<uni-swiper-dot :info="info" :current="current" :mode="mode" field="content" cash="cash" >
				<swiper class="swiper-box" @change="change" 
				autoplay="true" 
				interval="3000" 
				duration="1000"
				circular="true">
					<swiper-item v-for="(item, index) in info" :key="index">
						<view :class="item.colorClass" class="swiper-item">
							<image :src="item.url" mode="aspectFill" />
						</view>
					</swiper-item>
				</swiper>
			</uni-swiper-dot>
		</view>
		<uni-goods-nav :style="goods_nav_style" id="goods_nav_style" :fill="true" ref="openGroup" :options="options" :button-group="buttonGroup"   @click="cart" @butt="buy" />
		<!-- 这个是菜品展示模块time.m_img -->
		<view style="width: 100%;margin: 10px 0px;">
			<view v-for="(time,index) in menu" :key="index" class="menus">
				<view style="width: 115px;">
					<image :src="http+upload+time.m_img" style="width: 100px;margin: 20px 5px 0px 10px;" mode="widthFix"></image>
				</view>
				<view style="margin:10px 0px 0px 20px; width: 110px;font-size: 16px;color: #808080;">
					<view style="height: 40px;margin-top: 10px;overflow: hidden;">{{time.m_name}}</view>
					<text style="color: red;">&yen;{{time.m_price}}</text>
				</view>
				<view style="width: 100px;margin-top: 30px;">
					<yp-number-box @change="bindChange" :index="index" :value="time.count" />
				</view>
			</view>
		</view>
		</view>
	</view>
</template>

<script>
	import uniDrawer from '../../components/uni-drawer/uni-drawer.vue'
	import uniListItem from '../../components/uni-list-item/uni-list-item.vue'//drawer列表数据
	//轮播
	import uniSwiperDot from '@/components/uni-swiper-dot/uni-swiper-dot.vue'
	import ypNumberBox  from "@/components/yp-number-box/yp-number-box.vue"
	import ypNumberBox1  from "@/components/yp-number-box/yp-number-box1.vue"
	//Popup弹出框
	import uniPopup from "@/components/uni-popup/uni-popup.vue"
	//购物车
	import uniGoodsNav from "@/components/uni-goods-nav/uni-goods-nav.vue"
	let _sef;
	let num = 0;
	export default {
		data() {
			return {
				upload :'upload/',
				//订单时间
				orderDate:"",
				//是否显示扫一扫
				desk:false,
				//桌号
				deskNumber:0,
				//判断手机高度
				goods_nav_style:"margin-top:405px;",
				//总价
				price:0,
				//请求地址
				http:"",
				//菜单
				menu:[],
				//已选菜单
				selectMenus:[],
				//是否显示类型
				showLeft: false,
				cartStatus:true,
				types:[
					{"t_name":'全部',t_id:0}
				],
				//轮播内容
				info: [{
						colorClass: 'uni-bg-red',
						url: 'http://pic.qjimage.com/chineseview116/high/482-10992.jpg',
						content: '猪脚',
						cash:'89.00'
					},
					{
						colorClass: 'uni-bg-green',
						url: 'http://images.quanjing.com/chineseview116/high/482-10610.jpg',
						content: '口水鸡',
						cash:'88.80'
					},
					{
						colorClass: 'uni-bg-blue',
						url: 'http://img1.juimg.com/161225/335318-161225154Z323.jpg',
						content: '山药银杏果份',
						cash:'35.80'
					}
				],
				//轮播样式
				dotStyle: [{
						backgroundColor: 'rgba(0, 0, 0, .3)',
						border: '1px rgba(0, 0, 0, .3) solid',
						color: '#fff',
						selectedBackgroundColor: 'rgba(0, 0, 0, .9)',
						selectedBorder: '1px rgba(0, 0, 0, .9) solid'
					}
				],
				current: 0,
				mode: 'nav',
				options: [
					{icon: '/static/qingkong.png',
				    text: '清空购物车'
				   },
					{
				     icon: 'https://img-cdn-qiniu.dcloud.net.cn/uniapp/uni-ui/goodsnav/carts.png',
				     text: '购物车',
				     info: 0
				   },
					{
					icon: '/static/Money.png',
					text: '0',
					}
					],
				buttonGroup: [
					{
					text: '立即购买',
					backgroundColor: '#ffa200',
					color: '#fff'
					}
				]
			}
		},
		components: {
			uniDrawer,
			uniListItem,
			uniSwiperDot,
			ypNumberBox,
			ypNumberBox1,
			uniPopup,
			uniGoodsNav
		},
		onLoad() {
			_sef = this;
			uni.showTabBar({
				animation:true,
			});
			_sef.http = getApp().globalData.http;
			//这个是菜品类型的展示
			uni.request({
				url:this.http+"zkq/types",
				success:(vel)=>{
					this.types = this.types.concat(vel.data);
				}
			});
			//这个是所有菜品的展示
			uni.request({
				url:this.http+"zkq/menus",
				success:(vel)=>{
					let ms = vel.data;
					for(let m in ms){
						ms[m].count = 0;
					};
					this.menu = ms
				}
			});
			uni.getSystemInfo({
				success: function (res) {
					let px = Number(res.windowHeight)-Number(148);
					_sef.goods_nav_style = "margin-top:"+px+"px;";
				}
			});
		},
		methods: {
			//submitOrder 提交订单
			submitOrder(){
				
				let count = new Array();
				let id = new Array();
				let sm = this.selectMenus;
				for(let i in sm){
					//console.log(sm[i].m_id);
					id.push(sm[i].m_id);
					count.push(sm[i].count);
				}
				uni.redirectTo({
				    url: '../pay/pay?orderDate='+this.orderDate+"&deskNumber="+this.deskNumber+"&id="+id+"&count="+count+"&price="+this.price
				});
			},
			//显示分类
			show(e) {
				if (e === 'left') {
					this.showLeft = true
				}
			},
			//隐藏分类
			hide() {
				this.showLeft = false
			},
			closeDrawer(e) {
				if (e === 'left') {
					this.showLeft = false
				}
			},
			confirm() {},
			//轮播图片编号
			change(e) {
				this.current = e.detail.current
			},
			//分类查询
			chaxun(type){
				uni.request({
					url:this.http+"zkq/menus",
					data:{
						t_id:type.t_id
					},
					success:(vel)=>{
						let ms = vel.data;
						let sms = _sef.selectMenus; 
						for(let m in ms){
							//ms[m].count = 0;
							for(let s in sms){
								if(ms[m].m_id === sms[s].m_id){
									ms[m].count = sms[s].count;
									break;
								}else if(s === sms.length-1){
									ms[m].count = 0;
								}
							}
						};
						this.menu = ms
					}
				});
				this.hide();
			},
			//购物车加减时触发
			Modified(val,fh){
				 let num = val.count;
				 if(fh==="-"){
					 num--;
				 }else{
					 num++;
				 }
				let m = this.menu;
				for(let i in m){
					if(m[i].m_id === val.m_id){
						m[i].count = num;
					}
				}
			},
			//输入框改变时出发 加入购物车和购物车的加减
			bindChange(val){
				let m_index = val[0];
				//改变时的数量
				let m_count = val[1];
				let seMenu = this.menu[m_index];
				if(m_count>0){
					if(this.selectMenus.length>0){
						let cou = 0;
						for(let i in this.selectMenus){
							cou++;
							if(this.selectMenus[i].m_id == seMenu.m_id){
								if(m_count == 0){
									this.selectMenus.splice(i,1);
									return this.zongjia();
								}else{
									this.selectMenus[i].count = m_count;
									return this.zongjia();
								}
							}else if(this.selectMenus.length == cou){
								seMenu.count = m_count;
								this.selectMenus.push(seMenu);
								return this.zongjia();
							}else{
								continue;
							}
						}
					}else{
						seMenu.count = m_count;
						this.selectMenus.push(seMenu);
						return this.zongjia();
					}
				}else{
					seMenu.count = 0;
					let sms = this.selectMenus;
					for(let i in sms){
						if(sms[i].m_id === seMenu.m_id){
							sms.splice(i,1);
						}
					}
					return this.zongjia();
				}
			},
			//总价格
			zongjia(){
				this.price = 0;
				let num = 0;
				let sum = 0;
				for(let i in this.selectMenus){
					sum += Number(this.selectMenus[i].count);
					num = this.selectMenus[i].m_price * this.selectMenus[i].count;
					this.price += num;
				}
				this.options[1].info = sum;
				this.options[2].text=this.price;
				if(this.price==0){
					//关闭购物车弹出框
					this.$refs.popup.close();
					this.$refs.openGroup.close()
					//显示TabBar
					uni.showTabBar({
						animation:true,
					});
				}else{
					//关闭TabBar
					uni.hideTabBar({
						animation:true,
					});
					//打购物车开弹出框 goods_nav_style
					this.$refs.openGroup.open();
				}
			},
			//点击扫码
			ondesk(val){
				// 允许从相机和相册扫码
				uni.scanCode({
				    success: function (res) {
						//this.deskNumber = Number(res.result);
						_sef.deskNumber = Number(res.result);
						_sef.desk = true;
				    }
				});
			},
			//购物车
			cart(e) {
				_sef.hide();
				this.$refs.popup1.close();
				if(e.index==1 && this.cartStatus){
					this.$refs.popup.open();
					this.cartStatus = false;
				}else if(e.index==0){
					_sef.selectMenus=[];
					var m = _sef.menu;
					for (let i in m) {
						m[i].count = 0;
					}
				}else{
					this.cartStatus = true;
					this.$refs.popup.close();
				}
			},
			//立即购买
			buy(e) {
				let date = new Date();
				let year = date.getFullYear();       //年
				let month = date.getMonth() + 1;     //月
				let day = date.getDate();			//日
				let hh = date.getHours();            //时
				let mm = date.getMinutes();          //分
				let ss = date.getSeconds();
				this.orderDate = year+"-"+month+"-"+day+" "+hh+":"+mm+":"+ss;
				if(_sef.deskNumber == '' || _sef.deskNumber <= 0){
					uni.showToast({
					    title: '你还没有扫描桌号！',
					    duration: 2000,
						icon:'none'
					});
					return;
				}
				this.$refs.popup.close();
				this.$refs.popup1.open();
			}
		},
		onNavigationBarButtonTap(e) {
		},
		onBackPress() {
			if (this.showLeft) {
				this.hide()
				return true
			}
		},
		watch:{
		}
	}
</script>
			
<style>
	.anorder{
		width: 100%;
		height: 100px;
		margin: 10px;
		font-size: 16px;
		
	}
	.anorder>view{
		width: 30%;
		float: left;
	}
	.desknone{
		display: none;
	}
	#cartPopup>view{
		margin: 10px auto;
		clear: both;
		color: #808080;
		font-size: 16px;
		height: 100px;
	}
	#cartPopup>view>view{
		float: left;
		width: 30%;
		margin-left: 5px;
	}
	#apps{
		display: flex;
	}
	#goods_nav_style{
		border: #00BFFF;z-index: 999;position: fixed;width: 100%;
	}
	#desk{
		position: fixed;
		right: 0px;
		z-index: 2;
		width: 40px;
		height: 40px;
		background-color: #DEDEDE;
		line-height: 40px;
		text-align: center;
		overflow: hidden;
		border-radius: 5px;
		opacity:0.5;
	}
	#but_type{
		position: fixed;
		width: 40px;
		z-index: 2;
		color: gray;
		opacity:0.5;
	}
	#v_type{
		
	}
	.swiper-item {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 100%;
		background: #eee;
		color: #fff;
	}
	.swiper-item image {
		width: 100%;
		height: 100%;
	}
	/* 菜单 */
	.menus{
		width: 100%;
		height: 100px;
		overflow: hidden;
		
		/* border: #000000 1px solid; */
	}
	.menus>view{
		float: left;
	}
	.butCount{
		width: 10xp;
		height: 10xp;
	}
	#shade{
		width: 100%;height: 100%;background-color: #00BFFF;opacity: 0.3;position: fixed;
	}
	.uni-tab__cart-box{
	}
</style>