{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/test/test.vue?4447", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/test/test.vue?9185", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/test/test.vue?e7ff", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/test/test.vue?b9e6", "uni-app:///pages/test/test.vue", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/test/test.vue?f357", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/test/test.vue?8d64"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "components", "uniSearchBar", "onLoad", "_self", "methods", "startSoterAuthenticationFingerPrint", "uni", "requestAuthModes", "challenge", "success", "console", "fail", "complete", "search", "aaa", "bbb"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AAC+K;AAC/K,gBAAgB,wLAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4NAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAgpB,CAAgB,qqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACWpqB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;IACA,QACA;EACA;EACAC;IACAC;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MACAC;QACAC;QAAA;QACAC;QAEA;QACAC;UACAC;QACA;QACAC;UACAD;QACA;QACAE;UACAF;QACA;MACA;IACA;IACAG;MACAH;IACA;IACAI,qBAEA;IACAC,sBACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnDA;AAAA;AAAA;AAAA;AAAs7B,CAAgB,g7BAAG,EAAC,C;;;;;;;;;;;ACA18B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/test/test.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/test/test.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./test.vue?vue&type=template&id=3e202046&\"\nvar renderjs\nimport script from \"./test.vue?vue&type=script&lang=js&\"\nexport * from \"./test.vue?vue&type=script&lang=js&\"\nimport style0 from \"./test.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/test/test.vue\"\nexport default component.exports", "export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./test.vue?vue&type=template&id=3e202046&\"", "var components\ntry {\n  components = {\n    uniSearchBar: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-search-bar/uni-search-bar\" */ \"@/components/uni-search-bar/uni-search-bar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./test.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./test.vue?vue&type=script&lang=js&\"", "<template>\n    <view class=\"content\">\n        <button type=\"primary\" @click=\"startSoterAuthenticationFingerPrint\">开始指纹认证</button>\r\n\t\t<!--  @confirm=\"search\"  -->\r\n\t\t<uni-search-bar radius=\"50\" clearButton=\"auto\"  @confirm=\"search\" placeholder=\"菜名\" />\r\n\t\t<button @click=\"aaa\">清空</button>\r\n\t\t<!-- <button @click=\"search\">提交</button> -->\n    </view>\n</template>\n\n<script>\r\n\tlet _self;\r\n\timport uniSearchBar from '@/components/uni-search-bar/uni-search-bar.vue'\n    export default {\n        data() {\n            return {\n            }\n        },\r\n\t\tcomponents:{\r\n\t\t\tuniSearchBar\r\n\t\t},\n        onLoad() {\n\t\t\t_self=this;\n        },\n        methods: {\n            startSoterAuthenticationFingerPrint() {\n                uni.startSoterAuthentication({\n                    requestAuthModes: ['fingerPrint'],//facial 人脸  fingerPrint指纹\n                    challenge: '123456',\r\n\t\t\t\t\t\n                    //authContent: '请用指纹',\n                    success(res) {\n                        console.log(res);\n                    },\n                    fail(err) {\n                        console.log(err);\n                    },\n                    complete(res) {\n                        console.log(res);\n                    }\n                })\n            },\r\n\t\t\tsearch(){\r\n\t\t\t\tconsole.log(1);\r\n\t\t\t},\r\n\t\t\taaa(){\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\tbbb(e){\r\n\t\t\t}\n        }\n    }\n</script>\n\n<style>\n    button {\n        width: 200px;\n        margin: 20px auto;\n    }\n</style>", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./test.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./test.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1748246852006\n      var cssReload = require(\"E:/software/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}