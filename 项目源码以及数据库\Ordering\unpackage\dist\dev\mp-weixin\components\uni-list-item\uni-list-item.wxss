
@charset "UTF-8";
.uni-list-item {
	font-size: 32rpx;
	position: relative;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	align-items: center
}
.uni-list-item--disabled {
	opacity: .3
}
.uni-list-item--hover {
	background-color: #f1f1f1
}
.uni-list-item__container {
	padding: 24rpx 30rpx;
	width: 100%;
	box-sizing: border-box;
	flex: 1;
	position: relative;
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center
}
.uni-list-item__container:after {
	position: absolute;
	z-index: 3;
	right: 0;
	bottom: 0;
	left: 30rpx;
	height: 1px;
	content: '';
	-webkit-transform: scaleY(.5);
	transform: scaleY(.5);
	background-color: #e5e5e5
}
.uni-list-item__content {
	flex: 1;
	overflow: hidden;
	display: flex;
	flex-direction: column;
	color: #3b4144
}
.uni-list-item__content-title {
	font-size: 32rpx;
	text-overflow: ellipsis;
	white-space: nowrap;
	color: inherit;
	line-height: 1.5;
	overflow: hidden
}
.uni-list-item__content-note {
	margin-top: 6rpx;
	color: #999;
	font-size: 28rpx;
	white-space: normal;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden
}
.uni-list-item__extra {
	width: 25%;
	display: flex;
	flex-direction: row;
	justify-content: flex-end;
	align-items: center
}
.uni-list-item__icon {
	margin-right: 18rpx;
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center
}
.uni-list-item__icon-img {
	height: 52rpx;
	width: 52rpx
}
.uni-list>.uni-list-item:last-child .uni-list-item-container:after {
	height: 0
}
.uni-list-item__content-id{
	display: none;
}

