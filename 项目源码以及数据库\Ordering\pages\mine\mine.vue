<template>
  <view class="html">
    <view class="person-head">
      <cmd-avatar :src="userImg" @click="fnInfoWin" size="lg" :make="{'background-color': '#fff'}"></cmd-avatar>
      <view class="person-head-box">
        <view class="person-head-nickname">{{userName}}</view>
        <!-- <view class="person-head-username">ID：slimmer9501</view> -->
      </view>
    </view>
    <view class="person-list">
      <cmd-cell-item title="我的订单" slot-left arrow @click="order">
        <cmd-icon type="bullet-list" size="24" color="#368dff"></cmd-icon>
      </cmd-cell-item>
      <!-- <cmd-cell-item title="店铺评论" slot-left arrow>
        <cmd-icon type="message" size="24" color="#368dff"></cmd-icon>
      </cmd-cell-item> -->
      <!-- <cmd-cell-item title="系统设置" slot-left arrow>
        <cmd-icon type="settings" size="24" color="#368dff"></cmd-icon>
      </cmd-cell-item>
      <cmd-cell-item title="检查版本" addon="v1.0" slot-left arrow>
        <cmd-icon type="alert-circle" size="24" color="#368dff"></cmd-icon>
      </cmd-cell-item> -->
    </view>
  </view>
</template>

<script>
  import cmdAvatar from "@/components/cmd-avatar/cmd-avatar.vue"
  import cmdIcon from "@/components/cmd-icon/cmd-icon.vue"
  import cmdCellItem from "@/components/cmd-cell-item/cmd-cell-item.vue"
	var _sef;
  export default {
    components: {
      cmdAvatar,
      cmdCellItem,
      cmdIcon
    },
    data() {
      return {
		  userImg : '',
		  userName : '',
		  userID : 0,
	  };
    },
    methods: {
      /**
       * 打开用户信息页
       */
      fnInfoWin() {
        uni.navigateTo({
          url: '/pages/user/info/info'
        })
      },
	  order(){
		  uni.navigateTo({
		    url: '/pages/order/order'
		  })
		}
    },
	onLoad(){
		_sef = this;
		_sef.userImg = getApp().globalData.userImg
		_sef.userName = getApp().globalData.userName
		_sef.userID = getApp().globalData.u_id
	
	}
  }
</script>

<style>
  .person-head {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 150px;
    padding-left: 20px;/* 
    background: linear-gradient(to right, #365fff, #36bbff); */
	background-image: url(https://ss2.bdstatic.com/70cFvnSh_Q1YnxGkpoWK1HF6hhy/it/u=3912358549,1727259047&fm=26&gp=0.jpg);
	background-size:100% 100%;
	background-repeat:no-repeat;
	
  }

  .person-head-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    margin-left: 10px;
  }

  .person-head-nickname {
    font-size: 18px;
    font-weight: 500;
    color: #fff;
  }

  .person-head-username {
    font-size: 14px;
    font-weight: 500;
    color: #fff;
  }

  .person-list {
    line-height: 0;
	//padding-top: ;
	margin-top: 50px;
  }
  /* .html{
	  background-image: url(https://ss1.bdstatic.com/70cFuXSh_Q1YnxGkpoWK1HF6hhy/it/u=3039487882,4099011575&fm=26&gp=0.jpg);
	  background-size: ;
  } */
</style>
