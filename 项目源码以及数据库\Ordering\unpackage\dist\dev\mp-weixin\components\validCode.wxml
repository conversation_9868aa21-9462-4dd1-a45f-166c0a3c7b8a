<view class="code-area"><view class="flex-box"><input class="hide-input" type="number" focus="true" maxlength="{{maxlength}}" data-event-opts="{{[['input',[['getVal',['$event']]]]]}}" bindinput="__e"/><view class="{{['item',[(codeIndex==1)?'active':'']]}}"><view class="line"></view><block wx:if="{{$root.g0}}"><block><text class="dot">.</text></block></block><block wx:else><block>{{''+(codeArr[0]?codeArr[0]:'')}}</block></block></view><view class="{{['item',[(codeIndex==2)?'active':'']]}}"><view class="line"></view><block wx:if="{{$root.g1}}"><block><text class="dot">.</text></block></block><block wx:else><block>{{''+(codeArr[1]?codeArr[1]:'')}}</block></block></view><view class="{{['item',[(codeIndex==3)?'active':'']]}}"><view class="line"></view><block wx:if="{{$root.g2}}"><block><text class="dot">.</text></block></block><block wx:else><block>{{''+(codeArr[2]?codeArr[2]:'')}}</block></block></view><view class="{{['item',[(codeIndex==4)?'active':'']]}}"><view class="line"></view><block wx:if="{{$root.g3}}"><block><text class="dot">.</text></block></block><block wx:else><block>{{''+(codeArr[3]?codeArr[3]:'')}}</block></block></view><block wx:if="{{maxlength===6}}"><block><view class="{{['item',[(codeIndex==5)?'active':'']]}}"><view class="line"></view><block wx:if="{{$root.g4}}"><block><text class="dot">.</text></block></block><block wx:else><block>{{''+(codeArr[4]?codeArr[4]:'')}}</block></block></view><view class="{{['item',[(codeIndex==6)?'active':'']]}}"><view class="line"></view><block wx:if="{{$root.g5}}"><block><text class="dot">.</text></block></block><block wx:else><block>{{''+(codeArr[5]?codeArr[5]:'')}}</block></block></view></block></block></view></view>