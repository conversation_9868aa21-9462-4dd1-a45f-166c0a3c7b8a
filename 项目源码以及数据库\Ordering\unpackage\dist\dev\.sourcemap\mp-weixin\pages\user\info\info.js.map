{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/user/info/info.vue?b4a3", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/user/info/info.vue?1428", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/user/info/info.vue?caec", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/user/info/info.vue?7630", "uni-app:///pages/user/info/info.vue", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/user/info/info.vue?63be", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/user/info/info.vue?a78c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "cmdNavBar", "cmdPageBody", "cmdTransition", "cmdCelItem", "cmdAvatar", "onLoad", "_self", "data", "name", "img", "mounted", "methods", "exitLogin", "uni", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AAC+K;AAC/K,gBAAgB,wLAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,aAAa,4NAEN;AACP,KAAK;AACL;AACA,aAAa,oMAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAAgpB,CAAgB,qqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwBpqB;AAAA,eACA;EACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACAC;IACAA;IACAA;EACA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EAEAC;EAEAC;IACAC;MACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtDA;AAAA;AAAA;AAAA;AAAs7B,CAAgB,g7BAAG,EAAC,C;;;;;;;;;;;ACA18B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/user/info/info.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/info/info.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./info.vue?vue&type=template&id=6094af64&\"\nvar renderjs\nimport script from \"./info.vue?vue&type=script&lang=js&\"\nexport * from \"./info.vue?vue&type=script&lang=js&\"\nimport style0 from \"./info.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/info/info.vue\"\nexport default component.exports", "export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./info.vue?vue&type=template&id=6094af64&\"", "var components\ntry {\n  components = {\n    cmdPageBody: function () {\n      return import(\n        /* webpackChunkName: \"components/cmd-page-body/cmd-page-body\" */ \"@/components/cmd-page-body/cmd-page-body.vue\"\n      )\n    },\n    cmdTransition: function () {\n      return import(\n        /* webpackChunkName: \"components/cmd-transition/cmd-transition\" */ \"@/components/cmd-transition/cmd-transition.vue\"\n      )\n    },\n    cmdAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/cmd-avatar/cmd-avatar\" */ \"@/components/cmd-avatar/cmd-avatar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./info.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./info.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view>\r\n    <!-- <cmd-nav-bar back title=\"信息设置\"></cmd-nav-bar> -->\r\n    <cmd-page-body type=\"top\">\r\n      <cmd-transition name=\"fade-up\">\r\n        <view>\r\n          <cmd-cel-item title=\"头像\" slot-right arrow>\r\n            <cmd-avatar :src=\"img\"></cmd-avatar>\r\n          </cmd-cel-item>\r\n          <cmd-cel-item title=\"昵称\" :addon=\"name\" arrow></cmd-cel-item>\r\n          <button class=\"btn-logout\" @click=\"exitLogin\">退出登录</button>\r\n        </view>\r\n      </cmd-transition>\r\n    </cmd-page-body>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import cmdNavBar from \"@/components/cmd-nav-bar/cmd-nav-bar.vue\"\r\n  import cmdPageBody from \"@/components/cmd-page-body/cmd-page-body.vue\"\r\n  import cmdTransition from \"@/components/cmd-transition/cmd-transition.vue\"\r\n  import cmdCelItem from \"@/components/cmd-cell-item/cmd-cell-item.vue\"\r\n  import cmdAvatar from \"@/components/cmd-avatar/cmd-avatar.vue\"\r\n\r\n\tlet _self;\r\n  export default {\r\n    components: {\r\n      cmdNavBar,\r\n      cmdPageBody,\r\n      cmdTransition,\r\n      cmdCelItem,\r\n      cmdAvatar\r\n    },\r\n\tonLoad(){\r\n\t\t_self = this;\r\n\t\t_self.img = getApp().globalData.userImg;\r\n\t\t_self.name = getApp().globalData.userName;\r\n\t},\r\n    data() {\r\n      return {\r\n\t\t  name:'',\r\n\t\t  img:''\r\n\t  };\r\n    },\r\n\r\n    mounted() {},\r\n    \r\n    methods:{\r\n     exitLogin(){\r\n\t\t uni.reLaunch({\r\n\t\t     url: '/pages/login/login'\r\n\t\t });\r\n\t }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style>\r\n  .btn-logout {\r\n    margin-top: 100upx;\r\n    width: 80%;\r\n    border-radius: 50upx;\r\n    font-size: 16px;\r\n    color: #fff;\r\n    background: linear-gradient(to right, #365fff, #36bbff);\r\n  }\r\n\r\n  .btn-logout-hover {\r\n    background: linear-gradient(to right, #365fdd, #36bbfa);\r\n  }\r\n</style>\n", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./info.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./info.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1748246852010\n      var cssReload = require(\"E:/software/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}