<view class="uni-numbox"><view data-event-opts="{{[['tap',[['_calcValue',['minus']]]]]}}" class="{{['uni-numbox__minus',(inputValue<=min||disabled)?'uni-numbox--disabled':'']}}" bindtap="__e">-</view><input class="uni-numbox__value" disabled="{{disabled}}" type="number" data-event-opts="{{[['blur',[['_onBlur',['$event']]]],['input',[['__set_model',['','inputValue','$event',[]]]]]]}}" value="{{inputValue}}" bindblur="__e" bindinput="__e"/><view data-event-opts="{{[['tap',[['_calcValue',['plus']]]]]}}" class="{{['uni-numbox__plus',(inputValue>=max||disabled)?'uni-numbox--disabled':'']}}" bindtap="__e">+</view></view>