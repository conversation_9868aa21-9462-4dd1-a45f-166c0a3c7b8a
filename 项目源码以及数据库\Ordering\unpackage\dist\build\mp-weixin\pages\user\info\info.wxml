<view><cmd-nav-bar vue-id="1" back title="信息设置" bind:__l="__l"></cmd-nav-bar><cmd-page-body vue-id="2" type="top" bind:__l="__l" vue-slots="{{['default']}}"><cmd-transition vue-id="{{('3')+','+('2')}}" name="fade-up" bind:__l="__l" vue-slots="{{['default']}}"><view><cmd-cel-item vue-id="{{('4')+','+('3')}}" title="头像" slot-right arrow bind:__l="__l" vue-slots="{{['default']}}"><cmd-avatar vue-id="{{('5')+','+('4')}}" src="https://avatar.bbs.miui.com/images/noavatar_small.gif" bind:__l="__l"></cmd-avatar></cmd-cel-item><cmd-cel-item vue-id="{{('6')+','+('3')}}" title="积分" addon="566" arrow bind:__l="__l"></cmd-cel-item><cmd-cel-item vue-id="{{('7')+','+('3')}}" title="昵称" addon="Slimmer" arrow bind:__l="__l"></cmd-cel-item><cmd-cel-item vue-id="{{('8')+','+('3')}}" title="姓名" addon="Lich" arrow bind:__l="__l"></cmd-cel-item><cmd-cel-item vue-id="{{('9')+','+('3')}}" title="联系方式" addon="15676109501" arrow bind:__l="__l"></cmd-cel-item><cmd-cel-item vue-id="{{('10')+','+('3')}}" title="证件号码" addon="450112xxxxxxxx2017" arrow bind:__l="__l"></cmd-cel-item><cmd-cel-item vue-id="{{('11')+','+('3')}}" title="我的地址" addon="广西壮族自治区南宁市西乡塘区大学西路29号" arrow bind:__l="__l"></cmd-cel-item><cmd-cel-item vue-id="{{('12')+','+('3')}}" title="修改密码" arrow data-event-opts="{{[['^click',[['fnClick',['modify']]]]]}}" bind:click="__e" bind:__l="__l"></cmd-cel-item><button class="btn-logout">退出登录</button></view></cmd-transition></cmd-page-body></view>