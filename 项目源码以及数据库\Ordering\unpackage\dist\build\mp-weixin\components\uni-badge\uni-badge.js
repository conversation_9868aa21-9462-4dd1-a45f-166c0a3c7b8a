(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/uni-badge/uni-badge"],{"07e4":function(t,e,n){},"4b9c":function(t,e,n){"use strict";var u=n("07e4"),a=n.n(u);a.a},8213:function(t,e,n){"use strict";n.r(e);var u=n("8ac3"),a=n("d49a");for(var i in a)"default"!==i&&function(t){n.d(e,t,function(){return a[t]})}(i);n("4b9c");var r=n("2877"),c=Object(r["a"])(a["default"],u["a"],u["b"],!1,null,null,null);e["default"]=c.exports},"8ac3":function(t,e,n){"use strict";var u=function(){var t=this,e=t.$createElement;t._self._c},a=[];n.d(e,"a",function(){return u}),n.d(e,"b",function(){return a})},d49a:function(t,e,n){"use strict";n.r(e);var u=n("edf7"),a=n.n(u);for(var i in u)"default"!==i&&function(t){n.d(e,t,function(){return u[t]})}(i);e["default"]=a.a},edf7:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var u={name:"UniBadge",props:{type:{type:String,default:"default"},inverted:{type:Boolean,default:!1},text:{type:String,default:""},size:{type:String,default:"normal"}},methods:{onClick:function(){this.$emit("click")}}};e.default=u}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/uni-badge/uni-badge-create-component',
    {
        'components/uni-badge/uni-badge-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('543d')['createComponent'](__webpack_require__("8213"))
        })
    },
    [['components/uni-badge/uni-badge-create-component']]
]);                
