<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.aaa.mapper.SysPermissionMapperCustom" >
  
  
  <!-- 根据用户id查询url -->
  <select id="findPermissionListByUserId" parameterType="string" resultType="SysPermission">
	  SELECT 
	  * 
	FROM
	  sys_permission 
	WHERE TYPE = 'permission' 
	  AND id IN 
	  (SELECT 
	    sys_permission_id 
	  FROM
	    sys_role_permission 
	  WHERE sys_role_id IN 
	    (SELECT 
	      sys_role_id 
	    FROM
	      sys_user_role 
	    WHERE sys_user_id = #{id}))
  </select>
  
   <!-- 根据用户id查询菜单 -->
  <select id="findMenuListByUserId"  parameterType="string" resultType="SysPermission">
  		SELECT 
	  * 
	FROM
	  sys_permission 
	WHERE TYPE = 'menu' 
	  AND id IN 
	  (SELECT 
	    sys_permission_id 
	  FROM
	    sys_role_permission 
	  WHERE sys_role_id IN 
	    (SELECT 
	      sys_role_id 
	    FROM
	      sys_user_role 
	    WHERE sys_user_id = #{id}))
  </select>
  
</mapper>