<!DOCTYPE html>
<html lang="en"  xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>用户添加</title>
    <link rel="stylesheet" th:href="@{/layui/css/layui.css}">
    <script th:src="@{/layui/layui.js}"></script>
    <script th:src="@{/js/jquery-3.1.1.js}"></script>
    <style>
        .layui-form-item .layui-input{
            width:195px;
        }
    </style>
</head>
<body>
<br>
<br>
<form class="layui-form" action="" lay-filter="example">
    <input type="hidden" name="id" value="" id="verCode">
    <div class="layui-form-item">
        <label class="layui-form-label">角色名称</label>
        <div class="layui-input-block">
            <input type="text" name="name" lay-verify="title" autocomplete="off" placeholder="请输入角色名" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-input-block">
            <button type="submit" class="layui-btn" lay-submit="" lay-filter="demo1">添加</button>
            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
        </div>
    </div>
</form>
<script>
    layui.use('form', function() {
        var form = layui.form;
        // var code=(Math.floor(Math.random()*1000000));
        // $("#verCode").val(code);
        //监听提交
        form.on('submit(demo1)', function (data) {
            // layer.alert(JSON.stringify(data.field), {
            //     title: '最终的提交信息'
            // })
            $.post("add",data.field,function (result) {
                if(result.data==true){
                    layer.msg(result.msg, {
                        icon: 1,
                        time: 1000
                    }, function(){
                        var index=parent.layer.getFrameIndex(window.name); //获取当前窗口的name
                        parent.layer.close(index);		//关闭窗口
                        parent.window.location.reload();
                    });

                }else {
                    layer.msg(result.msg, {
                        icon: 7,
                        time: 3000
                    })


                }
                

            });
            return false;
        });
        //自定义验证规则
        form.verify({
            title: function(value){
                if(value.length < 2){
                    return '标题至少得2个字符啊';
                }
            }
            ,content: function(value){
                layedit.sync(editIndex);
            }
        });

    })
</script>
<script>

</script>
</body>
</html>
