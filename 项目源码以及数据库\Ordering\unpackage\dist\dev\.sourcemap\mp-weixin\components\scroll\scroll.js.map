{"version": 3, "sources": ["webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/scroll/scroll.vue?ce1a", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/scroll/scroll.vue?3b6c", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/scroll/scroll.vue?9fb7", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/scroll/scroll.vue?67fb", "uni-app:///components/scroll/scroll.vue", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/scroll/scroll.vue?ffa4", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/scroll/scroll.vue?9eaa"], "names": ["props", "requesting", "type", "value", "end", "emptyShow", "listCount", "default", "emptyUrl", "emptyText", "hasTop", "refreshSize", "bottomSize", "color", "raise", "watch", "data", "mode", "successShow", "successTran", "refreshStatus", "move", "scrollHeight1", "scrollHeight2", "scrollTop", "oldX", "mounted", "methods", "appraise", "scroll", "clearTimeout", "change", "touchend", "wx", "more", "requestingEnd", "setTimeout", "isFullScreen", "query", "_this", "initScroll"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AAC+K;AAC/K,gBAAgB,wLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkpB,CAAgB,uqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCuEtqB;EACAA;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAK;QACA;MACA;IACA;IACA;IACAC;MACAN;MACAK;QACA;MACA;IACA;IACA;IACAE;MACAP;MACAK;QACA;MACA;IACA;IACA;IACAG;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAK;QACA;MACA;IACA;IACA;IACAK;MACAV;MACAK;QACA;MACA;IACA;IACA;IACAM;MACAX;MACAK;QACA;MACA;IACA;IACA;IACAO;MACAZ;MACAC;IACA;EACA;EACAY;IACAd;MACA;IACA;IACAK;MAAA;MACA;QACA;UACA;QACA;MACA;IACA;EACA;EACAU;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IAEA;AACA;AACA;IACAC;MAAA;MACAC;MACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;;MAEA;MACA;QACA;MACA;MAEA;MAEA;QACA;MACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;MAEA;QACA;MACA;MAEA;QACAC;QACA;QACA;QACA;QACA;MACA;QACA;QACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;QACA;MACA;MAEA;QACA;QACA;QAEAC;UACA;UACAA;YACA;YACA;YACAA;cACA;cACA;cACA;cACA;YACA;UACA;QACA;MAEA;QACA;UACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;MAEAC;MACAA;MAEAA;QACA;UACAC;QACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACA;MAEAF;MACAA;MAEAA;QACAC;QACAA;QACAA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC/SA;AAAA;AAAA;AAAA;AAAquC,CAAgB,ksCAAG,EAAC,C;;;;;;;;;;;ACAzvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/scroll/scroll.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./scroll.vue?vue&type=template&id=6e1960b8&\"\nvar renderjs\nimport script from \"./scroll.vue?vue&type=script&lang=js&\"\nexport * from \"./scroll.vue?vue&type=script&lang=js&\"\nimport style0 from \"./scroll.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/scroll/scroll.vue\"\nexport default component.exports", "export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./scroll.vue?vue&type=template&id=6e1960b8&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./scroll.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./scroll.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<movable-area>\r\n\t\t<movable-view\r\n\t\t\tclass=\"scroll\"\r\n\t\t\tid=\"movableview\"\r\n\t\t\t:style=\"{ height : 'calc(100vh + '+ refreshSize +'rpx)'}\"\r\n\t\t\t@change=\"change\"\r\n\t\t\t@touchend=\"touchend\"\r\n\t\t\tdirection=\"vertical\"\r\n\t\t\t:out-of-bounds=\"true\"\r\n\t\t\t:disabled=\"refreshStatus >= 3\"\r\n\t\t\t:y='move'>\r\n\t\t\t<scroll-view\r\n\t\t\t\tclass=\"scroll__view\"\r\n\t\t\t\t:style=\"{'padding-bottom': bottomSize + 'rpx', 'padding-top': (hasTop ? refreshSize : 0) + 'rpx'}\"\r\n\t\t\t\t:scroll-y=\"refreshStatus == 1\"\r\n\t\t\t\t@scroll=\"scroll\"\r\n\t\t\t\t:scroll-top=\"scrollTop\"\r\n\t\t\t\t:lower-threshold=\"0\"\r\n\t\t\t\t@scrolltolower=\"more\">\r\n\t\t\t\t<view id=\"refresh\"\r\n\t\t\t\t\t  :class=\"['scroll__refresh', successShow ? 'scroll__refresh--hidden': '' ]\"\r\n\t\t\t\t\t  :style=\"{height: refreshSize + 'rpx' , padding: (refreshSize - 50) / 2 + 'rpx 0'}\">\r\n\t\t\t\t\t<view class=\"la-square-jelly-box\" :style=\"{color: color}\">\r\n\t\t\t\t\t\t<view></view>\r\n\t\t\t\t\t\t<view></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view id=\"success\"\r\n\t\t\t\t\t  :class=\"['scroll__success', successShow ? 'scroll__success--show' : '', successTran ? 'scroll__success--tran' : '']\"\r\n\t\t\t\t\t  :style=\"{top: refreshSize - 60 + 'rpx', color: color}\">\r\n\t\t\t\t\t<view>刷新成功</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view id=\"container\">\r\n\t\t\t\t\t<slot></slot>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view v-if=\"listCount === 0 && emptyShow\" class=\"scroll__empty\">\r\n\t\t\t\t\t<image :src=\"emptyUrl\"></image>\r\n\t\t\t\t\t<view>{{emptyText}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-if=\"listCount !== 0\" class=\"scroll__bottom\">\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view style=\"margin-bottom: 10px;\" v-if=\"raise\">\r\n\t\t\t\t\t\t<button id=\"button_evaluate\" @click=\"appraise\">对本次服务进行评价</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"scroll__loading\" v-if=\"end\">\r\n\t\t\t\t\t\t<view class=\"text\">已全部加载</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"scroll__loading\" v-else>\r\n\t\t\t\t\t\t<view class=\"la-line-spin-fade-rotating\">\r\n\t\t\t\t\t\t\t<view></view>\r\n\t\t\t\t\t\t\t<view></view>\r\n\t\t\t\t\t\t\t<view></view>\r\n\t\t\t\t\t\t\t<view></view>\r\n\t\t\t\t\t\t\t<view></view>\r\n\t\t\t\t\t\t\t<view></view>\r\n\t\t\t\t\t\t\t<view></view>\r\n\t\t\t\t\t\t\t<view></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"scroll__text\">加载中...</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t</movable-view>\r\n\t</movable-area>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tprops: {\n\t\t\t// 加载中\n\t\t\trequesting: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tvalue: false\n\t\t\t},\n\t\t\t// 加载完毕\n\t\t\tend: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tvalue: false,\n\t\t\t},\n\t\t\t// 控制空状态的显示\n\t\t\temptyShow: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tvalue: false,\n\t\t\t},\n\t\t\t// 当前列表长度\n\t\t\tlistCount: {\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault(){\r\n\t\t\t\t\treturn 0\r\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 空状态的图片\n\t\t\temptyUrl: {\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault(){\r\n\t\t\t\t\treturn \"/static/scroll/image/empty/empty.png\"\r\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 空状态的文字提示\n\t\t\temptyText: {\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault(){\r\n\t\t\t\t\treturn \"未找到数据\"\r\n\t\t\t\t} \n\t\t\t},\n\t\t\t// 是否有header\n\t\t\thasTop: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tvalue: false\n\t\t\t},\n\t\t\t// 下拉刷新的高度\n\t\t\trefreshSize: {\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault(){\r\n\t\t\t\t\treturn 90\r\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 底部高度\n\t\t\tbottomSize: {\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault(){\r\n\t\t\t\t\treturn 0\r\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 颜色\n\t\t\tcolor: {\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault(){\r\n\t\t\t\t\treturn \"#3F82FD\"\r\n\t\t\t\t}\n\t\t\t},\r\n\t\t\t//是否显示评价\r\n\t\t\traise:{\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tvalue: false\r\n\t\t\t}\n\t\t},\r\n\t\twatch: {\r\n\t\t\trequesting(newVal, oldVal){\r\n\t\t\t\tthis.requestingEnd(newVal, oldVal);\r\n\t\t\t},\r\n\t\t\tlistCount(newVal, oldVal){\r\n\t\t\t\tif(newVal > 0){\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tthis.isFullScreen();\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\n\t\tdata() {\r\n\t\t\treturn {\n\t\t\t\tmode: 'refresh', // refresh 和 more 两种模式\n\t\t\t\tsuccessShow: false, // 显示success\n\t\t\t\tsuccessTran: false, // 过度success\n\t\t\t\trefreshStatus: 0, // 1: 下拉刷新, 2: 松开刷新, 3: 加载中, 4: 加载完成\n\t\t\t\tmove: -45, // movable-view 偏移量\n\t\t\t\tscrollHeight1: 0, // refresh view 高度负值\n\t\t\t\tscrollHeight2: 0, // refresh view - success view 高度负值\n\t\t\t\tscrollTop: 0,\r\n\t\t\t\toldX: 0,\r\n\t\t\t}\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.initScroll();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t//点击评价\r\n\t\t\tappraise(){\r\n\t\t\t\tthis.$emit('evaluate')\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t/**\n\t\t\t * 处理 bindscrolltolower 失效情况\n\t\t\t */\n\t\t\tscroll(e) {\n\t\t\t\tclearTimeout(this.timer)\n\t\t\t\tthis.timer = setTimeout(() => {\r\n\t\t\t\t\tthis.scrollTop = e.detail.scrollTop;\n\t\t\t\t}, 100)\n\t\t\t},\n\t\t\t/**\n\t\t\t * movable-view 滚动监听\n\t\t\t */\n\t\t\tchange(e) {\r\n\t\t\t\tconst refreshStatus = this.refreshStatus;\n\n\t\t\t\t// 判断如果状态大于3则返回\n\t\t\t\tif (refreshStatus >= 3) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tlet diff = e.detail.y;\r\n\n\t\t\t\tif (diff > -10) {\r\n\t\t\t\t\tthis.refreshStatus = 2;\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.oldX = diff;\r\n\t\t\t\t\tthis.refreshStatus = 1;\n\t\t\t\t}\n\t\t\t},\n\t\t\t/**\n\t\t\t * movable-view 触摸结束事件\n\t\t\t */\n\t\t\ttouchend(e) {\r\n\t\t\t\tconst refreshStatus = this.refreshStatus;\n\n\t\t\t\tif (refreshStatus >= 3) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (refreshStatus == 2) {\n\t\t\t\t\twx.vibrateShort();\r\n\t\t\t\t\tthis.refreshStatus = 3;\n\t\t\t\t\tthis.move = 0;\n\t\t\t\t\tthis.mode = 'refresh';\n\t\t\t\t\tthis.$emit('refresh');\n\t\t\t\t} else if (refreshStatus == 1) {\r\n\t\t\t\t\tthis.move = this.oldX;\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tthis.move = this.scrollHeight1;\r\n\t\t\t\t\t})\r\n\t\t\t\t}\n\t\t\t},\n\t\t\t/**\n\t\t\t * 加载更多\n\t\t\t */\n\t\t\tmore() {\n\t\t\t\tif (!this.end) {\r\n\t\t\t\t\tthis.mode = 'more';\n\t\t\t\t\tthis.$emit('more');\n\t\t\t\t}\n\t\t\t},\n\t\t\t/**\n\t\t\t * 监听 requesting 字段变化, 来处理下拉刷新对应的状态变化\n\t\t\t */\n\t\t\trequestingEnd(newVal, oldVal) {\r\n\t\t\t\tif (this.mode == 'more') {\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tif (oldVal === true && newVal === false) {\r\n\t\t\t\t\tthis.refreshStatus = 4;\n\t\t\t\t\tthis.move = this.scrollHeight2;\r\n\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthis.successShow = true;\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tthis.successTran = true;\n\t\t\t\t\t\t\tthis.move = this.scrollHeight1;\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tthis.refreshStatus = 1;\n\t\t\t\t\t\t\t\tthis.successShow = false;\n\t\t\t\t\t\t\t\tthis.successTran = false;\n\t\t\t\t\t\t\t\tthis.move = this.scrollHeight1;\n\t\t\t\t\t\t\t}, 350);\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t}, 650);\n\n\t\t\t\t} else {\n\t\t\t\t\tif (this.refreshStatus != 3) {\n\t\t\t\t\t\tthis.refreshStatus = 3;\n\t\t\t\t\t\tthis.move = 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 计算内容高度，有没有满屏，没有满屏的话自动加载下一页\r\n\t\t\t */\n\t\t\tisFullScreen() {\n\t\t\t\tlet query = this.createSelectorQuery();\r\n\t\t\t\tlet _this = this;\r\n\n\t\t\t\tquery.select(\"#movableview\").boundingClientRect();\n\t\t\t\tquery.select(\"#container\").boundingClientRect();\n\n\t\t\t\tquery.exec(function (res) {\r\n\t\t\t\t\tif(res[0].height > res[1].height){\r\n\t\t\t\t\t\t_this.more();\r\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\r\n\t\t\t\n\t\t\t/**\n\t\t\t * 初始化scroll组件参数, 动态获取 下拉刷新区域 和 success 的高度\n\t\t\t */\n\t\t\tinitScroll() {\n\t\t\t\tlet query = this.createSelectorQuery();\r\n\t\t\t\tlet _this = this;\r\n\n\t\t\t\tquery.select(\"#refresh\").boundingClientRect();\n\t\t\t\tquery.select(\"#success\").boundingClientRect();\n\n\t\t\t\tquery.exec(function (res) {\r\n\t\t\t\t\t_this.scrollHeight1 = -res[0].height;\n\t\t\t\t\t_this.scrollHeight2 = res[1].height - res[0].height;\n\t\t\t\t\t_this.move = -res[0].height;\n\t\t\t\t});\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n@import \"../../static/css/variables.scss\";\r\n\r\n\r\n$refresh-height: 90rpx;\r\n$success-height: 60rpx;\r\n$success-top: $refresh-height - $success-height;\r\n$refresh-padding: ($refresh-height - 50rpx) / 2;\r\n\r\nmovable-area {\r\n\twidth: 100%;\r\n\theight: 100vh;\r\n}\r\n\r\n.scroll {\r\n\twidth: 100%;\r\n\theight: calc(100vh + #{$refresh-height});\r\n\r\n\t&__view {\r\n\t\theight: 100%;\r\n\t\tposition: relative;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t&__success {\r\n\t\tposition: absolute;\r\n\t\tz-index: 9;\r\n\t\ttop: $success-top;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: $success-height;\r\n\t\tline-height: $success-height;\r\n\t\tfont-size: 24rpx;\r\n\t\ttext-align: center;\r\n\t\topacity: 0;\r\n\t\tcolor: $mainColor;\r\n\r\n\t\t&:after {\r\n\t\t\tcontent: \" \";\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tbackground-color: currentColor;\r\n\t\t\topacity: 0.7;\r\n\t\t\ttransform: scaleX(0);\r\n\t\t\ttransition: transform 0.15s ease-in-out;\r\n\t\t\tz-index: 0;\r\n\t\t}\r\n\r\n\t\t& > view {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tz-index: 1;\r\n\t\t\tcolor: #ffffff;\r\n\t\t}\r\n\r\n\t\t&--show {\r\n\t\t\topacity: 1;\r\n\r\n\t\t\t&:after {\r\n\t\t\t\ttransform: scaleX(1);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&--tran {\r\n\t\t\topacity: 0;\r\n\t\t\ttransition: opacity 0.3s linear;\r\n\t\t}\r\n\t}\r\n\r\n\t&__refresh {\r\n\t\theight: $refresh-height;\r\n\t\tpadding: $refresh-padding 0;\r\n\t\tbox-sizing: border-box;\r\n\r\n\t\t&--hidden {\r\n\t\t\tvisibility: hidden;\r\n\t\t}\r\n\t}\r\n\r\n\t&__empty {\r\n\t\tpadding: 30rpx;\r\n\t\ttext-align: center;\r\n\r\n\t\timage {\r\n\t\t\twidth: 200rpx;\r\n\t\t\theight: 200rpx;\r\n\t\t\tmargin: 160rpx auto 60rpx;\r\n\t\t}\r\n\r\n\t\tview {\r\n\t\t\tcolor: #999999;\r\n\t\t}\r\n\t}\r\n\r\n\t&__bottom {\r\n\t\theight: 40rpx;\r\n\t\tpadding: 30rpx 0;\r\n\t}\r\n\r\n\t&__bottom > &__loading {\r\n\t\ttext-align: center;\r\n\t\tcolor: #999999;\r\n\t\tfont-size: 30rpx;\r\n\t}\r\n\r\n\t&__bottom > &__loading > &__text {\r\n\t\tdisplay: inline-block;\r\n\t\tvertical-align: middle;\r\n\t}\r\n}\r\n\r\n/* start: refresh */\r\n\r\n.la-square-jelly-box, .la-square-jelly-box > view {\r\n\tposition: relative;\r\n\tbox-sizing: border-box\r\n}\r\n\r\n.la-square-jelly-box {\r\n\twidth: 50rpx;\r\n\theight: 50rpx;\r\n\tmargin: 0 auto;\r\n\tmargin-top: -6rpx;\r\n\tdisplay: block;\r\n\tfont-size: 0;\r\n\tcolor: $mainColor;\r\n}\r\n\r\n.la-square-jelly-box > view {\r\n\tdisplay: inline-block;\r\n\tfloat: none;\r\n\tbackground-color: currentColor;\r\n\topacity: 0.5;\r\n}\r\n\r\n.la-square-jelly-box > view:nth-child(1), .la-square-jelly-box > view:nth-child(2) {\r\n\tposition: absolute;\r\n\tleft: 0;\r\n\twidth: 100%\r\n}\r\n\r\n.la-square-jelly-box > view:nth-child(1) {\r\n\ttop: -25%;\r\n\tz-index: 1;\r\n\theight: 100%;\r\n\tborder-radius: 10%;\r\n\tanimation: square-jelly-box-animate .6s -.1s linear infinite\r\n}\r\n\r\n.la-square-jelly-box > view:nth-child(2) {\r\n\tbottom: -9%;\r\n\theight: 10%;\r\n\tbackground: #000;\r\n\tborder-radius: 50%;\r\n\topacity: .2;\r\n\tanimation: square-jelly-box-shadow .6s -.1s linear infinite\r\n}\r\n\r\n@keyframes square-jelly-box-shadow {\r\n\t50% {\r\n\t\ttransform: scale(1.25, 1)\r\n\t}\r\n}\r\n\r\n@keyframes square-jelly-box-animate {\r\n\t17% {\r\n\t\tborder-bottom-right-radius: 10%\r\n\t}\r\n\r\n\t25% {\r\n\t\ttransform: translateY(25%) rotate(22.5deg)\r\n\t}\r\n\r\n\t50% {\r\n\t\tborder-bottom-right-radius: 100%;\r\n\t\ttransform: translateY(50%) scale(1, .9) rotate(45deg)\r\n\t}\r\n\r\n\t75% {\r\n\t\ttransform: translateY(25%) rotate(67.5deg)\r\n\t}\r\n\r\n\t100% {\r\n\t\ttransform: translateY(0) rotate(90deg)\r\n\t}\r\n}\r\n\r\n/* end: refresh */\r\n\r\n/* start: more */\r\n.la-line-spin-fade-rotating, .la-line-spin-fade-rotating > view {\r\n\tposition: relative;\r\n\tbox-sizing: border-box\r\n}\r\n\r\n.la-line-spin-fade-rotating {\r\n\tvertical-align: middle;\r\n\tdisplay: inline-block;\r\n\tfont-size: 0;\r\n\tcolor: currentColor;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.la-line-spin-fade-rotating > view {\r\n\tdisplay: inline-block;\r\n\tfloat: none;\r\n\tbackground-color: currentColor;\r\n\tborder: 0 solid currentColor\r\n}\r\n\r\n.la-line-spin-fade-rotating {\r\n\twidth: 32rpx;\r\n\theight: 32rpx;\r\n\tanimation: ball-spin-fade-rotating-rotate 6s infinite linear\r\n}\r\n\r\n.la-line-spin-fade-rotating > view {\r\n\tposition: absolute;\r\n\twidth: 2rpx;\r\n\theight: 8rpx;\r\n\tmargin: 4rpx;\r\n\tmargin-top: -4rpx;\r\n\tmargin-left: 0;\r\n\tborder-radius: 0;\r\n\tanimation: line-spin-fade-rotating 1s infinite ease-in-out\r\n}\r\n\r\n.la-line-spin-fade-rotating > view:nth-child(1) {\r\n\ttop: 15%;\r\n\tleft: 50%;\r\n\ttransform: rotate(0deg);\r\n\tanimation-delay: -1.125s\r\n}\r\n\r\n.la-line-spin-fade-rotating > view:nth-child(2) {\r\n\ttop: 25.2512626585%;\r\n\tleft: 74.7487373415%;\r\n\ttransform: rotate(45deg);\r\n\tanimation-delay: -1.25s\r\n}\r\n\r\n.la-line-spin-fade-rotating > view:nth-child(3) {\r\n\ttop: 50%;\r\n\tleft: 85%;\r\n\ttransform: rotate(90deg);\r\n\tanimation-delay: -1.375s\r\n}\r\n\r\n.la-line-spin-fade-rotating > view:nth-child(4) {\r\n\ttop: 74.7487373415%;\r\n\tleft: 74.7487373415%;\r\n\ttransform: rotate(135deg);\r\n\tanimation-delay: -1.5s\r\n}\r\n\r\n.la-line-spin-fade-rotating > view:nth-child(5) {\r\n\ttop: 84.9999999974%;\r\n\tleft: 50.0000000004%;\r\n\ttransform: rotate(180deg);\r\n\tanimation-delay: -1.625s\r\n}\r\n\r\n.la-line-spin-fade-rotating > view:nth-child(6) {\r\n\ttop: 74.7487369862%;\r\n\tleft: 25.2512627193%;\r\n\ttransform: rotate(225deg);\r\n\tanimation-delay: -1.75s\r\n}\r\n\r\n.la-line-spin-fade-rotating > view:nth-child(7) {\r\n\ttop: 49.9999806189%;\r\n\tleft: 15.0000039834%;\r\n\ttransform: rotate(270deg);\r\n\tanimation-delay: -1.875s\r\n}\r\n\r\n.la-line-spin-fade-rotating > view:nth-child(8) {\r\n\ttop: 25.2506949798%;\r\n\tleft: 25.2513989292%;\r\n\ttransform: rotate(315deg);\r\n\tanimation-delay: -2s\r\n}\r\n\r\n@keyframes ball-spin-fade-rotating-rotate {\r\n\t100% {\r\n\t\ttransform: rotate(360deg)\r\n\t}\r\n}\r\n\r\n@keyframes line-spin-fade-rotating {\r\n\t50% {\r\n\t\topacity: .2\r\n\t}\r\n\r\n\t100% {\r\n\t\topacity: 1\r\n\t}\r\n}\r\n</style>\n", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./scroll.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./scroll.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749718152961\n      var cssReload = require(\"E:/software/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}