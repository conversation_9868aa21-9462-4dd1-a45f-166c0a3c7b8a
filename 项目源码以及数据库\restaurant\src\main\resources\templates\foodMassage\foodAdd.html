<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>菜品添加</title>
    <link rel="stylesheet" href="../layui/css/layui.css">
    <script src="../layui/layui.js"></script>
    <script src="../js/jquery-3.1.1.js"></script>

</head>
<body>
<div ID="all">
    <form class="layui-form" action="/foodMassage/foodAdd" enctype="multipart/form-data">
        <div class="layui-form-item">
            <label class="layui-form-label">菜品名称</label>
            <div class="layui-input-block">
                <input type="text" id="foodTitle" name="foodTitle" required  lay-verify="title" placeholder="菜品名称" autocomplete="off" class="layui-input foodTitle">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">价格</label>
            <div class="layui-input-inline">
                <input type="text" name="foodPrice" required lay-verify="required" placeholder="0" autocomplete="off" class="layui-input">
            </div>
            <div class="layui-form-mid layui-word-aux">元</div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">类型</label>
            <div class="layui-input-block">
                <select name="type" lay-verify="required" >
                    <option th:each="c:${types}" th:value="${c.t_id}" th:text="${c.t_name}"></option>
                </select>
        </div>
        </div>
        <div>
            <label class="layui-form-label">菜品图片</label>
            <div class="layui-upload">
                <button class="layui-btn" id="uploadIMG" type="button">上传图片</button>
                <div class="layui-upload-list img">
                    <img class="layui-upload-img" id="demo1" name="file">
                    <p id="demoText"></p>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">上下架状态</label>
            <div class="layui-input-block">
                <input type="checkbox" name="state" lay-skin="switch" lay-text="上架|下架" checked value="0" disabled>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="formDemo">立即提交</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
</div>
</body>
<script>
    // 表单提交
    layui.use(['form','upload'], function(){
        var form = layui.form,
             $ = layui.jquery,
            upload = layui.upload;
        var img = null;
        //监听提交button
        form.on('submit(formDemo)', function(data){
            var obj = data.field;
            $.ajax({
                url:"selName",
                type:"post",
                data:{
                    m_name:obj.foodTitle,
                },
                success:function (data) {
                    if (data=="1"){
                        alert("此名称已存在!");
                    }else{
                        $.ajax({
                            url:"foodAdd",
                            type:"post",
                            data:{
                                m_name:obj.foodTitle,
                                m_price:obj.foodPrice,
                                t_id:obj.type,
                                m_img:img,
                                m_state:obj.state
                            },
                            dataType:"json",
                            success:function (i) {
                                if(i==1){
                                    layer.alert("添加成功!");
                                    window.location.href='foodSel.html';
                                }else if (i==0){
                                    layer.alert("添加失败!");
                                }
                            }
                        });
                    }
                }
            });
            return false;
        });
        // 文件上传
        var uploadInst = upload.render({
            elem: '#uploadIMG',
            url: '/restaurant/foodMassage/uploading',
            before: function (obj) {
                //预读本地文件示例，不支持ie8
                obj.preview(function (index, file, result) {
                    //获取图片的文件名
                    img = file.name;
                    $('#demo1').attr('src', result); //图片链接（base64）
                });
            },
            //done 上传后的回调函数 res 接口返回的数据
            done: function (res) {
                //如果上传失败
                if (res.code = 0) {
                    return layer.msg('上传失败');
                }
                //上传成功
                else{
                    // return layer.msg('上传成功');
                }
            },
            //请求异常回调
            error: function () {
                //演示失败状态，并实现重传
                var demoText = $('#demoText');
                demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-xs demo-reload">重试</a>');
                demoText.find('.demo-reload').on('click', function () {
                    uploadInst.upload();
                });
            }
        });
        // 监听下拉框时间
        form.on('select(test)', function(data){
            console.log(data);
        });
    });
</script>
<style>
    #all{
        width:500px;
        height: 800px;
        margin: 100px;
    }
    .foodTitle{
        width: 200px;
    }
    #demo1{
        left: 100px;
        width: 100px;
        height: 100px;
        margin-left: 109px;
    }

</style>
</html>