
.uni-swiper__warp {
	position: relative;
	width: 100%;
	-webkit-box-sizing: border-box;
	        box-sizing: border-box;
	overflow: hidden;
}
.uni-swiper__dots-box {
	position: absolute;
	bottom: 20rpx;
	display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	    -ms-flex-pack: center;
	        justify-content: center;
	-webkit-box-align: center;
	-webkit-align-items: center;
	    -ms-flex-align: center;
	        align-items: center;
	-webkit-box-sizing: box-sizing;
	        box-sizing: box-sizing;
	width: 100%;
}
.uni-swiper__dots-item {
	-webkit-flex-shrink: 0;
	    -ms-flex-negative: 0;
	        flex-shrink: 0;
	width: 16rpx;
	border-radius: 50%;
	margin-left: 12rpx;
	background: rgba(0, 0, 0, .3);
	-webkit-transition: all 0.2s linear;
	-o-transition: all 0.2s linear;
	transition: all 0.2s linear;
}
.uni-swiper__dots-item:first-child {
	margin: 0;
}
.uni-swiper__dots-default {
	border-radius: 50%;
}
.uni-swiper__dots-long {
	border-radius: 100rpx;
}
.uni-swiper__dots-bar {
	border-radius: 100rpx;
}
.uni-swiper__dots-nav {
	bottom: 0;
	height: 80rpx;
	-webkit-box-pack: start;
	-webkit-justify-content: flex-start;
	    -ms-flex-pack: start;
	        justify-content: flex-start;
	background: rgba(0, 0, 0, 0.2);
	-webkit-box-sizing: box-sizing;
	        box-sizing: box-sizing;
	overflow: hidden;
}
.uni-swiper__dots-nav-item {
	overflow: hidden;
	-o-text-overflow: ellipsis;
	   text-overflow: ellipsis;
	white-space: nowrap;
	font-size: 28rpx;
	color: #fff;
	-webkit-box-sizing: box-sizing;
	        box-sizing: box-sizing;
	margin: 0 30rpx;
}
.uni-swiper__dots-indexes {
	display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	    -ms-flex-pack: center;
	        justify-content: center;
	-webkit-box-align: center;
	-webkit-align-items: center;
	    -ms-flex-align: center;
	        align-items: center;
	color: #fff;
	font-size: 24rpx;
}

