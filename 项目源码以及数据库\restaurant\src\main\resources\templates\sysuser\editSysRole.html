<!DOCTYPE html>
<html lang="en"  xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>角色修改</title>
    <link rel="stylesheet" th:href="@{/layui/css/layui.css}">
    <script th:src="@{/layui/layui.js}"></script>
    <script th:src="@{/js/jquery-3.1.1.js}"></script>
    <style>
        .layui-form-item .layui-input{
            width:195px;
        }
    </style>
</head>
<body>
<br>
<br>
<form class="layui-form" action="" lay-filter="example">

    <div class="layui-form-item">
        <label class="layui-form-label">角色名称</label>
        <div class="layui-input-block" th:each="r:${sysRoleOne}">
            <input type="hidden" name="id" th:value="${r.id}" id="verCode">
            <input type="text" name="name" th:value="${r.name}" lay-verify="title" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-input-block">
            <button type="submit" class="layui-btn" lay-submit="" lay-filter="demo1">修改</button>
            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
        </div>
    </div>
</form>
</body>
</html>
<script>
    layui.use('form', function() {
        var form = layui.form;
        form.on('submit(demo1)', function (data) {
            // layer.alert(JSON.stringify(data.field), {
            //     title: '最终的提交信息'
            // })
            $.post("updateRole",data.field,function (result) {
                if(result.data==true){
                    layer.msg(result.msg, {
                        icon: 1,
                        time: 1000
                    }, function(){
                        var index=parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                        parent.window.location.reload();
                    });
                }else {
                    layer.msg(result.msg, {
                        icon: 7,
                        time: 3000
                    })
                }
            });
            return false;
        });
    })
</script>