{"status": {"code": 200, "message": "操作成功"}, "data": [{"id": "001", "title": "湖南省", "last": true, "parentId": "0", "basicData": {"data1": "自定义数据1", "data2": "自定义数据2", "data3": "自定义'我带了单引号'3"}, "children": []}, {"id": "002", "title": "湖北省", "last": true, "parentId": "0", "basicData": {"data1": "自定义数据11", "data2": "自定义数据22", "data3": "自定义'我带了单引号'33"}, "children": []}, {"id": "003", "title": "广东省", "last": true, "parentId": "0", "basicData": {"data1": "自定义数据111", "data2": "自定义数据222", "data3": "自定义'我带了单引号'333"}, "children": []}, {"id": "004", "title": "浙江省", "last": true, "parentId": "0", "basicData": {"data1": "自定义数据1111", "data2": "自定义数据2222", "data3": "自定义'我带了单引号'3333"}, "children": []}, {"id": "005", "title": "福建省", "last": true, "parentId": "0", "basicData": {"data1": "自定义数据11111", "data2": "自定义数据22222", "data3": "自定义'我带了单引号'33333"}, "children": []}]}