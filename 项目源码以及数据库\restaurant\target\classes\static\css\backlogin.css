body {
    /*background-color: #e7e7e7;*/
    /*background-size:cover;*/
    background:url("../images/bg.jpg") no-repeat;
}

input:-webkit-autofill {
    -webkit-box-shadow: inset 0 0 0 1000px #fff;
    background-color: transparent;

}

.admin-login-background {
    width: 400px;
    height: 300px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -220px;
    margin-top: -100px;
    background: rgba(0,0,0,0.2) ;
    border-radius: 5%;
}
#formLogin{
    width:300px;
    height: 250px;
    margin:25px 40px;
}
.admin-header {
    margin-top: -100px;
    margin-bottom: 20px;
}

.admin-logo {
    /*width: 280px;*/
    width:100px;
    height:100px;
    border-radius: 50px;
    margin-left:80px;

}

.admin-button {
    margin-top: 20px;
}

.admin-input {

    border-top-style: none;
    border-right-style: solid;
    border-bottom-style: solid;
    border-left-style: solid;
    height: 50px;
    width: 300px;
    padding-bottom: 0px;
}


.admin-input-username {
    border-top-style: solid;
    border-radius: 10px 10px 10px 10px;
}
.admin-input-password{
    border-radius: 10px 10px 10px 10px;
}
.admin-input-verify {
    border-radius: 0 0 10px 10px;
}

.admin-button {
    width: 300px;
    height: 50px;
    border-radius: 4px;
    background-color: #2d8cf0;
}

.admin-icon {
    margin-left: 260px;
    margin-top: 10px;
    font-size: 30px;
}

i {
    position: absolute;
}

.admin-captcha {
    position: absolute;
    margin-left: 205px;
    margin-top: -40px;
}
