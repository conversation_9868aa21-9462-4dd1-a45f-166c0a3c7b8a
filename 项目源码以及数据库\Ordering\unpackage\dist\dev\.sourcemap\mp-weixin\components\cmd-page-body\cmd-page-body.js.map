{"version": 3, "sources": ["webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/cmd-page-body/cmd-page-body.vue?b865", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/cmd-page-body/cmd-page-body.vue?3006", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/cmd-page-body/cmd-page-body.vue?bca2", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/cmd-page-body/cmd-page-body.vue?9fb7", "uni-app:///components/cmd-page-body/cmd-page-body.vue", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/cmd-page-body/cmd-page-body.vue?2cfa", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/cmd-page-body/cmd-page-body.vue?89b0"], "names": ["name", "props", "type", "default", "backgroundColor", "data", "bodyHeight", "computed", "setBodyClass", "bodyClass", "setBackgroundColor", "mounted", "windowHeight"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACa;;;AAGzE;AAC+K;AAC/K,gBAAgB,wLAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAypB,CAAgB,8qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;ACO7qB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,eAQA;EACAA;EAEAC;IACA;AACA;AACA;IACAC;MACAA;MACAC;IACA;IACA;AACA;AACA;IACAC;MACAF;MACAC;IACA;EACA;EAEAE;IACA;MACAC;IACA;EACA;EAEAC;IACA;AACA;AACA;IACAC;MACA;MACA;QACAC;QACAA;MACA;MACA;QACAA;QACAA;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;QACAN;MACA;MACA;IACA;EACA;EAEAO;IACA;IACA;IAEAC;IAGAA;IAEA;MACAA;IACA;IACA;MACAA;IACA;IACA;MACAA;IACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;AC1FA;AAAA;AAAA;AAAA;AAA+7B,CAAgB,y7BAAG,EAAC,C;;;;;;;;;;;ACAn9B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/cmd-page-body/cmd-page-body.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./cmd-page-body.vue?vue&type=template&id=7ac746a0&\"\nvar renderjs\nimport script from \"./cmd-page-body.vue?vue&type=script&lang=js&\"\nexport * from \"./cmd-page-body.vue?vue&type=script&lang=js&\"\nimport style0 from \"./cmd-page-body.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/cmd-page-body/cmd-page-body.vue\"\nexport default component.exports", "export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cmd-page-body.vue?vue&type=template&id=7ac746a0&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cmd-page-body.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cmd-page-body.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :class=\"setBodyClass\" :style=\"setBackgroundColor+bodyHeight\">\r\n\t\t<slot></slot>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**  \r\n\t * 导航栏内容页组件  \r\n\t * @description 针对使用底部导航栏组件 cmd-bottom-nav 或顶部导航栏组件 cmd-nav-bar 时。  \r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=207  \r\n\t * @property {String} type 使用导航栏类型 - 默认top，支持：top、bottom、top-bottom  \r\n\t * @property {String} background-color 内容区背景颜色 - 默认白色  \r\n\t * @example <cmd-page-body type=\"top-bottom\"></cmd-page-body>  \r\n\t */\r\n\texport default {\r\n\t\tname: 'cmd-page-body',\r\n\r\n\t\tprops: {\r\n\t\t\t/**\r\n\t\t\t * 使用导航栏类型,默认top，top、bottom、top-bottom\r\n\t\t\t */\r\n\t\t\ttype: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'top'\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 内容区背景颜色,默认#ffffff\r\n\t\t\t */\r\n\t\t\tbackgroundColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tbodyHeight: 0\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tcomputed: {\r\n\t\t\t/**\r\n\t\t\t * 内容区样式根据导航类型设置\r\n\t\t\t */\r\n\t\t\tsetBodyClass() {\r\n\t\t\t\tlet bodyClass = ['cmd-page-body', 'cmd-page-body-top-bottom']\r\n\t\t\t\tif (this.type == 'top') {\r\n\t\t\t\t\tbodyClass.splice(1)\r\n\t\t\t\t\tbodyClass.push('cmd-page-body-top');\r\n\t\t\t\t}\r\n\t\t\t\tif (this.type == 'bottom') {\r\n\t\t\t\t\tbodyClass.splice(1)\r\n\t\t\t\t\tbodyClass.push('cmd-page-body-bottom');\r\n\t\t\t\t}\r\n\t\t\t\treturn bodyClass;\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 内容区背景颜色\r\n\t\t\t */\r\n\t\t\tsetBackgroundColor() {\r\n\t\t\t\tlet backgroundColor = \"background: #ffffff;\";\r\n\t\t\t\tif (this.backgroundColor) {\r\n\t\t\t\t\tbackgroundColor = `background: ${this.backgroundColor};`;\r\n\t\t\t\t}\r\n\t\t\t\treturn backgroundColor;\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tmounted() {\r\n\t\t\t// 初始默认内容高度 \r\n\t\t\tlet windowHeight = uni.getSystemInfoSync().windowHeight;\r\n\t\t\t// #ifndef H5\r\n\t\t\twindowHeight -= uni.getSystemInfoSync().statusBarHeight;\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef MP\r\n\t\t\twindowHeight -= 5;\r\n\t\t\t// #endif\r\n      if (this.type == 'top') {\r\n      \twindowHeight -= uni.upx2px(88);\r\n      }\r\n      if (this.type == 'bottom') {\r\n      \twindowHeight -= uni.upx2px(118);\r\n      }\r\n      if (this.type == 'top-bottom') {\r\n      \twindowHeight -= uni.upx2px(206);\r\n      }\r\n\t\t\tthis.bodyHeight = `min-height:${windowHeight}px`;\r\n\t\t},\r\n\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.cmd-page-body {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbackground: #ffffff;\r\n\t}\r\n\r\n\t.cmd-page-body-top-bottom {\r\n\t\tpadding-bottom: 118upx;\r\n\t\tpadding-top: 88upx;\r\n\t\ttop: var(--status-bar-height);\r\n\t}\r\n\r\n\t.cmd-page-body-bottom {\r\n\t\tpadding-bottom: 118upx;\r\n\t}\r\n\r\n\t.cmd-page-body-top {\r\n\t\tpadding-top: 88upx;\r\n\t\ttop: var(--status-bar-height);\r\n\t}\r\n</style>\n", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cmd-page-body.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cmd-page-body.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749718152608\n      var cssReload = require(\"E:/software/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}