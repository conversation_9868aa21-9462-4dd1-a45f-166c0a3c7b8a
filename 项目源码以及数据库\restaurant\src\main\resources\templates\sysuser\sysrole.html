<!DOCTYPE html>
<html lang="en"  xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>角色查询</title>
    <link rel="stylesheet" th:href="@{/layui/css/layui.css}">
    <script th:src="@{/layui/layui.js}"></script>
    <link rel="stylesheet" th:href="@{/layui_ext/dtree/dtree.css}">
    <link rel="stylesheet" th:href="@{/layui_ext/dtree/font/dtreefont.css}">
    <script th:src="@{/js/jquery-3.1.1.js}"></script>
    <!--<script th:src="@{/layui_ext/dist/dtree.js}"></script>-->
</head>
<body>
<!--<script type="text/html" id="toolbarDemo">-->
<div class="layui-btn-group">
    <button type="button" class="layui-btn" id="addSysRole">设置角色</button>
    <!--<button class="layui-btn" id="export">-->
        <!--<i class="iconfont icon-export"></i> 导出-->
    <!--</button>-->
</div>
 <!--</script>-->
<div>
    <table class="layui-hide" id="roleAll" lay-filter="demo"></table>
</div>
<script type="text/html" id="barDemo">
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="detail" id="showPer">授权 </a>
    <!--<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>-->
</script>
<script th:inline="none">
    layui.use(['table','layer','dtree','form'], function() {
        var table = layui.table;
        var layer = layui.layer;
        var dtree = layui.dtree;
        var form=layui.form;
        //var exportData = " ";			//定义全局变量
        var roleAllList=table.render({
            elem: '#roleAll'
            , url: 'queryRoleData'
            ,toolbar: '#toolbarDemo' //开启头部工具栏
            ,defaultToolbar: []
            , cellMinWidth: 80
            , cols: [[
                {field: 'id', title: 'ID',hide:true}
                ,{field: 'name', width: '30%', title: '角色名'}
                , {
                    field: 'available', width: '30%', title: '角色状态',
                    templet:function(data){
                        var state = "";
                        if(data.available=="0"){
                            state = "<input type='checkbox' value='" + data.id + "' id='available' lay-filter='stat' checked='checked' name='available'  lay-skin='switch' lay-text='启用|禁用' >";

                        }else if(data.available=="1"){
                            state = "<input type='checkbox' value='" + data.id + "' id='available' lay-filter='stat'  name='available'  lay-skin='switch' lay-text='启用|禁用' >";

                        }
                        return state;
                    }

                },
                {field:'action',width:'30%',title:'操作',toolbar: '#barDemo'}
            ]]
            , page: true
            , limit: 1
            , limits: [1, 2, 4]
          //  ,done: function (res, curr, count) {
            //    $.ajax({
              //      type: "post",
              //      url: "AllRole",
               //     async: false,
               //     dataType: "json",
                //    success: function(data){
                        // 放到数组
                        // var htmlArray = [],
                        //     members = data.data;

                        // for (var i = 0; i < members.length; i++){
                        //     if (members[i].available == 0) {
                        //         htmlArray.push('<span>启用</span>');
                        //     }
                        //     else {
                        //         htmlArray.push('<span>禁用</span>');
                        //     }
                        // }


                   //  exportData=data.data;
                  //  }
             //   })
          //  }
        })
        // $("#export").click(function(){
        //
        //     table.exportFile(roleAllList.config.id,exportData, 'xls');
        // })
        $('#addSysRole').on('click',function () {
            layer.open({
                title: '角色添加',
                type: 2,
                content: 'addSysRole',    // 设置跳转的url，跳转到对应的页面
                area: ['400px','400px'],
                //btn: ['添加','取消'],
                yes: function (index,layero) {
                    // // 获取弹出层中的form表单元素
                    // var formSubmit=layer.getChildFrame('form', index);
                    // // 提交form表单（不会触发表单验证）
                    // formSubmit.submit();

                    // 获取弹出层中的form表单元素
                    var formSubmit=layer.getChildFrame('form', index);
                    // 获取表单中的提交按钮（在我的表单里第一个button按钮就是提交按钮，使用find方法寻找即可）
                    // var submited = formSubmit.find('button')[0];
                    // 触发点击事件，会对表单进行验证，验证成功则提交表单，失败则返回错误信息
                    submited.click();

                    // 弹出层关闭的操作在子层的js代码中完成

                }
            });
        });
        //监听开关事件
        form.on('switch(stat)', function (data) {

            var contexts;
            var sta;
            var id= data.elem.value;

            if ( data.elem.checked== true) {
                contexts = "启用";
                sta = 0;
                $.ajax({
                    type: "post",
                    async: false,
                    data: "id=" + id + "&available=" + 0,
                    url: "changeRoleState",
                    success: function(data) {
                        flag = data
                    },
                });

            } else if(data.elem.value!='sr-000001'){
                contexts = "禁用";
                sta = 1;

                    $.ajax({
                        type: "post",
                        async: false,
                        data: "id=" + id + "&available=" + 1,
                        url: "changeRoleState",
                        success: function(data) {
                            flag = data
                        }
                    });



            }else if(data.elem.value=='sr-000001'){
                table.reload('roleAll');
            }

        })
        table.on('tool(demo)', function(obj){
            var data = obj.data;
            if(obj.event === 'detail'){
              //  layer.msg('ID：'+ data.id + ' 的查看操作');
                var id=data.id;
                layer.open({
                    title: '角色权限',
                    type: 2,
                    content: 'rolePermission?id='+id,    // 设置跳转的url，跳转到对应的页面
                    area: ['400px','400px'],
                    yes: function (index,layero) {
                        var formSubmit=layer.getChildFrame('form', index);
                        submited.click();
                    }
                });

            }else if(obj.event === 'del'){
                layer.confirm('真的删除行么', function(index){
                    obj.del();
                    layer.close(index);
                });
            } else if(obj.event === 'edit'){
                //layer.alert('编辑行：<br>'+ JSON.stringify(data))
                //layer.alert(data.id);
                var id=data.id;
                layer.open({
                    title: '角色编辑',
                    type: 2,
                    content: 'editSysRole?id='+id,    // 设置跳转的url，跳转到对应的页面
                    area: ['400px','400px'],
                     yes: function (index,layero) {
                        var formSubmit=layer.getChildFrame('form', index);
                        submited.click();
                    }
                });
            }
        });
    })
</script>

</body>
</html>