{"version": 3, "sources": ["webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/cmd-nav-bar/cmd-nav-bar.vue?ed9b", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/cmd-nav-bar/cmd-nav-bar.vue?1f59", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/cmd-nav-bar/cmd-nav-bar.vue?a8b3", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/cmd-nav-bar/cmd-nav-bar.vue?1928", "uni-app:///components/cmd-nav-bar/cmd-nav-bar.vue", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/cmd-nav-bar/cmd-nav-bar.vue?1484", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/cmd-nav-bar/cmd-nav-bar.vue?1f14"], "names": ["name", "components", "cmdIcon", "props", "fixed", "type", "default", "fontColor", "backgroundColor", "title", "back", "leftText", "leftTitle", "rightText", "rightColor", "iconOne", "iconTwo", "iconThree", "iconFour", "computed", "setFontColor", "setBackgroundColor", "methods", "$_iconOneClick", "uni", "$_iconTwoClick", "$_iconThreeClick", "$_iconFourClick", "$_leftTextClick", "$_rightTextClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AAC+K;AAC/K,gBAAgB,wLAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAupB,CAAgB,4qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC+B3qB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAxBA,eAyBA;EACAA;EAEAC;IACAC;EACA;EAEAC;IACA;AACA;AACA;IACAC;MACAC;MACAC;IACA;IACA;AACA;AACA;IACAC;MACAF;MACAC;IACA;IACA;AACA;AACA;IACAE;MACAH;MACAC;IACA;IACA;AACA;AACA;IACAG;MACAJ;MACAC;IACA;IACA;AACA;AACA;IACAI;MACAL;MACAC;IACA;IACA;AACA;AACA;IACAK;MACAN;MACAC;IACA;IACA;AACA;AACA;IACAM;MACAP;MACAC;IACA;IACA;AACA;AACA;IACAO;MACAR;MACAC;IACA;IACA;AACA;AACA;IACAQ;MACAT;MACAC;IACA;IACA;AACA;AACA;IACAS;MACAV;MACAC;IACA;IACA;AACA;AACA;IACAU;MACAX;MACAC;IACA;IACA;AACA;AACA;IACAW;MACAZ;MACAC;IACA;IACA;AACA;AACA;IACAY;MACAb;MACAC;IACA;EACA;EAEAa;IACA;AACA;AACA;IACAC;MACA;MACA;QACAb;MACA;MACA;IACA;IACA;AACA;AACA;IACAc;MACA;MACA;QACAb;MACA;MACA;IACA;EACA;EAEAc;IACA;AACA;AACA;IACAC;MACA;QACAC;MACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;AC/NA;AAAA;AAAA;AAAA;AAA67B,CAAgB,u7BAAG,EAAC,C;;;;;;;;;;;ACAj9B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/cmd-nav-bar/cmd-nav-bar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./cmd-nav-bar.vue?vue&type=template&id=5eb37d9c&\"\nvar renderjs\nimport script from \"./cmd-nav-bar.vue?vue&type=script&lang=js&\"\nexport * from \"./cmd-nav-bar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./cmd-nav-bar.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/cmd-nav-bar/cmd-nav-bar.vue\"\nexport default component.exports", "export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cmd-nav-bar.vue?vue&type=template&id=5eb37d9c&\"", "var components\ntry {\n  components = {\n    cmdIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/cmd-icon/cmd-icon\" */ \"@/components/cmd-icon/cmd-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cmd-nav-bar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cmd-nav-bar.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :class=\"fixed ? 'cmd-nav-bar-fixed' : ''\" :style=\"setBackgroundColor\">\r\n\t\t<view class=\"status-bar\"></view>\r\n\t\t<view class=\"cmd-nav-bar\">\r\n\t\t\t<view class=\"cmd-nav-bar-left\">\r\n\t\t\t\t<view v-if=\"leftTitle\" class=\"cmd-nav-bar-left-title\" :style=\"'color:'+setFontColor\">{{leftTitle}}</view>\r\n\t\t\t\t<view v-if=\"back && !leftTitle || iconOne && !leftTitle \" @tap=\"$_iconOneClick\" class=\"cmd-nav-bar-left-icon\">\r\n\t\t\t\t\t<cmd-icon :type=\"back ?'chevron-left' : iconOne\" size=\"24\" :color=\"setFontColor\"></cmd-icon>\r\n\t\t\t\t</view>\r\n\t\t\t\t<text v-if=\"leftText && !leftTitle\" @tap=\"$_leftTextClick\" :style=\"'color:'+setFontColor\">{{leftText}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"!leftTitle\" class=\"cmd-nav-bar-title\" :style=\"'color:'+setFontColor\">{{title}}</view>\r\n\t\t\t<view class=\"cmd-nav-bar-right\">\r\n\t\t\t\t<view v-if=\"iconThree && iconFour && !rightText\" @tap=\"$_iconFourClick\" class=\"cmd-nav-bar-right-icon\" style=\"margin-left: 0;\">\r\n\t\t\t\t\t<cmd-icon :type=\"iconFour\" size=\"24\" :color=\"setFontColor\"></cmd-icon>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-if=\"iconTwo && iconThree\" @tap=\"$_iconThreeClick\" class=\"cmd-nav-bar-right-icon\">\r\n\t\t\t\t\t<cmd-icon :type=\"iconThree\" size=\"24\" :color=\"setFontColor\"></cmd-icon>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-if=\"iconTwo\" @tap=\"$_iconTwoClick\" class=\"cmd-nav-bar-right-icon\">\r\n\t\t\t\t\t<cmd-icon :type=\"iconTwo\" size=\"24\" :color=\"setFontColor\"></cmd-icon>\r\n\t\t\t\t</view>\r\n\t\t\t\t<text v-if=\"rightText\" @tap=\"$_rightTextClick\" class=\"cmd-nav-bar-right-text\" :style=\"(rightColor != '' ? 'color:'+rightColor : 'color:'+setFontColor)\">{{rightText}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport cmdIcon from \"../cmd-icon/cmd-icon.vue\"\r\n\r\n\t/**  \r\n\t * 导航栏组件 \r\n\t * @description 避免用过多的元素填满导航栏。一般情况下，一个『返回按钮』、一个『标题』、一个『当前视图的控件』就足够  \r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=199  \r\n\t * @property {Boolean} fixed 导航栏固定到页面顶部 - 默认true  \r\n\t * @property {Boolean} back 导航栏左侧返回按钮 - 默认false,点击返回上层  \r\n\t * @property {String} left-text 导航栏左侧文字 - 可同显导航栏左侧返回按钮  \r\n\t * @property {String} left-title 导航栏左侧标题 - 不可显示导航栏左侧文字、图标一、导航栏中心标题  \r\n\t * @property {String} title 导航栏中心标题  \r\n\t * @property {String} right-text 导航栏右侧文字  \r\n\t * @property {String} right-color 导航栏右侧文字颜色  \r\n\t * @property {String} font-color 导航栏内文字、图标的颜色  \r\n\t * @property {String} background-color 导航栏背景颜色  \r\n\t * @property {String} icon-one 导航栏图标一 - 不可与导航栏左侧返回按钮、导航栏左侧标题同显  \r\n\t * @property {String} icon-two 导航栏图标二  \r\n\t * @property {String} icon-three 导航栏图标三 - 须显有导航栏图标二  \r\n\t * @property {String} icon-four 导航栏图标四 - 不可与导航栏右侧文字同显  \r\n\t * @event {Function} iconOne 导航栏图标一 点击事件  \r\n\t * @event {Function} iconTwo 导航栏图标二 点击事件  \r\n\t * @event {Function} iconThree 导航栏图标三 点击事件  \r\n\t * @event {Function} iconFour 导航栏图标四 点击事件  \r\n\t * @event {Function} leftText 导航栏左侧文字 点击事件  \r\n\t * @event {Function} rightText 导航栏右侧文字 点击事件  \r\n\t * @example <cmd-nav-bar title=\"基本\"></cmd-nav-bar>  \r\n\t */\r\n\texport default {\r\n\t\tname: 'cmd-nav-bar',\r\n\r\n\t\tcomponents: {\r\n\t\t\tcmdIcon\r\n\t\t},\r\n\r\n\t\tprops: {\r\n\t\t\t/**\r\n\t\t\t * 固定到页面顶部\r\n\t\t\t */\r\n\t\t\tfixed: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 文字图标颜色\r\n\t\t\t */\r\n\t\t\tfontColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 导航栏背景颜色\r\n\t\t\t */\r\n\t\t\tbackgroundColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 导航栏标题\r\n\t\t\t */\r\n\t\t\ttitle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 导航栏左侧返回按钮，默认点击返回上层\r\n\t\t\t */\r\n\t\t\tback: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 左侧文字可同显返回按钮\r\n\t\t\t */\r\n\t\t\tleftText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 左侧显示标题，不可显示左侧文字图标\r\n\t\t\t */\r\n\t\t\tleftTitle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 右侧文字\r\n\t\t\t */\r\n\t\t\trightText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 右侧文字颜色\r\n\t\t\t */\r\n\t\t\trightColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 图标一，不可与返回按钮,左侧标题同显\r\n\t\t\t */\r\n\t\t\ticonOne: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 图标二\r\n\t\t\t */\r\n\t\t\ticonTwo: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 图标三，须显有图标二\r\n\t\t\t */\r\n\t\t\ticonThree: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 图标四，不可与右侧文字同显\r\n\t\t\t */\r\n\t\t\ticonFour: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tcomputed: {\r\n\t\t\t/**\r\n\t\t\t * 设置标题图标颜色\r\n\t\t\t */\r\n\t\t\tsetFontColor() {\r\n\t\t\t\tlet fontColor = '#000';\r\n\t\t\t\tif (this.fontColor != '') {\r\n\t\t\t\t\tfontColor = this.fontColor;\r\n\t\t\t\t}\r\n\t\t\t\treturn fontColor;\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 设置背景颜色\r\n\t\t\t */\r\n\t\t\tsetBackgroundColor() {\r\n\t\t\t\tlet backgroundColor = 'background: #fff';\r\n\t\t\t\tif (this.backgroundColor != '') {\r\n\t\t\t\t\tbackgroundColor = `background: ${this.backgroundColor};`;\r\n\t\t\t\t}\r\n\t\t\t\treturn backgroundColor;\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\t/**\r\n\t\t\t * 图标一点击事件\r\n\t\t\t */\r\n\t\t\t$_iconOneClick() {\r\n\t\t\t\tif (this.back) {\r\n\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$emit(\"iconOne\");\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 图标二点击事件\r\n\t\t\t */\r\n\t\t\t$_iconTwoClick() {\r\n\t\t\t\tthis.$emit(\"iconTwo\");\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 图标三点击事件\r\n\t\t\t */\r\n\t\t\t$_iconThreeClick() {\r\n\t\t\t\tthis.$emit(\"iconThree\");\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 图标四点击事件\r\n\t\t\t */\r\n\t\t\t$_iconFourClick() {\r\n\t\t\t\tthis.$emit(\"iconFour\");\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 左侧文字点击事件\r\n\t\t\t */\r\n\t\t\t$_leftTextClick() {\r\n\t\t\t\tthis.$emit(\"leftText\");\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 右侧文字点击事件\r\n\t\t\t */\r\n\t\t\t$_rightTextClick() {\r\n\t\t\t\tthis.$emit(\"rightText\");\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/* 固定到页面顶部 */\r\n\t.cmd-nav-bar-fixed {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tz-index: 1000;\r\n\t\tbackground: #fff;\r\n\t}\r\n\r\n\t/*沉浸状态栏变化*/\r\n\t.status-bar {\r\n\t\tbox-sizing: border-box;\r\n\t\tdisplay: block;\r\n\t\twidth: 100%;\r\n\t\tmargin-bottom: -3upx;\r\n\t\theight: var(--status-bar-height);\r\n\t\tline-height: var(--status-bar-height);\r\n\t\tbackground: transparent;\r\n\t}\r\n\r\n\t/*导航栏默认*/\r\n\t.cmd-nav-bar {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\theight: 92upx;\r\n\t\tline-height: 92upx;\r\n\t\tcolor: #000;\r\n\t\tbackground: transparent;\r\n\t\tbox-shadow: 0 6upx 6upx -3upx rgba(0, 0, 0, .2);\r\n\t}\r\n\r\n\t/*所有都垂直占比*/\r\n\t.cmd-nav-bar-left,\r\n\t.cmd-nav-bar-title,\r\n\t.cmd-nav-bar-right {\r\n\t\tflex: 1;\r\n\t\theight: 100%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t/*左侧*/\r\n\t.cmd-nav-bar-left {\r\n\t\tjustify-content: flex-start;\r\n\t\tfont-size: 32upx;\r\n\t\tpadding-left: 30upx;\r\n\t}\r\n\r\n\t.cmd-nav-bar-left-icon {\r\n\t\tmargin-right: 10upx;\r\n\t\tdisplay: inherit;\r\n\t}\r\n\r\n\t.cmd-nav-bar-left-title {\r\n\t\tfont-size: 48upx;\r\n\t\tfont-weight: 500;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t}\r\n\r\n\r\n\t/*标题部分 */\r\n\t.cmd-nav-bar-title {\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 36upx;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t}\r\n\r\n\t/*右侧*/\r\n\t.cmd-nav-bar-right {\r\n\t\tjustify-content: flex-end;\r\n\t\tfont-size: 32upx;\r\n\t\tmargin-right: 30upx;\r\n\t}\r\n\r\n\t.cmd-nav-bar-right-icon {\r\n\t\tmargin-left: 20upx;\r\n\t\tdisplay: inherit;\r\n\t}\r\n\r\n\t.cmd-nav-bar-right-text {\r\n\t\tmargin-left: 20upx;\r\n\t\tfont-weight: 500;\r\n\t}\r\n</style>\n", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cmd-nav-bar.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cmd-nav-bar.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749718152616\n      var cssReload = require(\"E:/software/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}