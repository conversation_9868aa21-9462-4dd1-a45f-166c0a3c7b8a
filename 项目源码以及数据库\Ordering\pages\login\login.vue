<template>
	<view>
		<view class="content">
			<image class="logo" src="https://wx.qlogo.cn/mmhead/Q3auHgzwzM6NSf6SwtNEUQuIJWxy3icApa5Pz4TEg6WDKSXeHowECLg/0"></image>
			<view class="text-area">
				<text class="title">{{title}}</text>
			</view>

		</view>
		<!-- open-type="getUserInfo" @getuserinfo="getUserInfo"  手机 open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber"  -->
		<button type="primary" open-type="getUserInfo" @getuserinfo="getUserInfo" withCredentials="true" style="width: 90%; margin: 50px auto;">微信登录</button>
		<!-- <button  type="primary" open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">获取手机号</button> -->
	</view>
</template>

<script>
	let _sef,openid;
	export default {
		data(){
			return{
				title : '小白兔order',
				//加载时获取总配置的请求路径
				http:''
			}
		},
		onLoad() {
			_sef = this;
			//加载时获取总配置的请求路径
			_sef.http = getApp().globalData.http
		},
		methods:{
			getUserInfo:function(res1){
				console.log('getUserInfo called:', res1);
				if(!res1.detail.iv){
					uni.showModal({
						title : '您拒绝了授权，将不能正常下单！',
						showCancel : false,
					});
					return false;
				}else{
					let userImg,userName;
					let userInfo = res1.detail.userInfo;
					userImg = userInfo.avatarUrl;
					userName = userInfo.nickName;
					console.log('用户信息:', userImg, userName);

					uni.login({
					  provider: 'weixin',
					  success: function (loginRes) {
						console.log('微信登录成功:', loginRes);
						let co = loginRes.code
						console.log('请求URL:', _sef.http+'/zkq/code');
						uni.request({
							// "https://api.weixin.qq.com/sns/jscode2session?appid=wx79dddb1b641eb9de&secret=7b2469c174fc09af95d97b0a7eddbc63&js_code="+co
							//请求用户的微信的唯一标识,openid
							url: _sef.http+'/zkq/code',
							data: {
								co : co
							},
							success:res2=>{
								console.log('获取openid成功:', res2);
								if(res2.data && res2.data.openid) {
									openid = res2.data.openid;
									console.log('openid:', openid);
									uni.request({
									    url: _sef.http+'/zkq/login', //仅为示例，并非真实接口地址。
									    data: {
									        userImg : userImg,
											userName : userName,
											openid : openid
									    },
									    success: (res3) => {
											console.log('登录接口返回:', res3);
											//给总配置设置信息
									        getApp().globalData.u_id = res3.data;
											getApp().globalData.userImg = userImg;
											getApp().globalData.userName = userName;
											getApp().globalData.openid = openid;
											if(res3.data>0){
												console.log('登录成功，跳转首页');
												uni.switchTab({
												    url: '/pages/index/index'
												});
											} else {
												console.log('登录失败，用户ID为0');
												uni.showToast({
													title: '登录失败',
													icon: 'none'
												});
											}
									    },
										fail: (err) => {
											console.error('登录接口请求失败:', err);
											uni.showToast({
												title: '登录请求失败',
												icon: 'none'
											});
										}
									});
								} else {
									console.error('获取openid失败:', res2);
									uni.showToast({
										title: '获取用户信息失败',
										icon: 'none'
									});
								}
							},
							fail: (err) => {
								console.error('获取openid请求失败:', err);
								uni.showToast({
									title: '网络请求失败',
									icon: 'none'
								});
							}
						})
					  },
					  fail: function(loginErr) {
						console.error('微信登录失败:', loginErr);
						uni.showToast({
							title: '微信登录失败',
							icon: 'none'
						});
					  }
					});
				}

			}
		}
	}
</script>

<style>
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.logo {
		height: 200rpx;
		width: 200rpx;
		margin-top: 200rpx;
		margin-left: auto;
		margin-right: auto;
		margin-bottom: 50rpx;
	}

	.text-area {
		display: flex;
		justify-content: center;
	}

	.title {
		font-size: 36rpx;
		color: #8f8f94;
	}
</style>
