<view class="{{['uni-list-item',disabled?'uni-list-item--disabled':'']}}" hover-class="{{disabled||showSwitch?'':'uni-list-item--hover'}}" data-event-opts="{{[['tap',[['onClick',['$event']]]]]}}" bindtap="__e"><view class="uni-list-item__container"><block wx:if="{{thumb}}"><view class="uni-list-item__icon"><image class="uni-list-item__icon-img" src="{{thumb}}"></image></view></block><block wx:else><block wx:if="{{showExtraIcon}}"><view class="uni-list-item__icon"><uni-icons class="uni-icon-wrapper" vue-id="01e39c76-1" color="{{extraIcon.color}}" size="{{extraIcon.size}}" type="{{extraIcon.type}}" bind:__l="__l"></uni-icons></view></block></block><view class="uni-list-item__content"><view class="uni-list-item__content-title">{{title}}</view><view class="uni-list-item__content-id">{{id}}</view><block wx:if="{{note}}"><view class="uni-list-item__content-note">{{note}}</view></block></view><block wx:if="{{showBadge||showArrow||showSwitch}}"><view class="uni-list-item__extra"><block wx:if="{{showBadge}}"><uni-badge vue-id="01e39c76-2" type="{{badgeType}}" text="{{badgeText}}" bind:__l="__l"></uni-badge></block><block wx:if="{{showSwitch}}"><switch disabled="{{disabled}}" checked="{{switchChecked}}" data-event-opts="{{[['change',[['onSwitchChange',['$event']]]]]}}" bindchange="__e"></switch></block><block wx:if="{{showArrow}}"><uni-icons class="uni-icon-wrapper" vue-id="01e39c76-3" size="{{20}}" color="#bbb" type="arrowright" bind:__l="__l"></uni-icons></block></view></block></view></view>