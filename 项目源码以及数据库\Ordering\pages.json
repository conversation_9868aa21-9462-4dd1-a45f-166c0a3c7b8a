{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/login/login",
			"style": {
				"navigationBarTitleText": "login"
			}
		 },
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "首页"
			}
		 },{
			"path": "pages/mine/mine",
			"style": {
				"navigationBarTitleText": "个人中心"
			}
		},{
			"path": "pages/test/test",
			"style": {
				"navigationBarTitleText": "test"
			}
		 },{
			"path": "pages/pay/pay",
			"style": {
				"navigationBarTitleText": "支付"
			}
		 },{
			"path": "pages/user/info/info",
			"style": {
				"navigationBarTitleText": "个人详情"
			}
		 },{
			"path": "pages/order/order",
			"style": {
				"navigationBarTitleText": "我的订单"
			}
		 },{
			"path": "pages/order/scroll/scroll",
			"style": {
				"navigationBarTitleText": "订单详情"
			}
		 },{
			"path": "pages/order/scroll/publish/publish",
			"style": {
				"navigationBarTitleText": "发表看法"
			}
		 }
		 
    ],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	},
	"tabBar": {
	    "color": "#7A7E83",
	    "selectedColor": "#FF0000",
	    "borderStyle": "black",
	    "backgroundColor": "#ffffff",
	    "list": [{
	        "pagePath": "pages/index/index",
	        "iconPath": "static/shouye.png",
	        "selectedIconPath": "static/shouyex.png",
	        "text": "点餐"
	    }, {
	        "pagePath": "pages/mine/mine",
	        "iconPath": "static/gerenzhongxinhui.png",
	        "selectedIconPath": "static/gerenzhongxinlv.png",
	        "text": "我的"
	    }
		// ,{
	 //        "pagePath": "pages/test/test",
	 //        "iconPath": "static/logo.png",
	 //        "selectedIconPath": "static/logo.png",
	 //        "text": "测试"
	 //    },
		]
	}
}
