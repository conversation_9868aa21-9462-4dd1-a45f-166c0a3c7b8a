server.servlet.context-path=/restaurant

server.port=80

spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=******************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=3132161409
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource

spring.resources.static-locations=classpath:/resources/,classpath:/static/,classpath:/public/,classpath:/templates/

mybatis.type-aliases-package=com.aaa.entity

# mybatis扫描mapper文件的配置
mybatis.mapper-locations=classpath:com/aaa/mapper/*.xml

pagehelper.helperDialect = mysql
pagehelper.reasonable = true
pagehelper.supportMethodsArguments = true
pagehelper.pageSizeZero = true
pagehelper.params = countSql

logging.level.com.aaa.mapper=debug

spring.thymeleaf.cache=false
#spring.thymeleaf.suffix=.html

spring.main.banner-mode=off

# 上传文件总的最大值
spring.servlet.multipart.max-request-size=10MB
# 单个文件的最大值
spring.servlet.multipart.max-file-size=10MB
