
#but_type{
	position: fixed;
	width: 40px;
	z-index: 999;
	color: gray;
	opacity:0.5;
}
#v_type{
}
.swiper-item {
	display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	    -ms-flex-pack: center;
	        justify-content: center;
	-webkit-box-align: center;
	-webkit-align-items: center;
	    -ms-flex-align: center;
	        align-items: center;
	height: 100%;
	background: #eee;
	color: #fff;
}
.swiper-item image {
	width: 100%;
	height: 100%;
}
/* 菜单 */
.menus{
	width: 100%;
	height: 100px;
	overflow: hidden;
	
	/* border: #000000 1px solid; */
}
.menus>view{
	float: left;
}
.butCount{
	width: 10xp;
	height: 10xp;
}

