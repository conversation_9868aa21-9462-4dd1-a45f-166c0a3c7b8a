<view><view id="v_type"><button type="default" id="but_type" data-event-opts="{{[['tap',[['show',['left']]]]]}}" bindtap="__e">></button><uni-drawer vue-id="1" visible="{{showLeft}}" mode="left" data-event-opts="{{[['^close',[['closeDrawer',['left']]]]]}}" bind:close="__e" bind:__l="__l" vue-slots="{{['default']}}"><uni-list vue-id="{{('2')+','+('1')}}" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{types}}" wx:for-item="time" wx:for-index="index"><uni-list-item vue-id="{{('3-'+index)+','+('2')}}" title="{{time.name}}" data-event-opts="{{[['^click',[['chaxun',['$0'],[[['types','',index]]]]]]]}}" bind:click="__e" bind:__l="__l"></uni-list-item></block></uni-list><view class="close"><button type="default" data-event-opts="{{[['tap',[['hide',['$event']]]]]}}" bindtap="__e">关闭</button></view></uni-drawer></view><view class="content"><uni-swiper-dot vue-id="4" info="{{info}}" current="{{current}}" mode="{{mode}}" field="content" cash="cash" bind:__l="__l" vue-slots="{{['default']}}"><swiper class="swiper-box" autoplay="true" interval="3000" duration="1000" circular="true" data-event-opts="{{[['change',[['change',['$event']]]]]}}" bindchange="__e"><block wx:for="{{info}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item><view class="{{['swiper-item',item.colorClass]}}"><image src="{{item.url}}" mode="aspectFill"></image></view></swiper-item></block></swiper></uni-swiper-dot></view><view style="width:100%;margin-top:10px;"><block wx:for="{{menu}}" wx:for-item="time" wx:for-index="index" wx:key="index"><view class="menus"><view style="width:115px;"><image style="width:100px;margin:20px 5px 0px 10px;" src="{{time.img}}" mode="widthFix"></image></view><view style="margin:20px 0px 0px 20px;width:110px;">{{''+time.name}}<view class="_br"></view><text style="color:red;">{{"¥"+time.cash}}</text></view><view style="width:120px;margin-top:30px;"><uni-number-box vue-id="{{'5-'+index}}" value="{{time.count}}" data-event-opts="{{[['^click',[['aaa',['$0','$1'],[[['menu','',index]],'val']]]]]}}" bind:click="__e" bind:__l="__l"></uni-number-box></view></view></block></view></view>