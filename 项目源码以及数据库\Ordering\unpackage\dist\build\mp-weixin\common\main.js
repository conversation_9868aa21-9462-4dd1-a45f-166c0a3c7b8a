(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["common/main"],{1656:function(n,e,t){},"640e":function(n,e,t){"use strict";(function(n){t("07a9"),t("921b");var e=u(t("66fd")),o=u(t("fe85"));function u(n){return n&&n.__esModule?n:{default:n}}function r(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{},o=Object.keys(t);"function"===typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(t).filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),o.forEach(function(e){a(n,e,t[e])})}return n}function a(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}e.default.config.productionTip=!1,o.default.mpType="app";var c=new e.default(r({},o.default));n(c).$mount()}).call(this,t("543d")["createApp"])},6967:function(n,e,t){"use strict";t.r(e);var o=t("925b"),u=t.n(o);for(var r in o)"default"!==r&&function(n){t.d(e,n,function(){return o[n]})}(r);e["default"]=u.a},"7f8d":function(n,e,t){"use strict";var o=t("1656"),u=t.n(o);u.a},"925b":function(n,e,t){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o={globalData:{userImg:"",userName:"",openid:"",u_id:0,http:"http://localhost:8080/restaurant/"},onLaunch:function(){console.log("App Launch")},onShow:function(){console.log("App Show")},onHide:function(){console.log("App Hide")}};e.default=o},fe85:function(n,e,t){"use strict";t.r(e);var o=t("6967");for(var u in o)"default"!==u&&function(n){t.d(e,n,function(){return o[n]})}(u);t("7f8d");var r,a,c=t("2877"),l=Object(c["a"])(o["default"],r,a,!1,null,null,null);e["default"]=l.exports}},[["640e","common/runtime","common/vendor"]]]);