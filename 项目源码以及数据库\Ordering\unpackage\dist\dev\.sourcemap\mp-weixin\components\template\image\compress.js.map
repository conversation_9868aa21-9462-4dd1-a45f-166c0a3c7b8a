{"version": 3, "sources": ["webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/template/image/compress.vue?8c57", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/template/image/compress.vue?6804", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/template/image/compress.vue?f3bf", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/template/image/compress.vue?5a49", "uni-app:///components/template/image/compress.vue"], "names": ["name", "props", "quality", "type", "default", "maxwh", "changes", "data", "imageW", "imageH", "redio", "methods", "<PERSON><PERSON>o", "uni", "src", "success", "width", "height", "ctx", "fileType", "x", "y", "destWidth", "destHeight", "canvasId", "res2", "resolve", "yasuoImg", "val", "imgs2"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;;;AAGvD;AAC+K;AAC/K,gBAAgB,wLAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAopB,CAAgB,yqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACSxqB;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,eACA;EACAA;EACAC;IACAC;MACA;MACAC;MACAC;IACA;IACAC;MACA;MACAF;MACAC;IACA;IACAE;MACA;MACAH;MACAC;IACA;EACA;EAEAG;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACAC;UACAC;UACAC;YACA;YACA;YACA;YACA;YACA;YACA;cACA;gBACAC;gBACAC;cACA;gBACAA;gBACAD;cACA;YACA;YAEA;YACA;YACA;YACA;YACAE;YACA;YACAA;cACA;cACA;cACA;cACA;gBACAC;cACA;gBACAA;cACA;cACA;cACAN;gBACAO;gBACAC;gBACAL;gBACAC;gBACAK;gBACAC;gBACArB;gBACAsB;gBACAL;gBACAJ;kBACA;kBACAU;kBACAA;kBACAA;kBACAA;kBACAA;kBACAA;kBACAC;gBAGA;cACA;YACA;UAEA;QACA;MACA;IACA;IACAC;MAAA;MAEA;MACA;MACA;QACA;QACA;UAEA;YACA;cACAC;cACA;cACA;gBACA;kBACAC;gBACA;cACA;cACA;gBACAH;cACA;YAEA;UAEA;YACAA;YACA;UACA;QACA;UAEA;YACAA;YACA;UACA;QACA;MACA;IACA;EACA;AAEA;AAAA,2B", "file": "components/template/image/compress.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./compress.vue?vue&type=template&id=9c9eb640&\"\nvar renderjs\nimport script from \"./compress.vue?vue&type=script&lang=js&\"\nexport * from \"./compress.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/template/image/compress.vue\"\nexport default component.exports", "export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./compress.vue?vue&type=template&id=9c9eb640&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./compress.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./compress.vue?vue&type=script&lang=js&\"", "<template>\r\n    <view>\r\n        <view style=\"position:fixed;top:9999px;\">\r\n            <canvas :style=\"'width:'+imageW+'px;height:'+imageH+'px;'\" canvas-id=\"myCanvas\"></canvas>\r\n        </view>\r\n    </view>\r\n</template>\r\n\r\n<script>\r\n    // 图片批量压缩使用说明  返回值h5=base64 字符串，微信小程序/h5+ 返回=图片地址链接\r\n    // 父页面<compress ref=\"compress\" :maxwh=\"maxwh=720\" :quality=\"quality=0.8\" @changes=\"callback =function(e){}\"> </compress>\r\n    // 两种使用方式 \r\n    // 1监听changes函数获得处理完成返回执\r\n    // 2 使用 this.$refs.compress.yasuoImg(['bold:eqweew.jpg','bold:'fdsfdsf,jpg']).then(e=>{\r\n    //                        console.log(e) //返回值\r\n    //                    })\r\n    export default {\r\n        name: 'image-compress',\r\n        props: {\r\n            quality: {\r\n                //质量\r\n                type: Number,\r\n                default: 1\r\n            },\r\n            maxwh: {\r\n                //最大宽高尺寸像素\r\n                type: Number,\r\n                default: 1920\r\n            },\r\n            changes: {\r\n                //监听变化\r\n                type: Function,\r\n                default: null\r\n            }\r\n        },\r\n\r\n        data() {\r\n            return {\r\n                imageW: this.maxwh,\r\n                imageH: this.maxwh,\r\n                redio: 1,\r\n            }\r\n        },\r\n        methods: {\r\n            yasuo(img) {\r\n                return new Promise((resolve, reject) => {\r\n                    uni.getImageInfo({\r\n                        src: img,\r\n                        success: (res) => {\r\n                            var ratio = parseFloat(res.width / res.height); //获得图片实际的宽高比\r\n                            var width = res.width;\r\n                            var height = res.height;\r\n                            // console.log(this.maxwh)\r\n                            var maxwh = this.maxwh;\r\n                            if (width > maxwh || height > maxwh) {\r\n                                if (width > height) {\r\n                                    width = maxwh;\r\n                                    height = parseInt(width / ratio);\r\n                                } else {\r\n                                    height = maxwh;\r\n                                    width = parseInt(height * ratio);\r\n                                }\r\n                            }\r\n\r\n                            this.imageW = width;\r\n                            this.imageH = height;\r\n                            // 将图片写入画布\r\n                            var ctx = uni.createCanvasContext('myCanvas', this);\r\n                            ctx.drawImage(res.path, 0, 0, width, height);\r\n                            // console.log(ctx)\r\n                            ctx.draw(true, (e) => {\r\n                                // console.log(e)\r\n                                // 获取画布要裁剪的位置和宽度   均为百分比 * 画布中图片的宽度  \r\n                                var fileType = res.path.replace(/^.+\\./, '');\r\n                                if (fileType == 'png') {\r\n                                    fileType = 'png';\r\n                                } else {\r\n                                    fileType = 'jpg';\r\n                                }\r\n                                // console.log(fileType)\r\n                                uni.canvasToTempFilePath({\r\n                                    x: 0,\r\n                                    y: 0,\r\n                                    width: width,\r\n                                    height: height,\r\n                                    destWidth: width,\r\n                                    destHeight: height,\r\n                                    quality: this.quality,\r\n                                    canvasId: 'myCanvas',\r\n                                    fileType: fileType,\r\n                                    success: (res2) => {\r\n                                        // console.log(res)\r\n                                        res2.oldWidth = res.width;\r\n                                        res2.oldHeight = res.height;\r\n                                        res2.width = width;\r\n                                        res2.height = height;\r\n                                        res2.path = res.path;\r\n                                        res2.fileType = fileType;\r\n                                        resolve(res2)\r\n\r\n\r\n                                    }\r\n                                }, this);\r\n                            });\r\n\r\n                        }\r\n                    })\r\n                })\r\n            },\r\n            yasuoImg(imgs, val) {\r\n\r\n                var val = val || [];\r\n                var imgs = imgs;\r\n                return new Promise((resolve, reject) => {\r\n                    var resolves = resolves || resolve;\r\n                    if (typeof imgs == 'object') {\r\n\r\n                        if (imgs.length) {\r\n                            this.yasuo(imgs[0]).then(e => {\r\n                                val.push(e)\r\n                                var imgs2 = [];\r\n                                for (var i = 0; i < imgs.length; i++) {\r\n                                    if (i != 0) {\r\n                                        imgs2.push(imgs[i])\r\n                                    }\r\n                                }\r\n                                this.yasuoImg(imgs2, val).then(e => {\r\n                                    resolve(e)\r\n                                })\r\n\r\n                            })\r\n\r\n                        } else {\r\n                            resolve(val)\r\n                            this.$emit(\"changes\", val);\r\n                        }\r\n                    } else {\r\n\r\n                        this.yasuoImg(imgs).then(e => {\r\n                            resolve(val)\r\n                            this.$emit(\"changes\", val);\r\n                        })\r\n                    }\r\n                })\r\n            }\r\n        }\r\n\r\n    }\r\n</script>\r\n\r\n<style>\r\n</style>\n"], "sourceRoot": ""}