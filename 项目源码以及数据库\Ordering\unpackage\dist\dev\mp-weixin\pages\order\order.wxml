<view style="flex-wrap:wrap;"><block wx:for="{{orders}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toDetails',['$0'],[[['orders','',index,'o_id']]]]]]]}}" class="orders-list" bindtap="__e"><view class="uni-media-list-logo"><block wx:if="{{showImg}}"><image style="width:60px;" src="{{http+upload+item.mimg}}" mode="widthFix"></image></block></view><view class="uni-media-list-body"><view class="uni-media-list-text-top">{{"下单时间:"+item.o_time}}</view><view class="uni-media-list-text-bottom uni-ellipsis">{{item.o_deskNum+"号桌"}}<label class="sumprice _span">{{"总价:"+item.o_totalprice+"￥"}}</label></view></view></view></block></view>