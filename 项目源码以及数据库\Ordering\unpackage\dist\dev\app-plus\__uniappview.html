<!DOCTYPE html>
<html lang="zh-CN">

    <head>
        <meta charset="UTF-8" />
        <script>
            var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
                CSS.supports('top: constant(a)'))
            document.write(
                '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
                (coverSupport ? ', viewport-fit=cover' : '') + '" />')
        </script>
        <title>View</title>
        <style>
            * {
                -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
                -webkit-tap-highlight-color: transparent;
            }

            input[type="search"i]::-webkit-search-cancel-button {
                display: none;
            }
        </style>
        <script>
            var __wxConfig = {"debug":false,"appname":"Ordering","entryPagePath":"pages/index/index.html","page":{"pages/index/index.html":{"window":{"navigationBarTitleText":"首页","usingComponents":{"uni-drawer":"/components/uni-drawer/uni-drawer","uni-list-item":"/components/uni-list-item/uni-list-item","uni-swiper-dot":"/components/uni-swiper-dot/uni-swiper-dot","uni-number-box":"/components/uni-number-box/uni-number-box"}}}},"global":{"window":{"navigationBarTextStyle":"black","navigationBarTitleText":"uni-app","navigationBarBackgroundColor":"#F8F8F8","backgroundColor":"#F8F8F8"}},"tabBar":{"color":"","selectedColor":"","backgroundColor":"","borderStyle":"black","list":[],"position":"bottom"},"networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000},"platform":"devtools","pages":["pages/index/index"],"nvueCompiler":"uni-app","renderer":"auto","splashscreen":{"alwaysShowBeforeRender":true,"autoclose":false},"compilerVersion":"2.3.3","usingComponents":{}}
        </script>
        <script>
            var __id__ = '';
            window.__dispatchGenerateFunc__ = function(page) {
                __id__ = page;
                document.title = page;
                // window.history.pushState('', '', page);
                var wxss = page.replace('.html', '.wxss')
                if (
                    typeof __wxAppCode__ !== 'undefined' &&
                    __wxAppCode__.hasOwnProperty(wxss)
                ) {
                    var setCssToHead = __wxAppCode__[wxss]
                    typeof setCssToHead === 'function' && setCssToHead()
                    document.dispatchEvent(new CustomEvent("generateFuncReady", {
                        detail: {
                            generateFunc: $gwx(page.replace('.html', '.wxml'))
                        }
                    }));
                } else {
                    plus.webview.currentWebview().setJsFile('_www/' + page.replace('.html', '.js'));
                }
            };
        </script>
        <script src="__uniappes6.js"></script>
        <script src="uniapp://ready"></script>
    </head>

    <body>
        <div></div>
    </body>

</html>
