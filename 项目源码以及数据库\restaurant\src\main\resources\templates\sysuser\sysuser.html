<!DOCTYPE html>
<html lang="en"  xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>用户查询</title>
    <link rel="stylesheet" th:href="@{/layui/css/layui.css}">
    <script th:src="@{/layui/layui.js}"></script>
    <script th:src="@{/js/jquery-3.1.1.js}"></script>
</head>
<body>
<div>
    <div class="layui-btn-group">
        <button type="button" class="layui-btn" id="addSysUser">增加</button>
    </div>
    <div>
        <table class="layui-hide" id="userAll" lay-filter="demo"></table>
    </div>
    <!--<script type="text/html" id="switchTpl">-->
        <!--<input type="checkbox" name="locked" value="" lay-skin="switch" lay-text="启用|禁用" lay-filter="enableDemo" checked id="enable">-->
    <!--</script>-->
</div>
<script type="text/html" id="barDemo">
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
</script>
<script th:inline="none">
    layui.use(['table','layer','form'], function(){
        var table = layui.table;
        var layer=layui.layer;
        var form=layui.form;
        table.render({
            elem: '#userAll'
            ,url:'queryData'
            ,cellMinWidth: 80
            ,cols: [[
                {field:'id', width:'10%', title: 'ID', sort: true}
                ,{field:'username',width:'10%', title: '用户名'}
                ,{field:'usercode', width:'10%', title: '账号'}
                ,{field:'password', title: '密码',hide:true}
                ,{field:'locked',width:'10%', title: '用户状态' ,
                   templet:function(data){
                       var state = "";
                        if(data.locked=="0"){
                            state = "<input type='checkbox' value='" + data.id + "' id='locked' lay-filter='stat' checked='checked' name='locked'  lay-skin='switch' lay-text='启用|禁用' >";

                        }else if(data.locked=="1"){
                            state = "<input type='checkbox' value='" + data.id + "' id='locked' lay-filter='stat'  name='locked'  lay-skin='switch' lay-text='启用|禁用' >";

                        }
                       return state;
                   }
                    }
                ,{field:'sysRoles.name',width:'30%',title:'角色',templet:'<div>{{d.sysRoles[0].name}}</div>'}
                , {field:'action',width:'20%',title:'操作',toolbar: '#barDemo'}
            ]]
            ,page: true
            ,limit:2
            ,limits:[2,4,8]
        });
        $('#addSysUser').on('click',function () {
            layer.open({
                title: '管理员添加',
                type: 2,
                content: 'addSysUser',    // 设置跳转的url，跳转到对应的页面
                area: ['400px','400px'],
                //btn: ['添加','取消'],
                yes: function (index,layero) {
                    // // 获取弹出层中的form表单元素
                    // var formSubmit=layer.getChildFrame('form', index);
                    // // 提交form表单（不会触发表单验证）
                    // formSubmit.submit();

                    // 获取弹出层中的form表单元素
                    var formSubmit=layer.getChildFrame('form', index);
                    // 获取表单中的提交按钮（在我的表单里第一个button按钮就是提交按钮，使用find方法寻找即可）
                    // var submited = formSubmit.find('button')[0];
                    // 触发点击事件，会对表单进行验证，验证成功则提交表单，失败则返回错误信息
                    submited.click();

                    // 弹出层关闭的操作在子层的js代码中完成

                }
            });
        });
        table.on('tool(demo)', function(obj){
            var data = obj.data;
            if(obj.event === 'edit'){
                //layer.alert('编辑行：<br>'+ JSON.stringify(data))
                //layer.alert(data.id);
                var id=data.id;
                layer.open({
                    title: '管理员编辑',
                    type: 2,
                    content: 'editSysUser?id='+id,    // 设置跳转的url，跳转到对应的页面
                    area: ['400px','400px'],
                    yes: function (index,layero) {
                        var formSubmit=layer.getChildFrame('form', index);
                        submited.click();
                    }
                });
            }
        });

        //监听开关事件
        form.on('switch(stat)', function (data) {
           var contexts;
           var sta;
           var id= data.elem.value;
            if ( data.elem.checked== true) {
                contexts = "启用";
               sta = 0;
                $.ajax({
                    type: "post",
                    async: false,
                    data: "id=" + id + "&locked=" + 0,
                    url: "changeState",
                    success: function(data) {
                        flag = data
                    },
                });

            } else if(data.elem.value!='su-000001'){
               contexts = "禁用";
                sta = 1;
                $.ajax({
                    type: "post",
                    async: false,
                    data: "id=" + id + "&locked=" + 1,
                    url: "changeState",
                    success: function(data) {
                        flag = data
                    },
                });

            }else if(data.elem.value=='su-000001'){
                table.reload('userAll');
            }

        })
    });
</script>
</body>
</html>