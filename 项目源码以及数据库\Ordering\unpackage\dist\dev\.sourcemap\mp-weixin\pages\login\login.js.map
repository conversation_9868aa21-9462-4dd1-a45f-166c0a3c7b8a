{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/login/login.vue?f76e", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/login/login.vue?0add", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/login/login.vue?5e25", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/login/login.vue?4bcf", "uni-app:///pages/login/login.vue", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/login/login.vue?ddee", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/login/login.vue?a859"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "title", "http", "onLoad", "_sef", "console", "uni", "url", "methods", "testLogin", "icon", "duration", "setTimeout", "getUserInfo", "showCancel", "userImg", "userName", "provider", "success", "co", "openid", "getApp", "fail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AAC+K;AAC/K,gBAAgB,wLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAipB,CAAgB,sqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiBrqB;AAAA,eACA;EACAC;IACA;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACAC;IACA;IACAA;IACAC;;IAEA;IACA;IACAA;IACA;MACAA;MACAC;QACAC;MACA;IACA;EACA;EACAC;IACAC;MACAJ;MACAC;QACAL;QACAS;QACAC;MACA;MACA;MACAC;QACAN;UACAC;QACA;MACA;IACA;IACAM;MACAR;MACA;QACAC;UACAL;UACAa;QACA;QACA;MACA;QACA;QACA;QACAC;QACAC;QACAX;QAEAC;UACAW;UACAC;YACAb;YACA;YACAA;YACAC;cACA;cACA;cACAC;cACAP;gBACAmB;cACA;cACAD;gBACAb;gBACAA;gBACA;kBACAe;kBACAf;kBACAC;oBACAC;oBAAA;oBACAP;sBACAe;sBACAC;sBACAI;oBACA;oBACAF;sBACAb;sBACA;sBACAgB;sBACAA;sBACAA;sBACAA;sBACA;wBACAhB;wBACAC;0BACAC;wBACA;sBACA;wBACAF;wBACAC;0BACAL;0BACAS;wBACA;sBACA;oBACA;oBACAY;sBACAjB;sBACAC;wBACAL;wBACAS;sBACA;oBACA;kBACA;gBACA;kBACAL;kBACAC;oBACAL;oBACAS;kBACA;gBACA;cACA;cACAY;gBACAjB;gBACAC;kBACAL;kBACAS;gBACA;cACA;YACA;UACA;UACAY;YACAjB;YACAC;cACAL;cACAS;YACA;UACA;QACA;MACA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3JA;AAAA;AAAA;AAAA;AAAu7B,CAAgB,i7BAAG,EAAC,C;;;;;;;;;;;ACA38B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login/login.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/login/login.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login.vue?vue&type=template&id=b237504c&\"\nvar renderjs\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/login.vue\"\nexport default component.exports", "export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=template&id=b237504c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"content\">\r\n\t\t\t<image class=\"logo\" src=\"https://wx.qlogo.cn/mmhead/Q3auHgzwzM6NSf6SwtNEUQuIJWxy3icApa5Pz4TEg6WDKSXeHowECLg/0\"></image>\r\n\t\t\t<view class=\"text-area\">\r\n\t\t\t\t<text class=\"title\">{{title}}</text>\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\t\t<!-- open-type=\"getUserInfo\" @getuserinfo=\"getUserInfo\"  手机 open-type=\"getPhoneNumber\" bindgetphonenumber=\"getPhoneNumber\"  -->\r\n\t\t<button type=\"primary\" open-type=\"getUserInfo\" @getuserinfo=\"getUserInfo\" withCredentials=\"true\" style=\"width: 90%; margin: 50px auto;\">微信登录</button>\r\n\t\t<button type=\"default\" @click=\"testLogin\" style=\"width: 90%; margin: 10px auto;\">测试登录</button>\r\n\t\t<!-- <button  type=\"primary\" open-type=\"getPhoneNumber\" bindgetphonenumber=\"getPhoneNumber\">获取手机号</button> -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tlet _sef,openid;\r\n\texport default {\r\n\t\tdata(){\r\n\t\t\treturn{\r\n\t\t\t\ttitle : '小白兔order',\r\n\t\t\t\t//加载时获取总配置的请求路径\r\n\t\t\t\thttp:''\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t_sef = this;\r\n\t\t\t//加载时获取总配置的请求路径\r\n\t\t\t_sef.http = getApp().globalData.http\r\n\t\t\tconsole.log('登录页面加载，请求地址:', _sef.http);\r\n\r\n\t\t\t// 检查是否已经登录\r\n\t\t\tlet u_id = getApp().globalData.u_id;\r\n\t\t\tconsole.log('当前用户ID:', u_id);\r\n\t\t\tif(u_id && u_id > 0) {\r\n\t\t\t\tconsole.log('用户已登录，跳转首页');\r\n\t\t\t\tuni.switchTab({\r\n\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\ttestLogin() {\r\n\t\t\t\tconsole.log(\"测试登录按钮被点击\");\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '测试登录成功',\r\n\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\t\t\t\t// 延迟跳转，让用户看到提示\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\turl: '../index/index'\r\n\t\t\t\t\t});\r\n\t\t\t\t}, 1000);\r\n\t\t\t},\r\n\t\t\tgetUserInfo:function(res1){\r\n\t\t\t\tconsole.log('getUserInfo called:', res1);\r\n\t\t\t\tif(!res1.detail.iv){\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle : '您拒绝了授权，将不能正常下单！',\r\n\t\t\t\t\t\tshowCancel : false,\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tlet userImg,userName;\r\n\t\t\t\t\tlet userInfo = res1.detail.userInfo;\r\n\t\t\t\t\tuserImg = userInfo.avatarUrl;\r\n\t\t\t\t\tuserName = userInfo.nickName;\r\n\t\t\t\t\tconsole.log('用户信息:', userImg, userName);\r\n\r\n\t\t\t\t\tuni.login({\r\n\t\t\t\t\t  provider: 'weixin',\r\n\t\t\t\t\t  success: function (loginRes) {\r\n\t\t\t\t\t\tconsole.log('微信登录成功:', loginRes);\r\n\t\t\t\t\t\tlet co = loginRes.code\r\n\t\t\t\t\t\tconsole.log('请求URL:', _sef.http+'/zkq/code');\r\n\t\t\t\t\t\tuni.request({\r\n\t\t\t\t\t\t\t// \"https://api.weixin.qq.com/sns/jscode2session?appid=wx79dddb1b641eb9de&secret=7b2469c174fc09af95d97b0a7eddbc63&js_code=\"+co\r\n\t\t\t\t\t\t\t//请求用户的微信的唯一标识,openid\r\n\t\t\t\t\t\t\turl: _sef.http+'/zkq/code',\r\n\t\t\t\t\t\t\tdata: {\r\n\t\t\t\t\t\t\t\tco : co\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tsuccess:res2=>{\r\n\t\t\t\t\t\t\t\tconsole.log('获取openid成功:', res2);\r\n\t\t\t\t\t\t\t\tconsole.log('返回的数据详情:', JSON.stringify(res2.data));\r\n\t\t\t\t\t\t\t\tif(res2.data && res2.data.openid) {\r\n\t\t\t\t\t\t\t\t\topenid = res2.data.openid;\r\n\t\t\t\t\t\t\t\t\tconsole.log('openid:', openid);\r\n\t\t\t\t\t\t\t\t\tuni.request({\r\n\t\t\t\t\t\t\t\t\t    url: _sef.http+'/zkq/login', //仅为示例，并非真实接口地址。\r\n\t\t\t\t\t\t\t\t\t    data: {\r\n\t\t\t\t\t\t\t\t\t        userImg : userImg,\r\n\t\t\t\t\t\t\t\t\t\t\tuserName : userName,\r\n\t\t\t\t\t\t\t\t\t\t\topenid : openid\r\n\t\t\t\t\t\t\t\t\t    },\r\n\t\t\t\t\t\t\t\t\t    success: (res3) => {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log('登录接口返回:', res3);\r\n\t\t\t\t\t\t\t\t\t\t\t//给总配置设置信息\r\n\t\t\t\t\t\t\t\t\t        getApp().globalData.u_id = res3.data;\r\n\t\t\t\t\t\t\t\t\t\t\tgetApp().globalData.userImg = userImg;\r\n\t\t\t\t\t\t\t\t\t\t\tgetApp().globalData.userName = userName;\r\n\t\t\t\t\t\t\t\t\t\t\tgetApp().globalData.openid = openid;\r\n\t\t\t\t\t\t\t\t\t\t\tif(res3.data>0){\r\n\t\t\t\t\t\t\t\t\t\t\t\tconsole.log('登录成功，跳转首页');\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\t\t\t\t    url: '/pages/index/index'\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\tconsole.log('登录失败，用户ID为0');\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: '登录失败',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t    },\r\n\t\t\t\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.error('登录接口请求失败:', err);\r\n\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '登录请求失败',\r\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tconsole.error('获取openid失败:', res2);\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '获取用户信息失败',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\tconsole.error('获取openid请求失败:', err);\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '网络请求失败',\r\n\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t  },\r\n\t\t\t\t\t  fail: function(loginErr) {\r\n\t\t\t\t\t\tconsole.error('微信登录失败:', loginErr);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '微信登录失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t  }\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.content {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.logo {\r\n\t\theight: 200rpx;\r\n\t\twidth: 200rpx;\r\n\t\tmargin-top: 200rpx;\r\n\t\tmargin-left: auto;\r\n\t\tmargin-right: auto;\r\n\t\tmargin-bottom: 50rpx;\r\n\t}\r\n\r\n\t.text-area {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.title {\r\n\t\tfont-size: 36rpx;\r\n\t\tcolor: #8f8f94;\r\n\t}\r\n</style>\r\n", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1748249136550\n      var cssReload = require(\"E:/software/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}