
.flex {
	display: flex;
}
.uni-tab__cart-box {
	width: 100%;
	height: 100rpx;
	background: #fff;
	z-index: 900;
	/* position: absolute fixed ; */
}
.uni-tab__cart-sub-box {
	width: 100%;
	box-sizing: border-box;
}
.uni-tab__right {
	margin: 5px 0;
	margin-right: 10px;
	border-radius: 100px;
	overflow: hidden;
}
.uni-tab__cart-button-left {
	position: relative;
	justify-content: center;
	align-items: center;
	flex-direction: column;
	width: 100%;
	font-size: 24rpx;
}
.uni-tab__icon {
	width: 40rpx;
	height: 40rpx;
}
.uni-tab__icon image {
	width: 100%;
	height: 100%;
}
.uni-tab__cart-button-left .uni-tab__text {
	margin-top: 5rpx;
	font-size: 24rpx;
	color: #666;
}
.uni-tab__cart-button-right {
	justify-content: center;
	align-items: center;
	width: 100%;
	font-size: 24rpx;
	color: #fff;
}
.uni-tab__cart-button-right:active {
	opacity: 0.7;
}
.uni-tab__cart-button-left .uni-tab__dot-box {
	position: absolute;
	right: 40rpx;
	top: 20rpx;
	justify-content: center;
	align-items: center;
	width: 0;
	height: 0;
}
.uni-tab__dot-box .uni-tab__dot {
	flex-shrink: 0;
	width: 30rpx;
	height: 30rpx;
	line-height: 30rpx;
	color: #ffffff;
	text-align: center;
	font-size: 12px;
	background: #ff0000;
	border-radius: 50%;
}
.uni-tab__dot-box .uni-tab__dot.uni-tab__dots {
	padding: 0 8rpx;
	width: auto;
	border-radius: 30rpx;
}
.uni-tab__color-y {
	background: #ffa200;
}
.uni-tab__color-r {
	background: #ff0000;
}

