<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>菜品列表</title>
    <link rel="stylesheet" href="../layui/css/layui.css">
    <script src="../js/jquery-3.1.1.js"></script>
    <script src="../layui/layui.js"></script>
<style>
    td>div{
        height: 100px!important;
        line-height: 100px!important;
        width: auto;
        font-size: 20px!important;
    }
    .layui-table-page,#layui-table-page1{
        height: 75px;
    }
    #demo1{
        left: 100px;
        width: 100px;
        height: 100px;
        margin-left: 109px;
    }
    #popUpdateTest{
        margin: 10px;
    }
    .foodTitle{
        width: 200px;
    }
    .demoTable{
        margin-top: 10px;
    }

</style>
</head>
<body>
        <div class="demoTable">
            请选择类型：
            <div class="layui-inline">
                <div class="layui-form layui-form-pane" style="width: 200px;">
                    <select name="tname" id="tname" lay-verify="required" lay-filter="change"></select>
                </div>
            </div>
            菜品名称:
            <div class="layui-inline">
                <input id="foodTitle" name="foodTitle" class="layui-input foodTitle" autocomplete="off" placeholder="请输入菜品名称" >
            </div>
            <button class="layui-btn layui-btn-radius" data-type="reload">搜索</button>
        </div>

        <table class="layui-hide" id="test" lay-filter="table"></table>
        <script type="text/html" id="barDemo">
            <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
            <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">下架</a>
        </script>
    <script>
        var img = null;
        var m_name = null;
        var tid = 0;
        layui.use(['table','layer','form','upload'], function(){
            var $ = layui.$,
                table = layui.table,
                layer=layui.layer,
                form = layui.form;
            var upload = layui.upload;
            $.ajax({
                url:'type',
                type:"post",
                success:function (obj) {
                    var str = "";
                    var opt = "<option value=\"0\" selected>全部</option>";
                    for(var i in obj){
                        str +="<option value="+obj[i].t_id+">"+obj[i].t_name+"</option>";
                    }
                    $("#tname").html(opt+str);
                    form.render("select");
                }
            });
            //表格数据
            table.render({
                elem: '#test'
                ,id:'toTest'
                ,url:'foodSel?m_state=0'
                ,height: 'full-20'
                ,cols: [[
                    {field:'numbers', width:'5%', title: 'ID', sort: true,type:'numbers'},
                    {img:'m_img', width:'9%',title: '图片',templet:'<div><img src="/restaurant/upload/{{ d.m_img }}" ></div>'},
                    {field:'m_name', width:'18%', title: '菜品名称', sort: true},
                    {field:'m_price', width:'18%', title: '价格', sort: true},
                    {field:'t_name', width:'15%', title: '类型', sort: true},
                    {field:'m_state', width:'15%', title: '状态',
                        templet:function (d) {
                            if (d.m_state==1){
                                return '已下架';
                            }else {
                                return '已上架';
                            }
                        }
                    },
                    {fixed: 'right', width:'20%', align:'center', toolbar: '#barDemo'}
                ]],
                limit:5,
                limits:[5,6,7,8,9],
                page: true
            });
            //表格重载
            window.tableReload = function () {
                table.reload('toTest',{
                    where:{
                        m_name:m_name,
                        t_id:tid
                    },
                    page:{
                        curr:1
                    }
                });
            }
            //监听行工具事件
            table.on('tool(table)', function(obj) { //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
                var data = obj.data //获得当前行数据
                    , layEvent = obj.event; //获得 lay-event 对应的值
                if (layEvent === 'edit') {
                   $('#nm_id').val(data.m_id);
                   $('#nm_name').val(data.m_name);
                   $('#nm_price').val(data.m_price);
                   var name = data.t_name;
                   var m_img = data.m_img;
                   var image = "\\restaurant\\upload\\"+data.m_img;
                    var nm_img = null;
                   $("#demo1").attr("src",image);
                   //异步请求放入类型下拉框
                    $.ajax({
                        url:'type',
                        type:"post",
                        success:function (obj) {
                            var str = "";
                            for(var i in obj){
                                if(name!=(obj[i].t_name)){
                                    str +="<option value="+obj[i].t_id+">"+obj[i].t_name+"</option>";
                                }else {
                                    str +="<option value="+obj[i].t_id+" selected>"+obj[i].t_name+"</option>";
                                }
                            }
                            $("#nt_name").html(str);
                            form.render("select");
                        }
                    });
                   layer.open({
                       type:1,
                       maxmin:true,
                       area:['800px','500px'],
                       title:'菜品信息编辑',
                       content:$('#popUpdateTest'),
                       btn: ['提交修改', '取消'],
                       yes: function(index){
                           if (img==null){
                               nm_img = m_img;
                           }else {
                               nm_img = "img/"+img;
                           }
                           $.ajax({
                               type: 'post',
                               url: 'foodUpd',
                               data: {
                                   nm_id:$('#nm_id').val(),
                                   nm_name:$('#nm_name').val(),
                                   nm_price:$('#nm_price').val(),
                                   nt_id:$('#nt_name').val(),
                                   nm_img:nm_img
                               },
                               success: function(data){
                                   if(data==1){
                                       layer.open({
                                           content: '已成功更新数据！',
                                           yes: function(index, layero){
                                               layer.close(index);
                                               window.location.reload();
                                           }
                                       });
                                   }else{
                                       layer.msg('更新失败，请稍后重试！');
                                   }
                               }
                           });
                       }
                   });
                    var uploadInst = upload.render({
                        elem: '#uploadI',
                        url: '/restaurant/foodMassage/uploading',
                        before: function (obj) {
                            //预读本地文件示例，不支持ie8
                            obj.preview(function (index, file, result) {
                                //获取图片的文件名
                                img = file.name;
                                $('#demo1').attr('src', result); //图片链接（base64）
                            });
                        },
                        //done 上传后的回调函数 res 接口返回的数据
                        done: function (res) {
                            //如果上传失败
                            if (res.code = 0) {
                                return layer.msg('上传失败');
                            }
                            //上传成功
                            else{
                                // return layer.msg('上传成功');
                            }
                        },
                        //请求异常回调
                        error: function () {
                            //演示失败状态，并实现重传
                            var demoText = $('#demoText');
                            demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-xs demo-reload">重试</a>');
                            demoText.find('.demo-reload').on('click', function () {
                                uploadInst.upload();
                            });
                        }
                    });
                } else if (layEvent === 'del') {
                    layer.confirm('确定下架吗?', function (index) {
                        $.ajax({
                            url: "foodState",
                            type: "POST",
                            data: {
                                m_id:data.m_id,
                                m_state:data.m_state
                            },
                            success: function (msg) {
                                if (msg == "1") {
                                    //删除这一行
                                    obj.del();
                                    //关闭弹框
                                    layer.close(index);
                                    layer.msg("删除成功", {icon: 6});
                                } else {
                                    layer.msg("删除失败", {icon: 5});
                                }
                                window.location.reload();
                            }
                        });
                    },function (index) {
                        layer.close(index);
                    });
                    return false;
                }
            });
        //    监听下拉框事件
            form.on('select(change)', function(obj){
                tid = obj.value;
                m_name=$('#foodTitle').val();
                window.tableReload();
            });
        //    模糊查询
            $('.layui-btn').on('click', function(){
                m_name = $('#foodTitle').val();
                window.tableReload();
            });
        });
    </script>
    <div class="layui-row" id="popUpdateTest" style="display:none;">

        <form class="layui-form dataFrm" action="/foodMassage/foodUpd" enctype="multipart/form-data">
            <div style="display:none;">
                <span ID="nm_id" name="nm_id"></span>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">菜品名称</label>
                <div class="layui-input-block" style="width: 200px;">
                    <input type="text" name="nm_name" id="nm_name" lay-filter="m_name" required  lay-verify="required" placeholder="菜品名称" autocomplete="off" class="layui-input m_name">

                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">价格</label>
                <div class="layui-input-inline">
                    <input type="text" name="nm_price" id="nm_price" required lay-verify="required" placeholder="0" autocomplete="off" class="layui-input m_price">
                </div>
                <div class="layui-form-mid layui-word-aux">元</div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">类型</label>
                <div class="layui-input-block" style="width: 200px;">
                    <select name="nt_name" id="nt_name" lay-verify="required" >
                        <option value="0">请选择类型</option>
                    </select>
                </div>
            </div>

            <div>
                <label class="layui-form-label">菜品图片</label>
                <div class="layui-upload">
                    <button class="layui-btn" id="uploadI" type="button">上传图片</button>
                    <div class="layui-upload-list img">
                        <img class="layui-upload-img" id="demo1" name="file">
                        <p id="demoText"></p>
                    </div>
                </div>
            </div>
        </form>
    </div>

</body>
</html>