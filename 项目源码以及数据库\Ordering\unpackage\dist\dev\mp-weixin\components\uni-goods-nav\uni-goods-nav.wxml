<block wx:if="{{showGoods}}"><view class="showGoods"><view class="uni-tab__seat"></view><view class="uni-tab__cart-box flex"><view class="flex uni-tab__cart-sub-box"><block wx:for="{{options}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['onClick',[index,'$0'],[[['options','',index]]]]]]]}}" class="flex uni-tab__cart-button-left uni-tab__shop-cart" bindtap="__e"><view class="uni-tab__icon"><image src="{{item.icon}}" mode="widthFix"></image></view><text class="uni-tab__text">{{item.text}}</text><view class="flex uni-tab__dot-box"><block wx:if="{{item.info}}"><view class="{{['uni-tab__dot','',(item.info>9)?'uni-tab__dots':'']}}">{{''+item.info+''}}</view></block></view></view></block></view><view class="{{['flex','uni-tab__cart-sub-box','',(fill)?'uni-tab__right':'']}}"><block wx:for="{{buttonGroup}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['buttonClick',[index,'$0'],[[['buttonGroup','',index]]]]]]]}}" class="flex uni-tab__cart-button-right" style="{{'background-color:'+(item.backgroundColor)+';'+('color:'+(item.color)+';')}}" bindtap="__e">{{item.text}}</view></block></view></view></view></block>