<template>
	<view>
		<view>
			<view style="width: 60%;height: 50px;margin: 120px auto 0px auto;padding-left: 50px;">
				<text>金额</text><br/>
				<text style="font-size: 36px;">&yen;{{amount}}</text>
			</view>
			<view><button style="background-color: #01C760;width: 60%;margin: 60px auto;" @click="pay">支付</button></view>
		</view>
		<uni-popup  ref="popup" type="center" @change="clear">
			<!-- 确认付款 -->
			<view v-if="affirm" style="width: 240px; height: 200px;" >
				<view>
					<view style="float: left;color: #808080;" @click="close">x</view>
					<view style="float: right;text-align: right;font-size: 16px;"><text style="color: #576B95;" @click="pwds">使用密码</text></view>
					<view style="clear: both;"></view>
				</view>
				<view style="text-align: center;width: 100%;border-bottom: #888888;font-size: 16px;">
					付给商家<br/><text style="font-size: 28px;">&yen;{{amount}}</text>
				</view>
				<view>
					<view style="float: left;font-size: 14px;color: #808080;">支付方式</view>
					<view style="float: right;text-align: right;font-size: 14px;color: #808080;">零钱></view>
					<view style="clear: both;"></view>
				</view>
				<view style="margin-top: 30px;">
					<button @click="yesPay" style="width: 80%;background-color:#01C760;">确认支付</button>
				</view>
			</view>
			<!-- 指纹付款 -->
			<!-- <view v-if="fingerprint">
				<view>
					<view style="float: left;color: #808080;" @click="close">x</view>
					<view style="float: right;text-align: right;font-size: 16px;"><text style="color: #576B95;" @click="pwds">使用密码</text></view>
					<view style="clear: both;"></view>
				</view>
			</view> -->
			<!-- 密码付款 -->
			<view v-if="pwd">
				<view style="width: 240px;">
					<view>
						<view style="float: left;color: #808080;" @click="close">x</view>
						<view style="float: right;text-align: right;font-size: 16px;"><text style="color: #576B95;" @click="yesPay">使用指纹</text></view>
						<view style="clear: both;"></view>
					</view>
					<view style="text-align: center; margin-top: 20px; width: 100%;border-bottom: #888888;font-size: 16px;">
						付给商家<br/><text style="font-size: 28px;">&yen;{{amount}}</text>
					</view>
					<view>
						<view style="float: left;font-size: 14px;color: #808080;">支付方式</view>
						<view style="float: right;text-align: right;font-size: 14px;color: #808080;">零钱></view>
						<view style="clear: both;"></view>
					</view>
					<view style="margin-top: 30px;">
						<!-- ref="pwdclear" -->
						<validCode :maxlength="6" :isPwd="true" @finish="getPwd" style="width: 100%;" ></validCode>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
	
</template>

<script>
	let _sef;
	import uniPopup from "@/components/uni-popup/uni-popup.vue"
	import validCode from '@/components/validCode.vue'
	export default{
		data(){
			return{
				amount:0,
				orderDate:'',
				deskNumber:0,
				selectMenus:[],
				affirm:true,
				//fingerprint:false,
				pwd:false,
				password:'666666',
				result:false,
				u_id:0,
				http:'',
				m_id:[],
				count:[]
			}
		},
		components:{
			uniPopup,
			validCode
		},
		methods:{
			pay(){
				this.$refs.popup.open();
			},
			close(){
				this.$refs.popup.close();
			},
			yesPay(){
				this.affirm = false;
				this.pwd = false;
				this.close();
				this.fg();
			},
			clear(){
				setTimeout(()=>{
					this.affirm = true;
					//this.fingerprint = false;
					this.pwd = false;
				},300);
			},
			//指纹支付
			fg(){
				uni.startSoterAuthentication({
				    requestAuthModes: ['fingerPrint'],//facial 人脸  fingerPrint指纹
				    challenge: '123456',
				    authContent: '请验证指纹',
				    success(res) {
				       //result console.log(res);
					   _sef.result = true;
					   _sef.payment();
				    },
				    fail(err) {
				        //console.log(err);
						setTimeout(
						()=>{
							uni.showToast({
								title: '指纹错误！'
							});
							_sef.pay();
							_sef.pwds();
						}
						, 2000);
						
				    }
				})
			},
			
			pwds(){
				this.affirm = false;
				//this.fingerprint = false;
				this.pwd = true;
			},
			getPwd(e){
				if(this.password == e){
					this.result = true;
					this.payment();
				}else{
					uni.showToast({
						title: '密码错误！'
					});
					//_sef.pwdclears();
					_sef.pwds();
				}
			},
			payment(){
				this.close();
				console.log(this.selectMenus);
				uni.request({
					method: 'POST',
					header: {
					    'content-type': 'application/x-www-form-urlencoded',   //自定义请求头信息
					},
					url: this.http+"zkq/addOrder", //仅为示例，并非真实接口地址。
					data: {
						orderDate : this.orderDate,
						u_id : this.u_id,
						amount : this.amount,
						deskNumber : this.deskNumber,
						m_id : this.m_id,
						count : this.count
					},
				    success: (res) => {
				        console.log(res.data);
				        //this.text = 'request success';
						//跳转页面
						uni.switchTab({
						    url: '/pages/index/index'
						});
				    }
				});
			},
			// pwdclears(){
			// 	this.$refs.pwdclear.clear();
			// }
		},
		onLoad(option){
			_sef = this;
			_sef.orderDate = option.orderDate
			_sef.deskNumber = option.deskNumber
			_sef.m_id = option.id
			_sef.count = option.count
			_sef.amount = option.price
			_sef.u_id = getApp().globalData.u_id
			_sef.http = getApp().globalData.http
		},
	}
</script>

<style>
</style>
