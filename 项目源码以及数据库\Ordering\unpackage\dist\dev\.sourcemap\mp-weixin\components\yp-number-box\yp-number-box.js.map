{"version": 3, "sources": ["webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/yp-number-box/yp-number-box.vue?5586", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/yp-number-box/yp-number-box.vue?65cc", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/yp-number-box/yp-number-box.vue?bb39", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/yp-number-box/yp-number-box.vue?a17a", "uni-app:///components/yp-number-box/yp-number-box.vue", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/yp-number-box/yp-number-box.vue?7d0f", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/yp-number-box/yp-number-box.vue?46b4"], "names": ["name", "props", "value", "type", "default", "min", "max", "step", "disabled", "index", "data", "inputValue", "windowHeight", "modelValue", "showHide", "watch", "a", "created", "methods", "_calcValue", "_getDecimalScale", "scale", "_onBlur", "console", "ifShow", "modelHide", "confirm"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACa;;;AAGzE;AAC+K;AAC/K,gBAAgB,wLAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAypB,CAAgB,8qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCqB7qB;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;IACA;EAEA;EACAO;IACA;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACAb;MACA;IACA;IACAS;MACA;QACA;QACA;UACAK;QACA;UACAA;QACA;QACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;QACA;MACA;MACA;MACA;MACA;MACA;QACAjB;MACA;QACAA;MACA;MACA;QACA;MACA;MACA;IACA;IACAkB;MACA;MACA;;MAEA;QACAC;MACA;MACA;IACA;IACAC;MACA;MACAC;MACA;MACA;QACA;QACA;MACA;MACArB;MACA;QACAA;MACA;QACAA;MACA;MACA;IACA;IACA;IACAsB;MACA;MACAD;MACA;IACA;IACA;IACAE;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACvIA;AAAA;AAAA;AAAA;AAA+7B,CAAgB,y7BAAG,EAAC,C;;;;;;;;;;;ACAn9B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/yp-number-box/yp-number-box.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./yp-number-box.vue?vue&type=template&id=687f4b80&\"\nvar renderjs\nimport script from \"./yp-number-box.vue?vue&type=script&lang=js&\"\nexport * from \"./yp-number-box.vue?vue&type=script&lang=js&\"\nimport style0 from \"./yp-number-box.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/yp-number-box/yp-number-box.vue\"\nexport default component.exports", "export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./yp-number-box.vue?vue&type=template&id=687f4b80&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./yp-number-box.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./yp-number-box.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"uni-numbox\">\n\t\t<view :class=\"{'uni-numbox--disabled': inputValue <= min || disabled}\" class=\"uni-numbox__minus\" @click=\"_calcValue('minus')\">-</view>\n\t\t<view :disabled=\"disabled\" v-model=\"inputValue\" class=\"uni-numbox__value\" type=\"number\" @tap=\"ifShow(inputValue)\" adjust-position=\"false\">{{inputValue}}</view>\n\t\t<view :class=\"{'uni-numbox--disabled': inputValue >= max || disabled} \"  class=\"uni-numbox__plus\"   @click=\"_calcValue('plus')\">+</view>\r\n\t\t<view class=\"modelBox\" v-if=\"showHide\">\r\n\t\t\t<view class=\"shade\" @tap=\"modelHide\"></view>\r\n\t\t\t<view class=\"model\">\r\n\t\t\t\t<view class=\"modelTitle\">请输入您的内容</view>\r\n\t\t\t\t<view class=\"modelInput\">\r\n\t\t\t\t\t<input  placeholder-class=\"inputStyle\" v-model=\"modelValue\"  type=\"number\" focus/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"modeBtnBox\">\r\n\t\t\t\t\t<view class=\"\" @tap=\"modelHide\">取消</view>\r\n\t\t\t\t\t<view class=\"\" @tap=\"confirm\">确定</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\n\t</view>\n</template>\n<script>\n\texport default {\n\t\tname: 'UniNumberBox',\n\t\tprops: {\n\t\t\tvalue: {\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tmin: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tmax: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 100\n\t\t\t},\n\t\t\tstep: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 1\n\t\t\t},\n\t\t\tdisabled: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t},\r\n\t\t\tindex:{\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t},\r\n\t\t\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tinputValue: 0,\r\n\t\t\t\twindowHeight: '',\r\n\t\t\t\tmodelValue:0,//model\r\n\t\t\t\tshowHide:false,//显示隐藏\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tvalue(val) {\n\t\t\t\tthis.inputValue = +val\n\t\t\t},\n\t\t\tinputValue(newVal, oldVal) {\n\t\t\t\tif (+newVal !== +oldVal) {\r\n\t\t\t\t\tlet a=null\r\n\t\t\t\t\tif(this.index!=undefined){\r\n\t\t\t\t\t\ta=[this.index,newVal]\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\ta=newVal\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.$emit('change', a)\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\tthis.inputValue = +this.value\n\t\t},\n\t\tmethods: {\n\t\t\t_calcValue(type) {\n\t\t\t\tif (this.disabled) {\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tconst scale = this._getDecimalScale()\n\t\t\t\tlet value = this.inputValue * scale\n\t\t\t\tlet step = this.step * scale\n\t\t\t\tif (type === 'minus') {\n\t\t\t\t\tvalue -= step\n\t\t\t\t} else if (type === 'plus') {\n\t\t\t\t\tvalue += step\n\t\t\t\t}\n\t\t\t\tif (value < this.min || value > this.max) {\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tthis.inputValue = value / scale\n\t\t\t},\n\t\t\t_getDecimalScale() {\n\t\t\t\tlet scale = 1\n\t\t\t\t// 浮点型\r\n\t\t\t\t\n\t\t\t\tif (~~this.step !== this.step) {\n\t\t\t\t\tscale = Math.pow(10, (this.step + '').split('.')[1].length)\n\t\t\t\t}\n\t\t\t\treturn scale\n\t\t\t},\n\t\t\t_onBlur(event) {\n\t\t\t\tlet value = event.detail.value\r\n\t\t\t\tconsole.log(value)\r\n\t\t\t\treturn;\n\t\t\t\tif (!value) {\n\t\t\t\t\tthis.inputValue = 0\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tvalue = +value\n\t\t\t\tif (value > this.max) {\n\t\t\t\t\tvalue = this.max\n\t\t\t\t} else if (value < this.min) {\n\t\t\t\t\tvalue = this.min\n\t\t\t\t}\n\t\t\t\tthis.inputValue = value\n\t\t\t},\r\n\t\t\t// 显示model\r\n\t\t\tifShow(e){\r\n\t\t\t\tthis.modelValue=e\r\n\t\t\t\tconsole.log(this.modelValue)\r\n\t\t\t\tthis.showHide=true\r\n\t\t\t},\r\n\t\t\t//隐藏model\r\n\t\t\tmodelHide(){\r\n\t\t\t\tthis.showHide=false\r\n\t\t\t},\r\n\t\t\t// 确定\r\n\t\t\tconfirm(){\r\n\t\t\t\tthis.inputValue=this.modelValue\r\n\t\t\t\tthis.showHide=false\r\n\t\t\t}\n\t\t}\n\t}\n</script>\n<style>\n\t@charset \"UTF-8\";\n\n\t.uni-numbox {\n\t\tdisplay: inline-flex;\n\t\tflex-direction: row;\n\t\tjustify-content: space-between;\n\t\theight: 50upx;\r\n\t\twidth: 220upx;\n\t\tposition: relative\n\t}\n\n\t.uni-numbox:after {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\ttransform-origin: center;\n\t\tbox-sizing: border-box;\n\t\tpointer-events: none;\n\t\ttop: -50%;\n\t\tleft: -50%;\n\t\tright: -50%;\n\t\tbottom: -50%;\n\t\tborder: 1px solid #c8c7cc;\n\t\tborder-radius: 12upx;\n\t\ttransform: scale(.5)\n\t}\n\n\t.uni-numbox__minus,\n\t.uni-numbox__plus {\n\t\tmargin: 0;\n\t\tbackground-color: #f8f8f8;\n\t\twidth: 70upx;\n\t\tfont-size: 40upx;\n\t\theight: 100%;\n\t\tline-height: 70upx;\n\t\ttext-align: center;\n\t\tdisplay: inline-flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tcolor: #333;\n\t\tposition: relative\n\t}\n\n\t.uni-numbox__value {\n\t\tposition: relative;\n\t\tbackground-color: #fff;\n\t\twidth: 108upx;\n\t\theight: 100%;\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\n\t\tpadding: 0\n\t}\n\n\t.uni-numbox__value:after {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\ttransform-origin: center;\n\t\tbox-sizing: border-box;\n\t\tpointer-events: none;\n\t\ttop: -50%;\n\t\tleft: -50%;\n\t\tright: -50%;\n\t\tbottom: -50%;\n\t\tborder-style: solid;\n\t\tborder-color: #c8c7cc;\n\t\tborder-left-width: 1px;\n\t\tborder-right-width: 1px;\n\t\tborder-top-width: 0;\n\t\tborder-bottom-width: 0;\n\t\ttransform: scale(.5)\n\t}\n\n\t.uni-numbox--disabled {\n\t\tcolor: silver\n\t}\r\n\t.modelBox{\r\n\t}\r\n\t.shade{\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tz-index: 100;\r\n\t\tbackground-color: rgba(0,0,0,.6);\r\n\t}\r\n\t.model{\r\n\t\twidth: 80%;\r\n\t\tborder-radius: 35upx;\r\n\t\tbackground-color: #fff;\r\n\t\tpadding: 30upx;\r\n\t\tbox-sizing: border-box;\r\n\t\tposition: fixed;\r\n\t\ttop: 50%;\r\n\t\tleft: 50%;\r\n\t\ttransform: translate(-50%,-50%);\r\n\t\tz-index: 200;\r\n\t}\r\n\t.modelTitle{\r\n\t\tfont-size: 34upx;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 20upx;\r\n\t}\r\n\t.modelInput{\r\n\t\twidth: 100%;\r\n\t\theight: 40upx;\r\n\t\tfont-size: 30upx;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 40upx;\r\n\t}\r\n\t.modelInput input{\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder: none;\r\n\t\tborder-bottom:2upx solid #8fba5d ;\r\n\t}\r\n\t.modeBtnBox{\r\n\t\twidth: 100%;\r\n\t\theight: 40upx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: flex-end;\r\n\t\tcolor:#8fba5d ;\r\n\t\tfont-size: 30upx;\r\n\t}\r\n\t.modeBtnBox view{\r\n\t\twidth: 25%;\r\n\t\theight: 100%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\n</style>", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./yp-number-box.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./yp-number-box.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1748248725575\n      var cssReload = require(\"E:/software/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}