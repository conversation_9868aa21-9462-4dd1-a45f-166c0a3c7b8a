{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/uni-app/Ordering/components/uni-popup/uni-popup1.vue?380e", "webpack:///C:/Users/<USER>/Desktop/uni-app/Ordering/components/uni-popup/uni-popup1.vue?1de2", "webpack:///C:/Users/<USER>/Desktop/uni-app/Ordering/components/uni-popup/uni-popup1.vue?7842", "webpack:///C:/Users/<USER>/Desktop/uni-app/Ordering/components/uni-popup/uni-popup1.vue?1da7", "webpack:///C:/Users/<USER>/Desktop/uni-app/Ordering/components/uni-popup/uni-popup1.vue", "webpack:///C:/Users/<USER>/Desktop/uni-app/Ordering/components/uni-popup/uni-popup1.vue?ff5c", "webpack:///C:/Users/<USER>/Desktop/uni-app/Ordering/components/uni-popup/uni-popup1.vue?509b"], "names": ["name", "props", "animation", "type", "Boolean", "default", "String", "custom", "maskClick", "show", "data", "ani", "showPopup", "watch", "newValue", "open", "close", "created", "methods", "clear", "$emit", "$nextTick", "setTimeout"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyF;AAC3B;AACL;AACa;;;AAGtE;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEA;AACA,IAAI,KAAU,EAAE,YAiBf;AACD;AACe,gF;;;;;;;;;;;;ACvCf;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACNA;AAAA;AAAA;AAAA;AAAo9B,CAAgB,y6BAAG,EAAC,C;;;;;;;;;;;;wFCAx+B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGe;AACdA,MAAI,EAAE,WADQ;AAEdC,OAAK,EAAE;AACN;AACAC,aAAS,EAAE;AACVC,UAAI,EAAEC,OADI;AAEVC,aAAO,EAAE,IAFC,EAFL;;AAMN;AACAF,QAAI,EAAE;AACLA,UAAI,EAAEG,MADD;AAELD,aAAO,EAAE,QAFJ,EAPA;;AAWN;AACAE,UAAM,EAAE;AACPJ,UAAI,EAAEC,OADC;AAEPC,aAAO,EAAE,KAFF,EAZF;;AAgBN;AACAG,aAAS,EAAE;AACVL,UAAI,EAAEC,OADI;AAEVC,aAAO,EAAE,IAFC,EAjBL;;AAqBNI,QAAI,EAAE;AACLN,UAAI,EAAEC,OADD;AAELC,aAAO,EAAE,IAFJ,EArBA,EAFO;;;AA4BdK,MA5Bc,kBA4BP;AACN,WAAO;AACNC,SAAG,EAAE,EADC;AAENC,eAAS,EAAE,KAFL,EAAP;;AAIA,GAjCa;AAkCdC,OAAK,EAAE;AACNJ,QADM,gBACDK,QADC,EACS;AACd,UAAIA,QAAJ,EAAc;AACb,aAAKC,IAAL;AACA,OAFD,MAEO;AACN,aAAKC,KAAL;AACA;AACD,KAPK,EAlCO;;AA2CdC,SA3Cc,qBA2CJ,CAAE,CA3CE;AA4CdC,SAAO,EAAE;AACRC,SADQ,mBACA,CAAE,CADF;AAERJ,QAFQ,kBAED;AACN,WAAKK,KAAL,CAAW,QAAX,EAAqB;AACpBX,YAAI,EAAE,IADc,EAArB;;AAGA,WAAKG,SAAL,GAAiB,IAAjB;AACA,WAAKS,SAAL,CAAe,YAAM;AACpBC,kBAAU,CAAC,YAAM;AAChB,eAAI,CAACX,GAAL,GAAW,SAAS,KAAI,CAACR,IAAzB;AACA,SAFS,EAEP,EAFO,CAAV;AAGA,OAJD;AAKA,KAZO;AAaRa,SAbQ,iBAaFb,IAbE,EAaI;AACX,UAAI,CAAC,KAAKK,SAAN,IAAmBL,IAAvB,EAA6B;AAC7B,WAAKiB,KAAL,CAAW,QAAX,EAAqB;AACpBX,YAAI,EAAE,KADc,EAArB;;AAGA,WAAKE,GAAL,GAAW,EAAX;AACA,WAAKU,SAAL,CAAe,YAAM;AACpBC,kBAAU,CAAC,YAAM;AAChB,gBAAI,CAACV,SAAL,GAAiB,KAAjB;AACA,SAFS,EAEP,GAFO,CAAV;AAGA,OAJD;AAKA,KAxBO,EA5CK,E;;;;;;;;;;;;ACbf;AAAA;AAAA;AAAA;AAAsxC,CAAgB,gsCAAG,EAAC,C;;;;;;;;;;;ACA1yC,uC", "file": "components/uni-popup/uni-popup1.js", "sourcesContent": ["import { render, staticRenderFns } from \"./uni-popup1.vue?vue&type=template&id=30c9038e&\"\nimport script from \"./uni-popup1.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-popup1.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-popup1.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\作业\\\\大二末\\\\Spring\\\\HBuilderX.1.9.9.20190522.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!module.hot.data) {\n      api.createRecord('30c9038e', component.options)\n    } else {\n      api.reload('30c9038e', component.options)\n    }\n    module.hot.accept(\"./uni-popup1.vue?vue&type=template&id=30c9038e&\", function () {\n      api.rerender('30c9038e', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"uni-app/Ordering/components/uni-popup/uni-popup1.vue\"\nexport default component.exports", "export * from \"-!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-hbuilderx/packages/webpack-uni-nvue-loader/lib/templateLoader.js??vue-loader-options!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/vue-loader/lib/index.js??vue-loader-options!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-custom-block-loader/index.js??ref--0-1!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-popup1.vue?vue&type=template&id=30c9038e&\"", "var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "import mod from \"-!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/vue-loader/lib/index.js??vue-loader-options!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-custom-block-loader/index.js??ref--0-1!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-popup1.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/vue-loader/lib/index.js??vue-loader-options!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-custom-block-loader/index.js??ref--0-1!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-popup1.vue?vue&type=script&lang=js&\"", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n\nexport default {\n\tname: 'UniPopup1',\n\tprops: {\n\t\t// 开启动画\n\t\tanimation: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// 弹出层类型，可选值，top: 顶部弹出层；bottom：底部弹出层；center：全屏弹出层\n\t\ttype: {\n\t\t\ttype: String,\n\t\t\tdefault: 'center'\n\t\t},\n\t\t// 是否开启自定义\n\t\tcustom: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// maskClick\n\t\tmaskClick: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\tshow: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t}\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tani: '',\n\t\t\tshowPopup: false\n\t\t}\n\t},\n\twatch: {\n\t\tshow(newValue) {\n\t\t\tif (newValue) {\n\t\t\t\tthis.open()\n\t\t\t} else {\n\t\t\t\tthis.close()\n\t\t\t}\n\t\t}\n\t},\n\tcreated() {},\n\tmethods: {\n\t\tclear() {},\n\t\topen() {\n\t\t\tthis.$emit('change', {\n\t\t\t\tshow: true\n\t\t\t})\n\t\t\tthis.showPopup = true\n\t\t\tthis.$nextTick(() => {\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.ani = 'uni-' + this.type\n\t\t\t\t}, 30)\n\t\t\t})\n\t\t},\n\t\tclose(type) {\n\t\t\tif (!this.maskClick && type) return\n\t\t\tthis.$emit('change', {\n\t\t\t\tshow: false\n\t\t\t})\n\t\t\tthis.ani = ''\n\t\t\tthis.$nextTick(() => {\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.showPopup = false\n\t\t\t\t}, 300)\n\t\t\t})\n\t\t}\n\t}\n}\n", "import mod from \"-!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-1!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/index.js??ref--6-oneOf-1-2!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/vue-loader/lib/index.js??vue-loader-options!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-custom-block-loader/index.js??ref--0-1!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-popup1.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-1!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/index.js??ref--6-oneOf-1-2!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/vue-loader/lib/index.js??vue-loader-options!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-custom-block-loader/index.js??ref--0-1!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-popup1.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin"], "sourceRoot": ""}