<view class="uni-swiper__warp"><slot></slot><block wx:if="{{mode==='default'}}"><view class="uni-swiper__dots-box" style="{{'bottom:'+(dots.bottom+'px')+';'}}"><block wx:for="{{info}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="uni-swiper__dots-item uni-swiper__dots-bar" style="{{'width:'+((index===current?dots.width*2:dots.width)+'px')+';'+('height:'+(dots.width/3+'px')+';')+('background-color:'+(index!==current?dots.backgroundColor:dots.selectedBackgroundColor)+';')+('border-radius:'+('0px')+';')}}"></view></block></view></block><block wx:if="{{mode==='dot'}}"><view class="uni-swiper__dots-box" style="{{'bottom:'+(dots.bottom+'px')+';'}}"><block wx:for="{{info}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="uni-swiper__dots-item" style="{{'width:'+(dots.width+'px')+';'+('height:'+(dots.height+'px')+';')+('background-color:'+(index!==current?dots.backgroundColor:dots.selectedBackgroundColor)+';')+('border:'+(index!==current?dots.border:dots.selectedBorder)+';')}}"></view></block></view></block><block wx:if="{{mode==='round'}}"><view class="uni-swiper__dots-box" style="{{'bottom:'+(dots.bottom+'px')+';'}}"><block wx:for="{{info}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['uni-swiper__dots-item ',index===current&&'uni-swiper__dots-long']}}" style="{{'width:'+((index===current?dots.width*3:dots.width)+'px')+';'+('height:'+(dots.height+'px')+';')+('background-color:'+(index!==current?dots.backgroundColor:dots.selectedBackgroundColor)+';')+('border:'+(index!==current?dots.border:dots.selectedBorder)+';')}}"></view></block></view></block><block wx:if="{{mode==='nav'}}"><view class="uni-swiper__dots-box uni-swiper__dots-nav" style="{{'background-color:'+(dotsStyles.backgroundColor)+';'}}"><view class="uni-swiper__dots-nav-item" style="{{'color:'+(dotsStyles.color)+';'}}">{{current+1+"/"+info.length+"\n\t\t\t\t"+info[current][field]+''}}</view><view style="width:30%;margin-left:30%;text-align:right;color:red;">{{"¥"+info[current][cash]}}</view></view></block><block wx:if="{{mode==='indexes'}}"><view class="uni-swiper__dots-box" style="{{'bottom:'+(dots.bottom+'px')+';'}}"><block wx:for="{{info}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="uni-swiper__dots-item uni-swiper__dots-indexes" style="{{'width:'+(dots.width+'px')+';'+('height:'+(dots.height+'px')+';')+('color:'+(index===current?dots.selectedColor:dots.color)+';')+('background-color:'+(index!==current?dots.backgroundColor:dots.selectedBackgroundColor)+';')+('border:'+(index!==current?dots.border:dots.selectedBorder)+';')}}">{{index+1}}</view></block></view></block></view>