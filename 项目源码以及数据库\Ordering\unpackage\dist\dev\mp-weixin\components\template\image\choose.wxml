<view class="data-v-4dd6e2d6"><view class="feedback-body feedback-uploader data-v-4dd6e2d6"><view class="uni-uploader data-v-4dd6e2d6"><view class="uni-uploader-head data-v-4dd6e2d6" style="text-align:right;"><view class="data-v-4dd6e2d6"></view><view class="uni-uploader-info data-v-4dd6e2d6">{{$root.g0+"/"+count}}</view></view><view class="uni-uploader-body data-v-4dd6e2d6"><view class="uni-uploader__files data-v-4dd6e2d6"><block wx:for="{{imgList}}" wx:for-item="image" wx:for-index="index" wx:key="index"><block class="data-v-4dd6e2d6"><view class="uni-uploader__file data-v-4dd6e2d6" style="position:relative;"><image class="uni-uploader__img data-v-4dd6e2d6" mode="aspectFill" src="{{image}}"></image><block wx:if="{{index}}"><view data-event-opts="{{[['tap',[['setCapital',[index]]]]]}}" class="set-capital data-v-4dd6e2d6" style="background:#0A98D5;" bindtap="__e">设为主图</view></block><block wx:else><view class="set-capital data-v-4dd6e2d6">主图</view></block><view data-event-opts="{{[['tap',[['close',[index]]]]]}}" class="close-view data-v-4dd6e2d6" bindtap="__e">x</view></view></block></block><view hidden="{{!($root.g1<count)}}" class="uni-uploader__input-box data-v-4dd6e2d6"><view data-event-opts="{{[['tap',[['chooseImg',['$event']]]]]}}" class="uni-uploader__input data-v-4dd6e2d6" bindtap="__e"></view></view></view></view></view></view></view>