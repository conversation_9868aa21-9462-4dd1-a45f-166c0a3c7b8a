<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.aaa.mapper.SysPermissionMapper" >
  <resultMap id="BaseResultMap" type="SysPermission" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="VARCHAR" />
    <result column="url" property="url" jdbcType="VARCHAR" />
    <result column="percode" property="percode" jdbcType="VARCHAR" />
    <result column="parentid" property="parentid" jdbcType="BIGINT" />
    <result column="parentids" property="parentids" jdbcType="VARCHAR" />
    <result column="sortstring" property="sortstring" jdbcType="VARCHAR" />
    <result column="available" property="available" jdbcType="CHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, name, type, url, percode, parentid, parentids, sortstring, available
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="SysPermissionExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sys_permission
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from sys_permission
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from sys_permission
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="SysPermissionExample" >
    delete from sys_permission
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="SysPermission" >
    insert into sys_permission (id, name, type, 
      url, percode, parentid, 
      parentids, sortstring, available
      )
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, 
      #{url,jdbcType=VARCHAR}, #{percode,jdbcType=VARCHAR}, #{parentid,jdbcType=BIGINT}, 
      #{parentids,jdbcType=VARCHAR}, #{sortstring,jdbcType=VARCHAR}, #{available,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="SysPermission" >
    insert into sys_permission
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="name != null" >
        name,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="url != null" >
        url,
      </if>
      <if test="percode != null" >
        percode,
      </if>
      <if test="parentid != null" >
        parentid,
      </if>
      <if test="parentids != null" >
        parentids,
      </if>
      <if test="sortstring != null" >
        sortstring,
      </if>
      <if test="available != null" >
        available,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="url != null" >
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="percode != null" >
        #{percode,jdbcType=VARCHAR},
      </if>
      <if test="parentid != null" >
        #{parentid,jdbcType=BIGINT},
      </if>
      <if test="parentids != null" >
        #{parentids,jdbcType=VARCHAR},
      </if>
      <if test="sortstring != null" >
        #{sortstring,jdbcType=VARCHAR},
      </if>
      <if test="available != null" >
        #{available,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="SysPermissionExample" resultType="java.lang.Integer" >
    select count(*) from sys_permission
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update sys_permission
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null" >
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null" >
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.url != null" >
        url = #{record.url,jdbcType=VARCHAR},
      </if>
      <if test="record.percode != null" >
        percode = #{record.percode,jdbcType=VARCHAR},
      </if>
      <if test="record.parentid != null" >
        parentid = #{record.parentid,jdbcType=BIGINT},
      </if>
      <if test="record.parentids != null" >
        parentids = #{record.parentids,jdbcType=VARCHAR},
      </if>
      <if test="record.sortstring != null" >
        sortstring = #{record.sortstring,jdbcType=VARCHAR},
      </if>
      <if test="record.available != null" >
        available = #{record.available,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update sys_permission
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=VARCHAR},
      url = #{record.url,jdbcType=VARCHAR},
      percode = #{record.percode,jdbcType=VARCHAR},
      parentid = #{record.parentid,jdbcType=BIGINT},
      parentids = #{record.parentids,jdbcType=VARCHAR},
      sortstring = #{record.sortstring,jdbcType=VARCHAR},
      available = #{record.available,jdbcType=CHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="SysPermission" >
    update sys_permission
    <set >
      <if test="name != null" >
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="url != null" >
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="percode != null" >
        percode = #{percode,jdbcType=VARCHAR},
      </if>
      <if test="parentid != null" >
        parentid = #{parentid,jdbcType=BIGINT},
      </if>
      <if test="parentids != null" >
        parentids = #{parentids,jdbcType=VARCHAR},
      </if>
      <if test="sortstring != null" >
        sortstring = #{sortstring,jdbcType=VARCHAR},
      </if>
      <if test="available != null" >
        available = #{available,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="SysPermission" >
    update sys_permission
    set name = #{name,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      url = #{url,jdbcType=VARCHAR},
      percode = #{percode,jdbcType=VARCHAR},
      parentid = #{parentid,jdbcType=BIGINT},
      parentids = #{parentids,jdbcType=VARCHAR},
      sortstring = #{sortstring,jdbcType=VARCHAR},
      available = #{available,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>