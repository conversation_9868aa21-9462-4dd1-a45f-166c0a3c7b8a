{"version": 3, "sources": ["webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/template/image/choose.vue?1416", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/template/image/choose.vue?de6c", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/template/image/choose.vue?dbb0", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/template/image/choose.vue?13bc", "uni-app:///components/template/image/choose.vue", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/template/image/choose.vue?7cee", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/template/image/choose.vue?92d4"], "names": ["name", "props", "imgList", "type", "default", "quality", "count", "changes", "data", "watch", "methods", "chooseImg", "uni", "success", "title", "icon", "close", "setCapital"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACqC;;;AAG1F;AAC+K;AAC/K,gBAAgB,wLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAAkpB,CAAgB,uqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC6BtqB;EACAA;EACAC;IAEAC;MACA;MACAC;MACAC;QACA;MACA;IACA;IACAC;MACA;MACAF;MACAC;IACA;IACAE;MACA;MACAH;MACAC;IACA;IACAG;MACA;MACAJ;MACAC;IACA;EACA;EAEAI;IACA,QAEA;EACA;EACAC;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA,CACA;EAEAC;IAEAC;MAAA;MAAA;MACA;MACA;MACAC;QACAN;QACAO;UACA;UACA;UACA;UACA;YACAX;YACA;UACA;YACA;YACA;YACA;cACAA;YACA;YACA;YACAU;cACAE;cACAC;YACA;UAEA;QAGA;MACA;IACA;IACAC;MACA;MACAd;MACA;IAEA;IACAe;MACA;MACAf;MACA;IACA;EAEA;AACA;AAAA,4B;;;;;;;;;;;;;ACtHA;AAAA;AAAA;AAAA;AAAg9B,CAAgB,08BAAG,EAAC,C;;;;;;;;;;;ACAp+B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/template/image/choose.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./choose.vue?vue&type=template&id=4dd6e2d6&scoped=true&\"\nvar renderjs\nimport script from \"./choose.vue?vue&type=script&lang=js&\"\nexport * from \"./choose.vue?vue&type=script&lang=js&\"\nimport style0 from \"./choose.vue?vue&type=style&index=0&id=4dd6e2d6&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4dd6e2d6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/template/image/choose.vue\"\nexport default component.exports", "export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./choose.vue?vue&type=template&id=4dd6e2d6&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.imgList.length\n  var g1 = _vm.imgList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./choose.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./choose.vue?vue&type=script&lang=js&\"", "<template>\r\n    <view>\r\n        <view class=\"feedback-body feedback-uploader\">\r\n            <view class=\"uni-uploader\">\r\n                <view class=\"uni-uploader-head\" style=\"text-align: right;\">\r\n                    <view class=\"\"></view>\r\n                    <view class=\"uni-uploader-info\">{{imgList.length}}/{{count}}</view>\r\n                </view>\r\n                <view class=\"uni-uploader-body\">\r\n                    <view class=\"uni-uploader__files\" >\r\n                        <block v-for=\"(image,index) in imgList\" :key=\"index\">\r\n                            <view class=\"uni-uploader__file\" style=\"position: relative;\">\r\n                                <image mode=\"aspectFill\" class=\"uni-uploader__img\" :src=\"image\"></image>\r\n                                <view v-if=\"index\" class=\"set-capital\" style=\"background: #0A98D5;\" @tap=\"setCapital(index)\">设为主图</view>\r\n                                <view v-else class=\"set-capital\">主图</view>\r\n                                <view class=\"close-view\" @click=\"close(index)\">x</view>\r\n                            </view>\r\n                        </block>\r\n                        <view class=\"uni-uploader__input-box\" v-show=\"imgList.length < count\">\r\n                            <view class=\"uni-uploader__input\" @tap=\"chooseImg\"></view>\r\n                        </view>\r\n                    </view>\r\n                </view>\r\n            </view>\r\n        </view>\r\n\r\n    </view>\r\n</template>\r\n<script type=\"text/javascript\">\r\n    export default {\r\n        name: 'image-choose',\r\n        props: {\r\n\r\n            imgList: {\r\n                //接收的图片列表[{src:12.jpg}]\r\n                type: Array,\r\n                default: function() {\r\n                    return []\r\n                }\r\n            },\r\n            quality: {\r\n                //质量\r\n                type: Number,\r\n                default: 1\r\n            },\r\n            count: {\r\n                //最多数量\r\n                type: Number,\r\n                default: 5\r\n            },\r\n            changes: {\r\n                //监听变化\r\n                type: Function,\r\n                default: null\r\n            }\r\n        },\r\n\r\n        data() {\r\n            return {\r\n\r\n            }\r\n        },\r\n        watch: {\r\n\r\n            // imgList: {\r\n            //     handler(val, oldval) {\r\n            //         this.$emit(\"changes\", val);\r\n            //         // console.log(val)\r\n            //     },\r\n            //     deep: true\r\n            // }\r\n        },\r\n\r\n        methods: {\r\n\r\n            chooseImg() { //选择图片\r\n                // console.log('选择图片')\r\n                var count = this.count - this.imgList.length;\r\n                uni.chooseImage({\r\n                    count: count,\r\n                    success: (res) => {\r\n                        var imgs = res.tempFilePaths || [];\r\n                        var count = imgs.length + this.imgList.length;\r\n                        var imgList = this.imgList;\r\n                        if (count <= this.count) {\r\n                            imgList = this.imgList.concat(res.tempFilePaths);\r\n                            this.$emit(\"changes\", imgList);\r\n                        } else {\r\n                            var len = this.count - this.imgList.length;\r\n                            // console.log(len)\r\n                            for (var i = 0; i < len; i++) {\r\n                                imgList.push(res.tempFilePaths[i])\r\n                            }\r\n                            this.$emit(\"changes\", imgList);\r\n                            uni.showToast({\r\n                                title: '最多只能添加' + this.count + '张图片',\r\n                                icon: 'none'\r\n                            })\r\n\r\n                        }\r\n\r\n\r\n                    },\r\n                })\r\n            },\r\n            close(e) {\r\n                var imgList = this.imgList;\r\n                imgList.splice(e, 1);\r\n                this.$emit(\"changes\", imgList);\r\n\r\n            },\r\n            setCapital(i, name) {\r\n                var imgList=this.imgList;\r\n                imgList[0]= imgList.splice(i,1,imgList[0])[0];\r\n                this.$emit(\"changes\", imgList);\r\n            },\r\n\r\n        }\r\n    }\r\n</script>\r\n<style scoped>\r\n    .set-capital {\r\n        text-align: center;\r\n        line-height: 30px;\r\n        height: 30px;\r\n        padding: 0 5px;\r\n        right: 0;\r\n        /* width: 30px; */\r\n        background: #1AAD19;\r\n        color: #FFFFFF;\r\n        position: absolute;\r\n        bottom: 0px;\r\n        font-size: 12px;\r\n    }\r\n\r\n    .close-view {\r\n        text-align: center;\r\n        line-height: 14px;\r\n        height: 16px;\r\n        width: 16px;\r\n        border-radius: 50%;\r\n        background: #FF5053;\r\n        color: #FFFFFF;\r\n        position: absolute;\r\n        top: -6px;\r\n        right: -4px;\r\n        font-size: 12px;\r\n    }\r\n</style>\n", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./choose.vue?vue&type=style&index=0&id=4dd6e2d6&scoped=true&lang=css&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./choose.vue?vue&type=style&index=0&id=4dd6e2d6&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1748248725895\n      var cssReload = require(\"E:/software/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}