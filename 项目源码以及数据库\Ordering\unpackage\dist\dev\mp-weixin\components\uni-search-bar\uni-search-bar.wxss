
@charset "UTF-8";
.uni-searchbar-form {
	position: relative;
	display: flex;
	padding: 15rpx;
	width: 100%;
	box-sizing: border-box
}
.uni-searchbar-form__box {
	display: flex;
	flex: 1;
	align-items: center;
	width: 100%;
	height: 64rpx;
	color: #c8c7cc;
	background: #fff;
	border: solid 1px #c8c7cc;
	border-radius: 10rpx
}
.uni-searchbar-form__box .icon-search {
	color: #c8c7cc;
	line-height: 24px;
	padding: 0rpx 10rpx 0rpx 15rpx
}
.uni-searchbar-form__box .search-input {
	flex: 1;
	font-size: 28rpx;
	height: 64rpx;
	line-height: 64rpx;
	color: #333
}
.uni-searchbar-form__box .icon-clear {
	color: #c8c7cc;
	line-height: 20px;
	padding: 0rpx 15rpx 0rpx 10rpx
}
.uni-searchbar-form__text {
	display: flex;
	flex: 1;
	align-items: center;
	width: 100%;
	height: 64rpx;
	line-height: 64rpx;
	text-align: center;
	color: #c8c7cc;
	background: #fff;
	border: solid 1px #c8c7cc;
	border-radius: 10rpx;
	display: none
}
.uni-searchbar-form__text .icon-search {
	height: 64rpx;
	line-height: 64rpx
}
.uni-searchbar-form__text .placeholder {
	display: inline-block;
	font-size: 28rpx;
	color: #ccc;
	margin-left: 10rpx
}
.uni-searchbar-form__cancel {
	padding-left: 20rpx;
	line-height: 64rpx;
	color: #333;
	white-space: nowrap
}
.uni-searchbar-form.hide .uni-searchbar-form__box {
	display: none
}
.uni-searchbar-form.hide .uni-searchbar-form__text {
	display: block
}
.uni-searchbar-form.hide .uni-searchbar-form__cancel {
	display: none
}

