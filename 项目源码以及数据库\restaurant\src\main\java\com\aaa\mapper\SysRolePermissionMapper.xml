<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.aaa.mapper.SysRolePermissionMapper" >
  <resultMap id="BaseResultMap" type="SysRolePermission" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="sys_role_id" property="sysRoleId" jdbcType="VARCHAR" />
    <result column="sys_permission_id" property="sysPermissionId" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, sys_role_id, sys_permission_id
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="SysRolePermissionExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sys_role_permission
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from sys_role_permission
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from sys_role_permission
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="SysRolePermissionExample" >
    delete from sys_role_permission
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="SysRolePermission" >
    insert into sys_role_permission (id, sys_role_id, sys_permission_id
      )
    values (#{id,jdbcType=VARCHAR}, #{sysRoleId,jdbcType=VARCHAR}, #{sysPermissionId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="SysRolePermission" >
    insert into sys_role_permission
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="sysRoleId != null" >
        sys_role_id,
      </if>
      <if test="sysPermissionId != null" >
        sys_permission_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="sysRoleId != null" >
        #{sysRoleId,jdbcType=VARCHAR},
      </if>
      <if test="sysPermissionId != null" >
        #{sysPermissionId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="SysRolePermissionExample" resultType="java.lang.Integer" >
    select count(*) from sys_role_permission
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update sys_role_permission
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.sysRoleId != null" >
        sys_role_id = #{record.sysRoleId,jdbcType=VARCHAR},
      </if>
      <if test="record.sysPermissionId != null" >
        sys_permission_id = #{record.sysPermissionId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update sys_role_permission
    set id = #{record.id,jdbcType=VARCHAR},
      sys_role_id = #{record.sysRoleId,jdbcType=VARCHAR},
      sys_permission_id = #{record.sysPermissionId,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="SysRolePermission" >
    update sys_role_permission
    <set >
      <if test="sysRoleId != null" >
        sys_role_id = #{sysRoleId,jdbcType=VARCHAR},
      </if>
      <if test="sysPermissionId != null" >
        sys_permission_id = #{sysPermissionId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="SysRolePermission" >
    update sys_role_permission
    set sys_role_id = #{sysRoleId,jdbcType=VARCHAR},
      sys_permission_id = #{sysPermissionId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>