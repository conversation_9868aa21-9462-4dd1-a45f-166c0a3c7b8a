<view><cmd-page-body vue-id="9bfc83bc-1" type="top" bind:__l="__l" vue-slots="{{['default']}}"><cmd-transition vue-id="{{('9bfc83bc-2')+','+('9bfc83bc-1')}}" name="fade-up" bind:__l="__l" vue-slots="{{['default']}}"><view><cmd-cel-item vue-id="{{('9bfc83bc-3')+','+('9bfc83bc-2')}}" title="头像" slot-right="{{true}}" arrow="{{true}}" bind:__l="__l" vue-slots="{{['default']}}"><cmd-avatar vue-id="{{('9bfc83bc-4')+','+('9bfc83bc-3')}}" src="{{img}}" bind:__l="__l"></cmd-avatar></cmd-cel-item><cmd-cel-item vue-id="{{('9bfc83bc-5')+','+('9bfc83bc-2')}}" title="昵称" addon="{{name}}" arrow="{{true}}" bind:__l="__l"></cmd-cel-item><button data-event-opts="{{[['tap',[['exitLogin',['$event']]]]]}}" class="btn-logout" bindtap="__e">退出登录</button></view></cmd-transition></cmd-page-body></view>