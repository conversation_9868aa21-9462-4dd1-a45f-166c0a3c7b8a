{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/uni-app/Ordering/components/uni-number-box/uni-number-box.vue?e791", "webpack:///C:/Users/<USER>/Desktop/uni-app/Ordering/components/uni-number-box/uni-number-box.vue?c0b7", "webpack:///C:/Users/<USER>/Desktop/uni-app/Ordering/components/uni-number-box/uni-number-box.vue?267d", "webpack:///C:/Users/<USER>/Desktop/uni-app/Ordering/components/uni-number-box/uni-number-box.vue?a404", "webpack:///C:/Users/<USER>/Desktop/uni-app/Ordering/components/uni-number-box/uni-number-box.vue", "webpack:///C:/Users/<USER>/Desktop/uni-app/Ordering/components/uni-number-box/uni-number-box.vue?fd30", "webpack:///C:/Users/<USER>/Desktop/uni-app/Ordering/components/uni-number-box/uni-number-box.vue?3be2"], "names": ["name", "props", "value", "type", "Number", "String", "default", "min", "max", "step", "disabled", "Boolean", "data", "inputValue", "watch", "val", "newVal", "oldVal", "$emit", "created", "methods", "_calcValue", "scale", "_getDecimalScale", "Math", "pow", "split", "length", "_onBlur", "event", "detail"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6F;AAC3B;AACL;AACa;;;AAG1E;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEA;AACA,IAAI,KAAU,EAAE,YAiBf;AACD;AACe,gF;;;;;;;;;;;;ACvCf;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACNA;AAAA;AAAA;AAAA;AAAw9B,CAAgB,66BAAG,EAAC,C;;;;;;;;;;;;wFCA5+B;AACA;AACA;AACA;AACA;AACA;AACA;;AAEe;AACdA,MAAI,EAAE,cADQ;AAEdC,OAAK,EAAE;AACNC,SAAK,EAAE;AACNC,UAAI,EAAE,CAACC,MAAD,EAASC,MAAT,CADA;AAENC,aAAO,EAAE,CAFH,EADD;;AAKNC,OAAG,EAAE;AACJJ,UAAI,EAAEC,MADF;AAEJE,aAAO,EAAE,CAFL,EALC;;AASNE,OAAG,EAAE;AACJL,UAAI,EAAEC,MADF;AAEJE,aAAO,EAAE,GAFL,EATC;;AAaNG,QAAI,EAAE;AACLN,UAAI,EAAEC,MADD;AAELE,aAAO,EAAE,CAFJ,EAbA;;AAiBNI,YAAQ,EAAE;AACTP,UAAI,EAAEQ,OADG;AAETL,aAAO,EAAE,KAFA,EAjBJ,EAFO;;;AAwBdM,MAxBc,kBAwBP;AACN,WAAO;AACNC,gBAAU,EAAE,CADN,EAAP;;AAGA,GA5Ba;AA6BdC,OAAK,EAAE;AACNZ,SADM,iBACAa,GADA,EACK;AACV,WAAKF,UAAL,GAAkB,CAACE,GAAnB;AACA,KAHK;AAINF,cAJM,sBAIKG,MAJL,EAIaC,MAJb,EAIqB;AAC1B,UAAI,CAACD,MAAD,KAAY,CAACC,MAAjB,EAAyB;AACxB,aAAKC,KAAL,CAAW,QAAX,EAAqBF,MAArB;AACA;AACD,KARK,EA7BO;;AAuCdG,SAvCc,qBAuCJ;AACT,SAAKN,UAAL,GAAkB,CAAC,KAAKX,KAAxB;AACA,GAzCa;AA0CdkB,SAAO,EAAE;AACRC,cADQ,sBACGlB,IADH,EACS;AAChB,UAAI,KAAKO,QAAT,EAAmB;AAClB;AACA;AACD,UAAMY,KAAK,GAAG,KAAKC,gBAAL,EAAd;AACA,UAAIrB,KAAK,GAAG,KAAKW,UAAL,GAAkBS,KAA9B;AACA,UAAIb,IAAI,GAAG,KAAKA,IAAL,GAAYa,KAAvB;AACA,UAAInB,IAAI,KAAK,OAAb,EAAsB;AACrBD,aAAK,IAAIO,IAAT;AACA,OAFD,MAEO,IAAIN,IAAI,KAAK,MAAb,EAAqB;AAC3BD,aAAK,IAAIO,IAAT;AACA;AACD,UAAIP,KAAK,GAAG,KAAKK,GAAb,IAAoBL,KAAK,GAAG,KAAKM,GAArC,EAA0C;AACzC;AACA;AACD,WAAKK,UAAL,GAAkBX,KAAK,GAAGoB,KAA1B;AACA,KAjBO;AAkBRC,oBAlBQ,8BAkBW;AAClB,UAAID,KAAK,GAAG,CAAZ;AACA;AACA,UAAI,CAAC,CAAC,KAAKb,IAAP,KAAgB,KAAKA,IAAzB,EAA+B;AAC9Ba,aAAK,GAAGE,IAAI,CAACC,GAAL,CAAS,EAAT,EAAa,CAAC,KAAKhB,IAAL,GAAY,EAAb,EAAiBiB,KAAjB,CAAuB,GAAvB,EAA4B,CAA5B,EAA+BC,MAA5C,CAAR;AACA;AACD,aAAOL,KAAP;AACA,KAzBO;AA0BRM,WA1BQ,mBA0BAC,KA1BA,EA0BO;AACd,UAAI3B,KAAK,GAAG2B,KAAK,CAACC,MAAN,CAAa5B,KAAzB;AACA,UAAI,CAACA,KAAL,EAAY;AACX,aAAKW,UAAL,GAAkB,CAAlB;AACA;AACA;AACDX,WAAK,GAAG,CAACA,KAAT;AACA,UAAIA,KAAK,GAAG,KAAKM,GAAjB,EAAsB;AACrBN,aAAK,GAAG,KAAKM,GAAb;AACA,OAFD,MAEO,IAAIN,KAAK,GAAG,KAAKK,GAAjB,EAAsB;AAC5BL,aAAK,GAAG,KAAKK,GAAb;AACA;AACD,WAAKM,UAAL,GAAkBX,KAAlB;AACA,KAvCO,EA1CK,E;;;;;;;;;;;;ACRf;AAAA;AAAA;AAAA;AAA0xC,CAAgB,osCAAG,EAAC,C;;;;;;;;;;;ACA9yC,uC", "file": "components/uni-number-box/uni-number-box.js", "sourcesContent": ["import { render, staticRenderFns } from \"./uni-number-box.vue?vue&type=template&id=63f133c8&\"\nimport script from \"./uni-number-box.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-number-box.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-number-box.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\作业\\\\大二末\\\\Spring\\\\HBuilderX.1.9.9.20190522.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!module.hot.data) {\n      api.createRecord('63f133c8', component.options)\n    } else {\n      api.reload('63f133c8', component.options)\n    }\n    module.hot.accept(\"./uni-number-box.vue?vue&type=template&id=63f133c8&\", function () {\n      api.rerender('63f133c8', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"uni-app/Ordering/components/uni-number-box/uni-number-box.vue\"\nexport default component.exports", "export * from \"-!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-hbuilderx/packages/webpack-uni-nvue-loader/lib/templateLoader.js??vue-loader-options!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/vue-loader/lib/index.js??vue-loader-options!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-custom-block-loader/index.js??ref--0-1!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-number-box.vue?vue&type=template&id=63f133c8&\"", "var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "import mod from \"-!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/vue-loader/lib/index.js??vue-loader-options!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-custom-block-loader/index.js??ref--0-1!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-number-box.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--12-1!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/vue-loader/lib/index.js??vue-loader-options!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-custom-block-loader/index.js??ref--0-1!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-number-box.vue?vue&type=script&lang=js&\"", "//\n//\n//\n//\n//\n//\n//\n\nexport default {\n\tname: 'UniNumberBox',\n\tprops: {\n\t\tvalue: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: 1\n\t\t},\n\t\tmin: {\n\t\t\ttype: Number,\n\t\t\tdefault: 0\n\t\t},\n\t\tmax: {\n\t\t\ttype: Number,\n\t\t\tdefault: 100\n\t\t},\n\t\tstep: {\n\t\t\ttype: Number,\n\t\t\tdefault: 1\n\t\t},\n\t\tdisabled: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t}\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tinputValue: 0\n\t\t}\n\t},\n\twatch: {\n\t\tvalue(val) {\n\t\t\tthis.inputValue = +val\n\t\t},\n\t\tinputValue(newVal, oldVal) {\n\t\t\tif (+newVal !== +oldVal) {\n\t\t\t\tthis.$emit('change', newVal)\n\t\t\t}\n\t\t}\n\t},\n\tcreated() {\n\t\tthis.inputValue = +this.value\n\t},\n\tmethods: {\n\t\t_calcValue(type) {\n\t\t\tif (this.disabled) {\n\t\t\t\treturn\n\t\t\t}\n\t\t\tconst scale = this._getDecimalScale()\n\t\t\tlet value = this.inputValue * scale\n\t\t\tlet step = this.step * scale\n\t\t\tif (type === 'minus') {\n\t\t\t\tvalue -= step\n\t\t\t} else if (type === 'plus') {\n\t\t\t\tvalue += step\n\t\t\t}\n\t\t\tif (value < this.min || value > this.max) {\n\t\t\t\treturn\n\t\t\t}\n\t\t\tthis.inputValue = value / scale\n\t\t},\n\t\t_getDecimalScale() {\n\t\t\tlet scale = 1\n\t\t\t// 浮点型\n\t\t\tif (~~this.step !== this.step) {\n\t\t\t\tscale = Math.pow(10, (this.step + '').split('.')[1].length)\n\t\t\t}\n\t\t\treturn scale\n\t\t},\n\t\t_onBlur(event) {\n\t\t\tlet value = event.detail.value\n\t\t\tif (!value) {\n\t\t\t\tthis.inputValue = 0\n\t\t\t\treturn\n\t\t\t}\n\t\t\tvalue = +value\n\t\t\tif (value > this.max) {\n\t\t\t\tvalue = this.max\n\t\t\t} else if (value < this.min) {\n\t\t\t\tvalue = this.min\n\t\t\t}\n\t\t\tthis.inputValue = value\n\t\t}\n\t}\n}\n", "import mod from \"-!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-1!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/index.js??ref--6-oneOf-1-2!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/vue-loader/lib/index.js??vue-loader-options!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-custom-block-loader/index.js??ref--0-1!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-number-box.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-1!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/index.js??ref--6-oneOf-1-2!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/vue-loader/lib/index.js??vue-loader-options!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-custom-block-loader/index.js??ref--0-1!../../../../作业/大二末/Spring/HBuilderX.1.9.9.20190522.full/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-number-box.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin"], "sourceRoot": ""}