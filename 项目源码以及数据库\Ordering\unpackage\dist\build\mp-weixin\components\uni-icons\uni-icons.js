(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/uni-icons/uni-icons"],{"0f84":function(n,t,e){},"24dc":function(n,t,e){"use strict";var u=e("0f84"),c=e.n(u);c.a},"28ca":function(n,t,e){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var u={name:"UniIcons",props:{type:{type:String,default:""},color:{type:String,default:"#333333"},size:{type:[Number,String],default:16}},methods:{_onClick:function(){this.$emit("click")}}};t.default=u},"2f37":function(n,t,e){"use strict";var u=function(){var n=this,t=n.$createElement;n._self._c},c=[];e.d(t,"a",function(){return u}),e.d(t,"b",function(){return c})},"546a":function(n,t,e){"use strict";e.r(t);var u=e("28ca"),c=e.n(u);for(var i in u)"default"!==i&&function(n){e.d(t,n,function(){return u[n]})}(i);t["default"]=c.a},c349:function(n,t,e){"use strict";e.r(t);var u=e("2f37"),c=e("546a");for(var i in c)"default"!==i&&function(n){e.d(t,n,function(){return c[n]})}(i);e("24dc");var r=e("2877"),a=Object(r["a"])(c["default"],u["a"],u["b"],!1,null,null,null);t["default"]=a.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/uni-icons/uni-icons-create-component',
    {
        'components/uni-icons/uni-icons-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('543d')['createComponent'](__webpack_require__("c349"))
        })
    },
    [['components/uni-icons/uni-icons-create-component']]
]);                
