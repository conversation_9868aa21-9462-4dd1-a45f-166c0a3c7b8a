(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/test/test"],{2267:function(t,n,e){"use strict";(function(t){e("07a9"),e("921b");u(e("66fd"));var n=u(e("6418"));function u(t){return t&&t.__esModule?t:{default:t}}t(n.default)}).call(this,e("543d")["createPage"])},"346b":function(t,n,e){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e={data:function(){return{}},onLoad:function(){},methods:{startSoterAuthenticationFingerPrint:function(){t.startSoterAuthentication({requestAuthModes:["fingerPrint"],challenge:"123456",success:function(t){console.log(t)},fail:function(t){console.log(t)},complete:function(t){console.log(t)}})}}};n.default=e}).call(this,e("543d")["default"])},"4ae9":function(t,n,e){"use strict";var u=function(){var t=this,n=t.$createElement;t._self._c},o=[];e.d(n,"a",function(){return u}),e.d(n,"b",function(){return o})},6418:function(t,n,e){"use strict";e.r(n);var u=e("4ae9"),o=e("bc74");for(var c in o)"default"!==c&&function(t){e.d(n,t,function(){return o[t]})}(c);e("8ef7");var a=e("2877"),r=Object(a["a"])(o["default"],u["a"],u["b"],!1,null,null,null);n["default"]=r.exports},"8ef7":function(t,n,e){"use strict";var u=e("c850"),o=e.n(u);o.a},bc74:function(t,n,e){"use strict";e.r(n);var u=e("346b"),o=e.n(u);for(var c in u)"default"!==c&&function(t){e.d(n,t,function(){return u[t]})}(c);n["default"]=o.a},c850:function(t,n,e){}},[["2267","common/runtime","common/vendor"]]]);