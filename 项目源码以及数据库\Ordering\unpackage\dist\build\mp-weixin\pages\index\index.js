(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/index/index"],{"140e":function(t,n,e){"use strict";e.r(n);var i=e("a536"),o=e.n(i);for(var u in i)"default"!==u&&function(t){e.d(n,t,function(){return i[t]})}(u);n["default"]=o.a},2470:function(t,n,e){},"2c7c":function(t,n,e){"use strict";var i=function(){var t=this,n=t.$createElement;t._self._c},o=[];e.d(n,"a",function(){return i}),e.d(n,"b",function(){return o})},4476:function(t,n,e){"use strict";var i=e("2470"),o=e.n(i);o.a},"90f0":function(t,n,e){"use strict";(function(t){e("07a9"),e("921b");i(e("66fd"));var n=i(e("d8be"));function i(t){return t&&t.__esModule?t:{default:t}}t(n.default)}).call(this,e("543d")["createPage"])},a536:function(t,n,e){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var i,o=function(){return e.e("components/uni-drawer/uni-drawer").then(e.bind(null,"2b6f"))},u=function(){return e.e("components/uni-list-item/uni-list-item").then(e.bind(null,"e0de"))},s=function(){return e.e("components/uni-swiper-dot/uni-swiper-dot").then(e.bind(null,"1e44"))},r=function(){return e.e("components/yp-number-box/yp-number-box").then(e.bind(null,"41d4"))},c=function(){return e.e("components/yp-number-box/yp-number-box1").then(e.bind(null,"f3f8"))},a=function(){return e.e("components/uni-popup/uni-popup").then(e.bind(null,"b201"))},p=function(){return e.e("components/uni-goods-nav/uni-goods-nav").then(e.bind(null,"3437"))},h={data:function(){return{orderDate:"",desk:!1,deskNumber:0,goods_nav_style:"margin-top:405px;",price:0,http:"",menu:[],selectMenus:[],showLeft:!1,cartStatus:!0,types:[{t_name:"全部",t_id:0}],info:[{colorClass:"uni-bg-red",url:"http://pic.qjimage.com/chineseview116/high/482-10992.jpg",content:"猪脚",cash:"89.00"},{colorClass:"uni-bg-green",url:"http://images.quanjing.com/chineseview116/high/482-10610.jpg",content:"口水鸡",cash:"88.80"},{colorClass:"uni-bg-blue",url:"http://img1.juimg.com/161225/335318-161225154Z323.jpg",content:"山药银杏果份",cash:"35.80"}],dotStyle:[{backgroundColor:"rgba(0, 0, 0, .3)",border:"1px rgba(0, 0, 0, .3) solid",color:"#fff",selectedBackgroundColor:"rgba(0, 0, 0, .9)",selectedBorder:"1px rgba(0, 0, 0, .9) solid"}],current:0,mode:"nav",options:[{icon:"https://img-cdn-qiniu.dcloud.net.cn/uniapp/uni-ui/goodsnav/kefu.png",text:"客服"},{icon:"https://img-cdn-qiniu.dcloud.net.cn/uniapp/uni-ui/goodsnav/carts.png",text:"购物车",info:0},{icon:"/static/qian_1.png",text:"0"}],buttonGroup:[{text:"立即购买",backgroundColor:"#ffa200",color:"#fff"}]}},components:{uniDrawer:o,uniListItem:u,uniSwiperDot:s,ypNumberBox:r,ypNumberBox1:c,uniPopup:a,uniGoodsNav:p},onLoad:function(){var n=this;i=this,i.http=getApp().globalData.http,t.request({url:this.http+"zkq/types",success:function(t){n.types=n.types.concat(t.data)}}),t.request({url:this.http+"zkq/menus",success:function(t){var e=t.data;for(var i in e)e[i].count=0;n.menu=e}}),t.getSystemInfo({success:function(t){var n=Number(t.windowHeight)-Number(148);i.goods_nav_style="margin-top:"+n+"px;"}})},methods:{submitOrder:function(){var n=new Array,e=new Array,i=this.selectMenus;for(var o in i)e.push(i[o].m_id),n.push(i[o].count);t.redirectTo({url:"../pay/pay?orderDate="+this.orderDate+"&deskNumber="+this.deskNumber+"&id="+e+"&count="+n+"&price="+this.price})},show:function(t){"left"===t&&(this.showLeft=!0)},hide:function(){this.showLeft=!1},closeDrawer:function(t){"left"===t&&(this.showLeft=!1)},confirm:function(){},change:function(t){this.current=t.detail.current},chaxun:function(n){var e=this;t.request({url:this.http+"zkq/menus",data:{t_id:n.t_id},success:function(t){var n=t.data;for(var i in n)n[i].count=0;e.menu=n}}),this.hide()},Modified:function(t,n){var e=t.count;"-"===n?e--:e++;var i=this.menu;for(var o in i)i[o].m_id===t.m_id&&(i[o].count=e)},bindChange:function(t){var n=t[0],e=t[1],i=this.menu[n];if(!(this.selectMenus.length>0))return i.count=e,this.selectMenus.push(i),this.zongjia();var o=0;for(var u in this.selectMenus){if(o++,this.selectMenus[u].m_id==i.m_id)return 0==e?(this.selectMenus.splice(u,1),this.zongjia()):(this.selectMenus[u].count=e,this.zongjia());if(this.selectMenus.length==o)return i.count=e,this.selectMenus.push(i),this.zongjia()}},zongjia:function(){this.price=0;var n=0,e=0;for(var i in this.selectMenus)e+=Number(this.selectMenus[i].count),n=this.selectMenus[i].m_price*this.selectMenus[i].count,this.price+=n;this.options[1].info=e,this.options[2].text=this.price,0==this.price?(this.$refs.popup.close(),this.$refs.openGroup.close(),t.showTabBar({animation:!0})):(t.hideTabBar({animation:!0}),this.$refs.openGroup.open())},ondesk:function(n){t.scanCode({success:function(t){i.deskNumber=Number(t.result),i.desk=!0}})},cart:function(t){this.$refs.popup1.close(),1==t.index&&this.cartStatus?(this.$refs.popup.open(),this.cartStatus=!1):(this.cartStatus=!0,this.$refs.popup.close())},buy:function(n){var e=new Date,o=e.getFullYear(),u=e.getMonth()+1,s=e.getDate(),r=e.getHours(),c=e.getMinutes(),a=e.getSeconds();this.orderDate=o+"-"+u+"-"+s+" "+r+":"+c+":"+a,""==i.deskNumber||i.deskNumber<=0?t.showToast({title:"你还没有扫描桌号！",duration:2e3,icon:"none"}):(this.$refs.popup.close(),this.$refs.popup1.open())}},onNavigationBarButtonTap:function(t){},onBackPress:function(){if(this.showLeft)return this.hide(),!0},watch:{}};n.default=h}).call(this,e("543d")["default"])},d8be:function(t,n,e){"use strict";e.r(n);var i=e("2c7c"),o=e("140e");for(var u in o)"default"!==u&&function(t){e.d(n,t,function(){return o[t]})}(u);e("4476");var s=e("2877"),r=Object(s["a"])(o["default"],i["a"],i["b"],!1,null,null,null);n["default"]=r.exports}},[["90f0","common/runtime","common/vendor"]]]);