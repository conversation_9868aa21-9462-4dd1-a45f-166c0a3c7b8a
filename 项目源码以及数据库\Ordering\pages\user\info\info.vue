<template>
  <view>
    <!-- <cmd-nav-bar back title="信息设置"></cmd-nav-bar> -->
    <cmd-page-body type="top">
      <cmd-transition name="fade-up">
        <view>
          <cmd-cel-item title="头像" slot-right arrow>
            <cmd-avatar :src="img"></cmd-avatar>
          </cmd-cel-item>
          <cmd-cel-item title="昵称" :addon="name" arrow></cmd-cel-item>
          <button class="btn-logout" @click="exitLogin">退出登录</button>
        </view>
      </cmd-transition>
    </cmd-page-body>
  </view>
</template>

<script>
  import cmdNavBar from "@/components/cmd-nav-bar/cmd-nav-bar.vue"
  import cmdPageBody from "@/components/cmd-page-body/cmd-page-body.vue"
  import cmdTransition from "@/components/cmd-transition/cmd-transition.vue"
  import cmdCelItem from "@/components/cmd-cell-item/cmd-cell-item.vue"
  import cmdAvatar from "@/components/cmd-avatar/cmd-avatar.vue"

	let _self;
  export default {
    components: {
      cmdNavBar,
      cmdPageBody,
      cmdTransition,
      cmdCelItem,
      cmdAvatar
    },
	onLoad(){
		_self = this;
		_self.img = getApp().globalData.userImg;
		_self.name = getApp().globalData.userName;
	},
    data() {
      return {
		  name:'',
		  img:''
	  };
    },

    mounted() {},
    
    methods:{
     exitLogin(){
		 uni.reLaunch({
		     url: '/pages/login/login'
		 });
	 }
    }
  }
</script>

<style>
  .btn-logout {
    margin-top: 100upx;
    width: 80%;
    border-radius: 50upx;
    font-size: 16px;
    color: #fff;
    background: linear-gradient(to right, #365fff, #36bbff);
  }

  .btn-logout-hover {
    background: linear-gradient(to right, #365fdd, #36bbfa);
  }
</style>
