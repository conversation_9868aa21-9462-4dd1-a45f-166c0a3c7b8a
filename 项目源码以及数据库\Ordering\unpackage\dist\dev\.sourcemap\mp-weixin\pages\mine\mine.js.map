{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/mine/mine.vue?39e5", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/mine/mine.vue?4b58", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/mine/mine.vue?b904", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/mine/mine.vue?4e13", "uni-app:///pages/mine/mine.vue", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/mine/mine.vue?d820", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/pages/mine/mine.vue?9b43"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "cmdAvatar", "cmdCellItem", "cmdIcon", "data", "userImg", "userName", "userID", "methods", "fnInfoWin", "uni", "url", "order", "onLoad", "_sef"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AAC+K;AAC/K,gBAAgB,wLAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oMAEN;AACP,KAAK;AACL;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtDA;AAAA;AAAA;AAAA;AAAgpB,CAAgB,qqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8BpqB;AAAA,eACA;EACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;AACA;AACA;IACAC;MACAC;QACAC;MACA;IACA;IACAC;MACAF;QACAC;MACA;IACA;EACA;EACAE;IACAC;IACAA;IACAA;IACAA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AClEA;AAAA;AAAA;AAAA;AAAs7B,CAAgB,g7BAAG,EAAC,C;;;;;;;;;;;ACA18B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/mine.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/mine.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./mine.vue?vue&type=template&id=dcbcfe34&\"\nvar renderjs\nimport script from \"./mine.vue?vue&type=script&lang=js&\"\nexport * from \"./mine.vue?vue&type=script&lang=js&\"\nimport style0 from \"./mine.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/mine.vue\"\nexport default component.exports", "export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mine.vue?vue&type=template&id=dcbcfe34&\"", "var components\ntry {\n  components = {\n    cmdAvatar: function () {\n      return import(\n        /* webpackChunkName: \"components/cmd-avatar/cmd-avatar\" */ \"@/components/cmd-avatar/cmd-avatar.vue\"\n      )\n    },\n    cmdCellItem: function () {\n      return import(\n        /* webpackChunkName: \"components/cmd-cell-item/cmd-cell-item\" */ \"@/components/cmd-cell-item/cmd-cell-item.vue\"\n      )\n    },\n    cmdIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/cmd-icon/cmd-icon\" */ \"@/components/cmd-icon/cmd-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var a0 = {\n    \"background-color\": \"#fff\",\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        a0: a0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mine.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mine.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"html\">\r\n    <view class=\"person-head\">\r\n      <cmd-avatar :src=\"userImg\" @click=\"fnInfoWin\" size=\"lg\" :make=\"{'background-color': '#fff'}\"></cmd-avatar>\r\n      <view class=\"person-head-box\">\r\n        <view class=\"person-head-nickname\">{{userName}}</view>\r\n        <!-- <view class=\"person-head-username\">ID：slimmer9501</view> -->\r\n      </view>\r\n    </view>\r\n    <view class=\"person-list\">\r\n      <cmd-cell-item title=\"我的订单\" slot-left arrow @click=\"order\">\r\n        <cmd-icon type=\"bullet-list\" size=\"24\" color=\"#368dff\"></cmd-icon>\r\n      </cmd-cell-item>\r\n      <!-- <cmd-cell-item title=\"店铺评论\" slot-left arrow>\r\n        <cmd-icon type=\"message\" size=\"24\" color=\"#368dff\"></cmd-icon>\r\n      </cmd-cell-item> -->\r\n      <!-- <cmd-cell-item title=\"系统设置\" slot-left arrow>\r\n        <cmd-icon type=\"settings\" size=\"24\" color=\"#368dff\"></cmd-icon>\r\n      </cmd-cell-item>\r\n      <cmd-cell-item title=\"检查版本\" addon=\"v1.0\" slot-left arrow>\r\n        <cmd-icon type=\"alert-circle\" size=\"24\" color=\"#368dff\"></cmd-icon>\r\n      </cmd-cell-item> -->\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import cmdAvatar from \"@/components/cmd-avatar/cmd-avatar.vue\"\r\n  import cmdIcon from \"@/components/cmd-icon/cmd-icon.vue\"\r\n  import cmdCellItem from \"@/components/cmd-cell-item/cmd-cell-item.vue\"\r\n\tvar _sef;\r\n  export default {\r\n    components: {\r\n      cmdAvatar,\r\n      cmdCellItem,\r\n      cmdIcon\r\n    },\r\n    data() {\r\n      return {\r\n\t\t  userImg : '',\r\n\t\t  userName : '',\r\n\t\t  userID : 0,\r\n\t  };\r\n    },\r\n    methods: {\r\n      /**\r\n       * 打开用户信息页\r\n       */\r\n      fnInfoWin() {\r\n        uni.navigateTo({\r\n          url: '/pages/user/info/info'\r\n        })\r\n      },\r\n\t  order(){\r\n\t\t  uni.navigateTo({\r\n\t\t    url: '/pages/order/order'\r\n\t\t  })\r\n\t\t}\r\n    },\r\n\tonLoad(){\r\n\t\t_sef = this;\r\n\t\t_sef.userImg = getApp().globalData.userImg\r\n\t\t_sef.userName = getApp().globalData.userName\r\n\t\t_sef.userID = getApp().globalData.u_id\r\n\t\r\n\t}\r\n  }\r\n</script>\r\n\r\n<style>\r\n  .person-head {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    height: 150px;\r\n    padding-left: 20px;/* \r\n    background: linear-gradient(to right, #365fff, #36bbff); */\r\n\tbackground-image: url(https://ss2.bdstatic.com/70cFvnSh_Q1YnxGkpoWK1HF6hhy/it/u=3912358549,1727259047&fm=26&gp=0.jpg);\r\n\tbackground-size:100% 100%;\r\n\tbackground-repeat:no-repeat;\r\n\t\r\n  }\r\n\r\n  .person-head-box {\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-items: flex-start;\r\n    margin-left: 10px;\r\n  }\r\n\r\n  .person-head-nickname {\r\n    font-size: 18px;\r\n    font-weight: 500;\r\n    color: #fff;\r\n  }\r\n\r\n  .person-head-username {\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n    color: #fff;\r\n  }\r\n\r\n  .person-list {\r\n    line-height: 0;\r\n\t//padding-top: ;\r\n\tmargin-top: 50px;\r\n  }\r\n  /* .html{\r\n\t  background-image: url(https://ss1.bdstatic.com/70cFuXSh_Q1YnxGkpoWK1HF6hhy/it/u=3039487882,4099011575&fm=26&gp=0.jpg);\r\n\t  background-size: ;\r\n  } */\r\n</style>\n", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mine.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mine.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1748246851979\n      var cssReload = require(\"E:/software/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}