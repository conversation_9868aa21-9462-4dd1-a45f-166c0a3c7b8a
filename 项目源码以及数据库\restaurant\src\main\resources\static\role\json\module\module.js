{"code": 200, "message": "success", "data": [{"id": "001", "title": "基础属性", "parentId": "0", "href": ""}, {"id": "001001", "title": "基础参数", "parentId": "001"}, {"id": "001001001", "title": "elem", "parentId": "001001"}, {"id": "001001002", "title": "obj", "parentId": "001001"}, {"id": "001001003", "title": "accordion", "parentId": "001001"}, {"id": "001001004", "title": "initLevel", "parentId": "001001"}, {"id": "001001005", "title": "type", "parentId": "001001"}, {"id": "001001006", "title": "cache", "parentId": "001001"}, {"id": "001001007", "title": "record", "parentId": "001001"}, {"id": "001001008", "title": "load", "parentId": "001001"}, {"id": "001001009", "title": "none", "parentId": "001001"}, {"id": "001002", "title": "样式参数", "parentId": "001"}, {"id": "001002001", "title": "line", "parentId": "001002"}, {"id": "001002002", "title": "skin", "parentId": "001002"}, {"id": "001002003", "title": "ficon", "parentId": "001002"}, {"id": "001002004", "title": "icon", "parentId": "001002"}, {"id": "001002005", "title": "firstIconArray", "parentId": "001002"}, {"id": "001002006", "title": "nodeIconArray", "parentId": "001002"}, {"id": "001002007", "title": "leafIconArray", "parentId": "001002"}, {"id": "001002008", "title": "iconfont", "parentId": "001002"}, {"id": "001002009", "title": "iconfontStyle", "parentId": "001002"}, {"id": "002", "title": "异步属性", "parentId": "0"}, {"id": "002001", "title": "url", "parentId": "002"}, {"id": "002002", "title": "async", "parentId": "002"}, {"id": "002003", "title": "headers", "parentId": "002"}, {"id": "002004", "title": "method", "parentId": "002"}, {"id": "002005", "title": "dataType", "parentId": "002"}, {"id": "002006", "title": "contentType", "parentId": "002"}, {"id": "002007", "title": "defaultRequest", "parentId": "002"}, {"id": "002008", "title": "request", "parentId": "002"}, {"id": "002009", "title": "filterRequest", "parentId": "002"}, {"id": "002010", "title": "response", "parentId": "002"}, {"id": "002011", "title": "success", "parentId": "002"}, {"id": "002012", "title": "done", "parentId": "002"}, {"id": "002013", "title": "dataStyle", "parentId": "002"}, {"id": "002014", "title": "dataFormat", "parentId": "002"}, {"id": "002015", "title": "errDataShow", "parentId": "002"}, {"id": "002016", "title": "formatter", "parentId": "002"}, {"id": "002017", "title": "error", "parentId": "002"}, {"id": "002018", "title": "complete", "parentId": "002"}, {"id": "003", "title": "扩展属性", "parentId": "0"}, {"id": "003001", "title": "复选框属性", "parentId": "003"}, {"id": "003001001", "title": "checkbar", "parentId": "003001"}, {"id": "003001002", "title": "checkbarLoad", "parentId": "003001"}, {"id": "003001003", "title": "checkbarType", "parentId": "003001"}, {"id": "003001004", "title": "checkbarData", "parentId": "003001"}, {"id": "003001005", "title": "checkbarFun", "parentId": "003001"}, {"id": "003002", "title": "菜单栏属性", "parentId": "003"}, {"id": "003002001", "title": "menubar", "parentId": "003002"}, {"id": "003002002", "title": "menubarTips", "parentId": "003002"}, {"id": "003002003", "title": "menubarFun", "parentId": "003002"}, {"id": "003003", "title": "工具栏属性", "parentId": "003"}, {"id": "003003001", "title": "toolbar", "parentId": "003003"}, {"id": "003003002", "title": "toolbarWay", "parentId": "003003"}, {"id": "003003003", "title": "scroll", "parentId": "003003"}, {"id": "003003004", "title": "toolbarLoad", "parentId": "003003"}, {"id": "003003005", "title": "toolbarShow", "parentId": "003003"}, {"id": "003003006", "title": "toolbarExt", "parentId": "003003"}, {"id": "003003007", "title": "toolbarBtn", "parentId": "003003"}, {"id": "003003008", "title": "toolbarFun", "parentId": "003003"}, {"id": "003003009", "title": "toolbarStyle", "parentId": "003003"}, {"id": "003004", "title": "iframe属性", "parentId": "003"}, {"id": "003004001", "title": "useIframe", "parentId": "003004"}, {"id": "003004002", "title": "iframe", "parentId": "003004"}, {"id": "003004003", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parentId": "003004"}, {"id": "004", "title": "基础方法", "parentId": "0"}, {"id": "004001", "title": "render", "parentId": "004"}, {"id": "004002", "title": "reload", "parentId": "004"}, {"id": "004003", "title": "click", "parentId": "004"}, {"id": "004004", "title": "getNowParam", "parentId": "004"}, {"id": "004005", "title": "getPara<PERSON>", "parentId": "004"}, {"id": "004006", "title": "getParentParam", "parentId": "004"}, {"id": "004007", "title": "getAllParentParam", "parentId": "004"}, {"id": "004008", "title": "get<PERSON><PERSON>dP<PERSON><PERSON>", "parentId": "004"}, {"id": "004009", "title": "changeCheckbarNodes", "parentId": "004"}, {"id": "004010", "title": "dataInit", "parentId": "004"}, {"id": "004011", "title": "chooseDataInit", "parentId": "004"}, {"id": "004012", "title": "getCheckbarNodesParam", "parentId": "004"}, {"id": "004013", "title": "initNoAllCheck", "parentId": "004"}, {"id": "004014", "title": "initAllCheck", "parentId": "004"}, {"id": "004015", "title": "escape", "parentId": "004"}, {"id": "004016", "title": "unescape", "parentId": "004"}, {"id": "004017", "title": "version", "parentId": "004"}, {"id": "005", "title": "事件监听", "parentId": "0"}, {"id": "005001", "title": "changeTree", "parentId": "005"}, {"id": "005002", "title": "node", "parentId": "005"}, {"id": "005003", "title": "nodedblclick", "parentId": "005"}, {"id": "005004", "title": "chooseDone", "parentId": "005"}, {"id": "005005", "title": "iframeDone", "parentId": "005"}, {"id": "006", "title": "内置方法", "parentId": "0"}, {"id": "006001", "title": "escape", "parentId": "006"}, {"id": "006002", "title": "unescape", "parentId": "006"}, {"id": "006003", "title": "<PERSON><PERSON><PERSON><PERSON>", "parentId": "006"}, {"id": "006004", "title": "dataInit", "parentId": "006"}, {"id": "006005", "title": "navThis", "parentId": "006"}, {"id": "006006", "title": "setDisabledNodes", "parentId": "006"}, {"id": "006007", "title": "cancelDisabledNodes", "parentId": "006"}, {"id": "006008", "title": "getDisabledNodesParam", "parentId": "006"}, {"id": "006009", "title": "getAllDisabledNodesParam", "parentId": "006"}, {"id": "006010", "title": "setHideNodes", "parentId": "006"}, {"id": "006011", "title": "cancelHideNodes", "parentId": "006"}, {"id": "006012", "title": "getHideNodesParam", "parentId": "006"}, {"id": "006013", "title": "getAllHideNodesParam", "parentId": "006"}, {"id": "006014", "title": "refreshTree", "parentId": "006"}, {"id": "006015", "title": "partialRefreshAdd", "parentId": "006"}, {"id": "006016", "title": "partialRefreshEdit", "parentId": "006"}, {"id": "006017", "title": "partialRefreshDel", "parentId": "006"}, {"id": "006018", "title": "chooseDataInit", "parentId": "006"}, {"id": "006019", "title": "changeCheck", "parentId": "006"}, {"id": "006020", "title": "initNoAllCheck", "parentId": "006"}, {"id": "006021", "title": "initAllCheck", "parentId": "006"}, {"id": "006022", "title": "checkStatus", "parentId": "006"}, {"id": "006023", "title": "getCheckbarNodesParam", "parentId": "006"}, {"id": "006024", "title": "changeCheckbarNodes", "parentId": "006"}, {"id": "006025", "title": "menubarMethod", "parentId": "006"}, {"id": "006026", "title": "searchNode", "parentId": "006"}, {"id": "006027", "title": "toolbarMethod", "parentId": "006"}, {"id": "006028", "title": "changeTreeNodeAdd", "parentId": "006"}, {"id": "006029", "title": "changeTreeNodeEdit", "parentId": "006"}, {"id": "006030", "title": "changeTreeNodeDel", "parentId": "006"}, {"id": "006031", "title": "changeTreeNodeDone", "parentId": "006"}, {"id": "006032", "title": "getNodeDom", "parentId": "006"}, {"id": "006033", "title": "getNowNodeUl", "parentId": "006"}, {"id": "006034", "title": "getNowNode", "parentId": "006"}, {"id": "006035", "title": "getNode", "parentId": "006"}, {"id": "006036", "title": "getNowNodeOrNull", "parentId": "006"}, {"id": "006037", "title": "getNowParam", "parentId": "006"}, {"id": "006038", "title": "getPara<PERSON>", "parentId": "006"}, {"id": "006039", "title": "getParentParam", "parentId": "006"}, {"id": "006040", "title": "getAllParentParam", "parentId": "006"}, {"id": "006041", "title": "get<PERSON><PERSON>dP<PERSON><PERSON>", "parentId": "006"}]}