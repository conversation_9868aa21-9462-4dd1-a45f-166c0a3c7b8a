@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.zong {
  height: 100%;
  display: flex;
  align-items: center;
}
.title {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  background-color: #ffffff;
  z-index: 99;
  line-height: 90rpx;
  text-align: center;
  font-weight: bold;
  box-shadow: 0 0 20rpx -5rpx rgba(0, 0, 0, 0.1);
}
.cells {
  background-color: #fff;
  margin-top: 20rpx;
}
.cell {
  display: flex;
  padding: 20rpx;
}
.cell:not(:last-child) {
  border-bottom: 1rpx solid #ebedf0;
}
.cell__hd {
  font-size: 0;
}
.cell__hd image {
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
  border-radius: 12rpx;
}
.cell__bd {
  flex: 1;
}
.cell__bd .name {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  word-break: break-all;
  font-size: 28rpx;
  margin-bottom: 12rpx;
}
.cell__bd .des {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  word-break: break-all;
  color: #666666;
  font-size: 24rpx;
}
.control-panel {
  border-top: 1rpx solid #ebedf0;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 12213;
  background: #ffffff;
  padding: 20rpx;
  box-sizing: border-box;
}
.control-panel.isX {
  padding-bottom: 60rpx;
}
.panel-item {
  display: flex;
  align-items: center;
}
.panel-item__bd {
  flex: 1;
}
