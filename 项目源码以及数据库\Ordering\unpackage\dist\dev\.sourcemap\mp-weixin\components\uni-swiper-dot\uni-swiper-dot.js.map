{"version": 3, "sources": ["webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/uni-swiper-dot/uni-swiper-dot.vue?c133", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/uni-swiper-dot/uni-swiper-dot.vue?1762", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/uni-swiper-dot/uni-swiper-dot.vue?a249", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/uni-swiper-dot/uni-swiper-dot.vue?2804", "uni-app:///components/uni-swiper-dot/uni-swiper-dot.vue", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/uni-swiper-dot/uni-swiper-dot.vue?b94d", "webpack:///D:/JavaWeb/餐饮点餐系统/项目源码以及数据库/Ordering/components/uni-swiper-dot/uni-swiper-dot.vue?e952"], "names": ["name", "props", "info", "type", "default", "current", "dotsStyles", "mode", "field", "cash", "data", "dots", "width", "height", "bottom", "color", "backgroundColor", "border", "selectedBackgroundColor", "selectedB<PERSON>er", "watch", "created"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACkE;AACL;AACa;;;AAG1E;AAC+K;AAC/K,gBAAgB,wLAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA0pB,CAAgB,+qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC6B9qB;EACAA;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;QACA;MACA;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;EACA;EACAM;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACAd;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;EAEA;EACAc;IACA;MACA;MACA;IACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACnGA;AAAA;AAAA;AAAA;AAAg8B,CAAgB,07BAAG,EAAC,C;;;;;;;;;;;ACAp9B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/uni-swiper-dot/uni-swiper-dot.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-swiper-dot.vue?vue&type=template&id=039811b8&\"\nvar renderjs\nimport script from \"./uni-swiper-dot.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-swiper-dot.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-swiper-dot.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uni-swiper-dot/uni-swiper-dot.vue\"\nexport default component.exports", "export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-swiper-dot.vue?vue&type=template&id=039811b8&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.mode === \"nav\" ? _vm.info.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-swiper-dot.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-swiper-dot.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"uni-swiper__warp\">\r\n\t\t<slot />\r\n\t\t<view v-if=\"mode === 'default'\" :style=\"{'bottom':dots.bottom + 'px'}\" class=\"uni-swiper__dots-box\">\r\n\t\t\t<view v-for=\"(item,index) in info\" :style=\"{\r\n        'width': (index === current? dots.width*2:dots.width ) + 'px','height':dots.width/3 +'px' ,'background-color':index !== current?dots.backgroundColor:dots.selectedBackgroundColor,'border-radius':'0px'}\" :key=\"index\" class=\"uni-swiper__dots-item uni-swiper__dots-bar\" />\r\n\t\t</view>\r\n\t\t<view v-if=\"mode === 'dot'\" :style=\"{'bottom':dots.bottom + 'px'}\" class=\"uni-swiper__dots-box\">\r\n\t\t\t<view v-for=\"(item,index) in info\" :style=\"{\r\n        'width': dots.width + 'px','height':dots.height +'px' ,'background-color':index !== current?dots.backgroundColor:dots.selectedBackgroundColor,'border':index !==current ? dots.border:dots.selectedBorder}\" :key=\"index\" class=\"uni-swiper__dots-item\" />\r\n\t\t</view>\r\n\t\t<view v-if=\"mode === 'round'\" :style=\"{'bottom':dots.bottom + 'px'}\" class=\"uni-swiper__dots-box\">\r\n\t\t\t<view v-for=\"(item,index) in info\" :class=\"[index === current&&'uni-swiper__dots-long']\" :style=\"{\r\n\t\t    'width':(index === current? dots.width*3:dots.width ) + 'px','height':dots.height +'px' ,'background-color':index !== current?dots.backgroundColor:dots.selectedBackgroundColor,'border':index !==current ? dots.border:dots.selectedBorder}\" :key=\"index\" class=\"uni-swiper__dots-item \" />\r\n\t\t</view>\r\n\t\t<view v-if=\"mode === 'nav'\" :style=\"{'background-color':dotsStyles.backgroundColor}\" class=\"uni-swiper__dots-box uni-swiper__dots-nav\">\r\n\t\t\t<view :style=\"{'color':dotsStyles.color}\" class=\"uni-swiper__dots-nav-item\">{{ (current+1)+\"/\"+info.length }}\r\n\t\t\t\t{{ info[current][field] }}\r\n\t\t\t</view>\r\n\t\t\t<view style=\"width: 30%;margin-left: 30%; text-align: right;color: red;\">&yen;{{info[current][cash]}}</view>\r\n\t\t</view>\r\n\t\t<view v-if=\"mode === 'indexes'\" :style=\"{'bottom':dots.bottom + 'px'}\" class=\"uni-swiper__dots-box\">\r\n\t\t\t<view v-for=\"(item,index) in info\" :style=\"{\r\n        'width':dots.width + 'px','height':dots.height +'px' ,'color':index === current?dots.selectedColor:dots.color,'background-color':index !== current?dots.backgroundColor:dots.selectedBackgroundColor,'border':index !==current ? dots.border:dots.selectedBorder}\" :key=\"index\" class=\"uni-swiper__dots-item uni-swiper__dots-indexes\">{{ index+1 }}</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tname: 'UniSwiperDot',\r\n\t\tprops: {\r\n\t\t\tinfo: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn []\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcurrent: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\tdotsStyles: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn {}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 类型 ：default(默认) indexes long nav\r\n\t\t\tmode: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'default'\r\n\t\t\t},\r\n\t\t\t// 只在 nav 模式下生效，变量名称\r\n\t\t\tfield: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tcash: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tdots: {\r\n\t\t\t\t\twidth: 8,\r\n\t\t\t\t\theight: 8,\r\n\t\t\t\t\tbottom: 10,\r\n\t\t\t\t\tcolor: '#fff',\r\n\t\t\t\t\tbackgroundColor: 'rgba(0, 0, 0, .3)',\r\n\t\t\t\t\tborder: '1px rgba(0, 0, 0, .3) solid',\r\n\t\t\t\t\tselectedBackgroundColor: '#333',\r\n\t\t\t\t\tselectedBorder: '1px rgba(0, 0, 0, .9) solid'\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tdotsStyles(newVal) {\r\n\t\t\t\tthis.dots = Object.assign(this.dots, this.dotsStyles)\r\n\t\t\t},\r\n\t\t\tmode(newVal) {\r\n\t\t\t\tif (newVal === 'indexes') {\r\n\t\t\t\t\tthis.dots.width = 20\r\n\t\t\t\t\tthis.dots.height = 20\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.dots.width = 8\r\n\t\t\t\t\tthis.dots.height = 8\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tif (this.mode === 'indexes') {\r\n\t\t\t\tthis.dots.width = 20\r\n\t\t\t\tthis.dots.height = 20\r\n\t\t\t}\r\n\t\t\tthis.dots = Object.assign(this.dots, this.dotsStyles)\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.uni-swiper__warp {\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\tbox-sizing: border-box;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.uni-swiper__dots-box {\r\n\t\tposition: absolute;\r\n\t\tbottom: 20rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tbox-sizing: box-sizing;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.uni-swiper__dots-item {\r\n\t\tflex-shrink: 0;\r\n\t\twidth: 16rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tmargin-left: 12rpx;\r\n\t\tbackground: rgba(0, 0, 0, .3);\r\n\t\ttransition: all 0.2s linear;\r\n\t}\r\n\r\n\t.uni-swiper__dots-item:first-child {\r\n\t\tmargin: 0;\r\n\t}\r\n\r\n\t.uni-swiper__dots-default {\r\n\t\tborder-radius: 50%;\r\n\t}\r\n\r\n\t.uni-swiper__dots-long {\r\n\t\tborder-radius: 100rpx;\r\n\t}\r\n\r\n\t.uni-swiper__dots-bar {\r\n\t\tborder-radius: 100rpx;\r\n\t}\r\n\r\n\t.uni-swiper__dots-nav {\r\n\t\tbottom: 0;\r\n\t\theight: 80rpx;\r\n\t\tjustify-content: flex-start;\r\n\t\tbackground: rgba(0, 0, 0, 0.2);\r\n\t\tbox-sizing: box-sizing;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.uni-swiper__dots-nav-item {\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #fff;\r\n\t\tbox-sizing: box-sizing;\r\n\t\tmargin: 0 30rpx;\r\n\t}\r\n\r\n\t.uni-swiper__dots-indexes {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n</style>", "import mod from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-swiper-dot.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\software\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-swiper-dot.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749718152582\n      var cssReload = require(\"E:/software/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}