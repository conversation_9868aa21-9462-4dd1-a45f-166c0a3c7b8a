package com.aaa.service;

import com.aaa.config.UserCredentialsMatcher;
import com.aaa.entity.ActiveUser;
import com.aaa.entity.SysPermission;
import com.aaa.entity.SysUser;
import com.aaa.entity.SysUserExample;
import com.aaa.exception.CustomException;
import com.aaa.mapper.SysPermissionMapperCustom;
import com.aaa.mapper.SysUserMapper;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.aaa.util.*;
import javax.annotation.Resource;
import java.util.List;

@Service
@Transactional
public class SysServiceImpl implements SysService {
	
	@Resource
	private SysUserMapper sysUserMapper;
	
	@Resource
	private SysPermissionMapperCustom sysPermissionMapperCustom;

	@Override
	public ActiveUser authenticat(String userCode, String password)
			throws Exception {
		/**
	认证过程：
	根据用户身份（账号）查询数据库，如果查询不到用户不存在
	对输入的密码 和数据库密码 进行比对，如果一致，认证通过
		 */
		//根据用户账号查询数据库
		SysUser sysUser = this.findSysUserByUserCode(userCode);
		
		if(sysUser == null){
			//抛出异常
			throw new CustomException("用户账号不存在");
		}
		
		//数据库密码 (md5密码 )
		String password_db = sysUser.getPassword();
		
		//对输入的密码 和数据库密码 进行比对，如果一致，认证通过
		//使用Shiro的加盐MD5验证方式
		String salt = sysUser.getSalt();
		String password_input_md5 = UserCredentialsMatcher.encryptPassword("MD5", password, salt, 1);
		if(!password_input_md5.equalsIgnoreCase(password_db)){
			//抛出异常
			throw new CustomException("用户名或密码 错误");
		}
		//得到用户id
		String userid = sysUser.getId();
		//根据用户id查询菜单 
		List<SysPermission> menus =this.findMenuListByUserId(userid);
		
		//根据用户id查询权限url
		List<SysPermission> permissions = this.findPermissionListByUserId(userid);
		
		//认证通过，返回用户身份信息
		ActiveUser activeUser = new ActiveUser();
		activeUser.setUserid(sysUser.getId());
		activeUser.setUsercode(userCode);
		activeUser.setUsername(sysUser.getUsername());//用户名称
		
		//放入权限范围的菜单和url
		activeUser.setMenus(menus);
		activeUser.setPermissions(permissions);
		
		return activeUser;
	}
	
	//根据用户账号查询用户信息
	public SysUser findSysUserByUserCode(String userCode)throws Exception{
		SysUserExample sysUserExample = new SysUserExample();
		SysUserExample.Criteria criteria = sysUserExample.createCriteria();
		criteria.andUsercodeEqualTo(userCode);
		
		List<SysUser> list = sysUserMapper.selectByExample(sysUserExample);
		
		if(list!=null && list.size()==1){
			return list.get(0);
		}	
		
		return null;
	}

	@Override
	public SysUser findSysUserByUsername(String username) throws Exception {
		return sysUserMapper.findUserByName(username);
	}

	@Override
	public List<SysPermission> findMenuListByUserId(String userid)
			throws Exception {
		
		return sysPermissionMapperCustom.findMenuListByUserId(userid);
	}

	@Override
	public List<SysPermission> findPermissionListByUserId(String userid)
			throws Exception {
		
		return sysPermissionMapperCustom.findPermissionListByUserId(userid);
	}

}
