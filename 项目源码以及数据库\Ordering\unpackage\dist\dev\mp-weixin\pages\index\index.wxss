
.anorder{
	width: 100%;
	height: 100px;
	margin: 10px;
	font-size: 16px;
}
.anorder>view{
	width: 30%;
	float: left;
}
.desknone{
	display: none;
}
#cartPopup>view{
	margin: 10px auto;
	clear: both;
	color: #808080;
	font-size: 16px;
	height: 100px;
}
#cartPopup>view>view{
	float: left;
	width: 30%;
	margin-left: 5px;
}
#apps{
	display: flex;
}
#goods_nav_style{
	border: #00BFFF;z-index: 999;position: fixed;width: 100%;
}
#desk{
	position: fixed;
	right: 0px;
	z-index: 2;
	width: 40px;
	height: 40px;
	background-color: #DEDEDE;
	line-height: 40px;
	text-align: center;
	overflow: hidden;
	border-radius: 5px;
	opacity:0.5;
}
#but_type{
	position: fixed;
	width: 40px;
	z-index: 2;
	color: gray;
	opacity:0.5;
}
#v_type{
}
.swiper-item {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100%;
	background: #eee;
	color: #fff;
}
.swiper-item image {
	width: 100%;
	height: 100%;
}
/* 菜单 */
.menus{
	width: 100%;
	height: 100px;
	overflow: hidden;

	/* border: #000000 1px solid; */
}
.menus>view{
	float: left;
}
.butCount{
	width: 10xp;
	height: 10xp;
}
#shade{
	width: 100%;height: 100%;background-color: #00BFFF;opacity: 0.3;position: fixed;
}
.uni-tab__cart-box{
}

